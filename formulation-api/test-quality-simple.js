#!/usr/bin/env node

// Simple test of the quality assessment function
const path = require('path');

// Mock components for testing
const mockComponents = {
  mainRecipe: {
    name: 'Test Recipe',
    ingredients: [
      { name: 'Water', percentage: 80 },
      { name: 'Extract', percentage: 15 },
      { name: 'Sweetener', percentage: 5 }
    ]
  },
  variations: [
    { name: 'Variation 1' },
    { name: 'Variation 2' },
    { name: 'Variation 3' }
  ],
  nutritionalProfile: {
    macronutrients: { protein_g: 2, carbohydrates_g: 10 }
  },
  costAnalysis: {
    total_cogs: 1500,
    margin_percentage: 75
  },
  manufacturingProcedure: 'Mix and blend ingredients',
  errors: {}
};

// Import the quality assessment function
function evaluateFormulationQuality(components, validationErrors = []) {
  let score = 0;
  let issues = [];
  
  // Check main recipe completeness (40 points max)
  if (components.mainRecipe) {
    score += 20;
    
    // Check ingredients count (minimum 3 for basic formulation)
    if (components.mainRecipe.ingredients && components.mainRecipe.ingredients.length >= 3) {
      score += 10;
    } else {
      issues.push('Insufficient ingredients (minimum 3 required)');
    }
    
    // Check nutritional profile
    if (components.nutritionalProfile) {
      score += 10;
    } else {
      issues.push('Missing nutritional profile');
    }
  } else {
    issues.push('Missing main recipe');
  }
  
  // Check variations completeness (30 points max)
  if (components.variations && components.variations.length >= 2) {
    score += 20;
    if (components.variations.length >= 3) {
      score += 10; // Bonus for complete set
    }
  } else {
    issues.push('Insufficient variations (minimum 2 required)');
  }
  
  // Check cost analysis (15 points max)
  if (components.costAnalysis) {
    score += 15;
  } else {
    issues.push('Missing cost analysis');
  }
  
  // Check manufacturing procedure (15 points max)
  if (components.manufacturingProcedure) {
    score += 15;
  } else {
    issues.push('Missing manufacturing procedure');
  }
  
  // Deduct points for validation errors (max -20 points)
  const errorPenalty = Math.min(validationErrors.length * 5, 20);
  score = Math.max(0, score - errorPenalty);
  
  if (validationErrors.length > 0) {
    issues.push(`${validationErrors.length} validation errors present`);
  }
  
  // Determine quality level and recommendations
  let level, readyForResults, recommendPlayground;
  
  if (score >= 85) {
    level = 'excellent';
    readyForResults = true;
    recommendPlayground = false;
  } else if (score >= 70) {
    level = 'good';
    readyForResults = true;
    recommendPlayground = false;
  } else if (score >= 50) {
    level = 'acceptable';
    readyForResults = false;
    recommendPlayground = true;
  } else {
    level = 'needs_optimization';
    readyForResults = false;
    recommendPlayground = true;
  }
  
  return {
    score,
    level,
    readyForResults,
    recommendPlayground,
    issues
  };
}

console.log('🧪 Testing Quality Assessment Function...\n');

// Test with complete formulation
console.log('📊 Test 1: Complete Formulation');
const result1 = evaluateFormulationQuality(mockComponents, []);
console.log(`  Score: ${result1.score}/100`);
console.log(`  Level: ${result1.level}`);
console.log(`  Ready for Results: ${result1.readyForResults}`);
console.log(`  Recommend Playground: ${result1.recommendPlayground}`);
console.log(`  Issues: ${result1.issues.length > 0 ? result1.issues.join(', ') : 'None'}`);

// Test with missing components
console.log('\n📊 Test 2: Missing Cost Analysis');
const incompleteComponents = { ...mockComponents };
delete incompleteComponents.costAnalysis;
const result2 = evaluateFormulationQuality(incompleteComponents, []);
console.log(`  Score: ${result2.score}/100`);
console.log(`  Level: ${result2.level}`);
console.log(`  Ready for Results: ${result2.readyForResults}`);
console.log(`  Recommend Playground: ${result2.recommendPlayground}`);
console.log(`  Issues: ${result2.issues.join(', ')}`);

// Test with validation errors
console.log('\n📊 Test 3: With Validation Errors');
const validationErrors = [
  { field: 'ingredients', message: 'Invalid percentage' },
  { field: 'cost', message: 'Cost too high' }
];
const result3 = evaluateFormulationQuality(mockComponents, validationErrors);
console.log(`  Score: ${result3.score}/100`);
console.log(`  Level: ${result3.level}`);
console.log(`  Ready for Results: ${result3.readyForResults}`);
console.log(`  Recommend Playground: ${result3.recommendPlayground}`);
console.log(`  Issues: ${result3.issues.join(', ')}`);

// Test minimal formulation
console.log('\n📊 Test 4: Minimal Formulation');
const minimalComponents = {
  mainRecipe: {
    ingredients: [{ name: 'Water' }, { name: 'Extract' }] // Only 2 ingredients
  },
  variations: [{ name: 'V1' }], // Only 1 variation
  errors: {}
};
const result4 = evaluateFormulationQuality(minimalComponents, []);
console.log(`  Score: ${result4.score}/100`);
console.log(`  Level: ${result4.level}`);
console.log(`  Ready for Results: ${result4.readyForResults}`);
console.log(`  Recommend Playground: ${result4.recommendPlayground}`);
console.log(`  Issues: ${result4.issues.join(', ')}`);

console.log('\n✅ Quality Assessment Function Tests Complete!');
console.log('\n📈 Quality Thresholds:');
console.log('  - Excellent (85-100): Ready for Results');
console.log('  - Good (70-84): Ready for Results');
console.log('  - Acceptable (50-69): Recommend Playground');
console.log('  - Needs Optimization (<50): Recommend Playground');