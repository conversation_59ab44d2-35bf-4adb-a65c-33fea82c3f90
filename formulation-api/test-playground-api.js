#!/usr/bin/env node

const http = require('http');

// Test data for formulation generation
const testData = {
  formData: {
    industry: 'beverages',
    productType: 'functional_juice',
    productDescription: 'Antioxidant-rich juice blend',
    goals: {
      budget: 50000,
      compliance: 'fssai',
      sustainability: 'high'
    }
  }
};

const postData = JSON.stringify(testData);

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/api/playground/generate',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData),
    'Authorization': 'Bearer test-token' // Mock token for testing
  }
};

console.log('🧪 Testing playground API with quality assessment...\n');

const req = http.request(options, (res) => {
  let data = '';

  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      
      console.log('📡 API Response Status:', res.statusCode);
      console.log('📊 Quality Assessment Results:');
      
      if (response.success && response.data) {
        const quality = response.data;
        console.log(`  - Quality Score: ${quality.qualityScore}/100`);
        console.log(`  - Quality Level: ${quality.qualityLevel}`);
        console.log(`  - Ready for Results: ${quality.readyForResults}`);
        console.log(`  - Recommend Playground: ${quality.recommendPlayground}`);
        
        if (quality.qualityIssues && quality.qualityIssues.length > 0) {
          console.log('  - Quality Issues:');
          quality.qualityIssues.forEach(issue => {
            console.log(`    • ${issue}`);
          });
        }
        
        console.log('\n✅ Quality assessment system working correctly!');
        
        // Show component status
        if (quality.components) {
          console.log('\n🧩 Component Status:');
          console.log(`  - Main Recipe: ${quality.components.mainRecipe ? '✅' : '❌'}`);
          console.log(`  - Variations: ${quality.components.variations?.length || 0}/3`);
          console.log(`  - Nutritional Profile: ${quality.components.nutritionalProfile ? '✅' : '❌'}`);
          console.log(`  - Cost Analysis: ${quality.components.costAnalysis ? '✅' : '❌'}`);
          console.log(`  - Manufacturing: ${quality.components.manufacturingProcedure ? '✅' : '❌'}`);
        }
        
      } else {
        console.log('❌ API Error:', response.error || 'Unknown error');
      }
      
    } catch (error) {
      console.log('❌ JSON Parse Error:', error.message);
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.log('❌ Request Error:', error.message);
  
  if (error.code === 'ECONNREFUSED') {
    console.log('💡 Make sure the backend server is running on port 3001');
    console.log('   Run: npm start');
  }
});

req.write(postData);
req.end();