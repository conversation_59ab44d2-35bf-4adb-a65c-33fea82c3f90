async function testFormulationIntegration() {
  try {
    console.log('Testing formulation components integration...');
    
    // First, login to get a token
    console.log('1. Logging in...');
    const loginResponse = await fetch('http://localhost:7700/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Asdram123@'
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('Login response:', JSON.stringify(loginData, null, 2));
    
    if (!loginData.success) {
      throw new Error('Login failed: ' + loginData.message);
    }
    
    const token = loginData.data?.token || loginData.token;
    console.log('✅ Login successful');
    console.log('Token (first 50 chars):', token?.substring(0, 50) + '...');
    
    if (!token) {
      throw new Error('No token received from login response');
    }
    
    // Test formulation generation
    console.log('2. Testing formulation generation...');
    const formulationResponse = await fetch('http://localhost:7700/api/formulations/generate', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        formData: {
          industry: 'cosmetics',
          productType: 'face_serum',
          productDescription: 'Anti-aging vitamin C serum',
          goals: {
            budget: 50000,
            nutrition_targets: {},
            sustainability: 'moderate',
            compliance: 'fda'
          }
        }
      })
    });
    
    const formulationData = await formulationResponse.json();
    
    console.log('✅ Formulation generation response:');
    console.log(JSON.stringify(formulationData, null, 2));
    
    if (formulationData.success) {
      console.log('🎉 Formulation components integration is working!');
    } else {
      console.log('❌ Formulation generation failed:', formulationData.error);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testFormulationIntegration();