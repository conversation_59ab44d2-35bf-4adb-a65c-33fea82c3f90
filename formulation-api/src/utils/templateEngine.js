const fs = require('fs');
const path = require('path');

class TemplateEngine {
  constructor() {
    this.templateCache = new Map();
  }

  /**
   * Load template from file
   */
  loadTemplate(templateName) {
    if (this.templateCache.has(templateName)) {
      return this.templateCache.get(templateName);
    }

    const templatePath = path.join(__dirname, '..', 'templates', `${templateName}.md`);
    const template = fs.readFileSync(templatePath, 'utf-8');
    this.templateCache.set(templateName, template);
    return template;
  }

  /**
   * Simple template engine that supports:
   * - {{variable}} - simple substitution
   * - {{#if condition}}...{{/if}} - conditional blocks
   * - {{#each array}}...{{/each}} - iteration
   */
  render(templateName, data) {
    let template = this.loadTemplate(templateName);
    
    // Process conditionals first
    template = this.processConditionals(template, data);
    
    // Process loops
    template = this.processLoops(template, data);
    
    // Process simple substitutions
    template = this.processSubstitutions(template, data);
    
    return template;
  }

  processConditionals(template, data) {
    const conditionalRegex = /\{\{#if\s+(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g;
    
    return template.replace(conditionalRegex, (match, condition, content) => {
      const value = this.getNestedValue(data, condition);
      return value ? content : '';
    });
  }

  processLoops(template, data) {
    const loopRegex = /\{\{#each\s+(\w+)\}\}([\s\S]*?)\{\{\/each\}\}/g;
    
    return template.replace(loopRegex, (match, arrayName, content) => {
      const array = this.getNestedValue(data, arrayName);
      if (!Array.isArray(array)) return '';
      
      return array.map(item => {
        let itemContent = content;
        // Replace item properties
        Object.keys(item).forEach(key => {
          const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
          itemContent = itemContent.replace(regex, item[key] || '');
        });
        return itemContent;
      }).join('');
    });
  }

  processSubstitutions(template, data) {
    const substitutionRegex = /\{\{([^#\/\s][^}]*)\}\}/g;
    
    return template.replace(substitutionRegex, (match, variable) => {
      // Handle expressions like budgetPerUnit * 1.5
      if (variable.includes('*') || variable.includes('+') || variable.includes('-') || variable.includes('/')) {
        try {
          // Simple expression evaluation (safe for basic math)
          const expression = variable.replace(/(\w+)/g, (varName) => {
            const value = this.getNestedValue(data, varName);
            return typeof value === 'number' ? value : 0;
          });
          return eval(expression);
        } catch (e) {
          return match;
        }
      }
      
      const value = this.getNestedValue(data, variable);
      return value !== undefined ? value : match;
    });
  }

  getNestedValue(obj, path) {
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return undefined;
      }
    }
    
    return current;
  }

  /**
   * Format shelf life in readable format
   */
  formatShelfLife(days) {
    if (days >= 365) {
      const years = Math.round(days / 365 * 10) / 10;
      return `${years} year${years !== 1 ? 's' : ''}`;
    }
    if (days >= 30) {
      const months = Math.round(days / 30);
      return `${months} month${months !== 1 ? 's' : ''}`;
    }
    return `${days} days`;
  }
}

module.exports = new TemplateEngine();