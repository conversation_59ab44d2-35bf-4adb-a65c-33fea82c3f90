const winston = require('winston');
const path = require('path');

// Create logs directory if it doesn't exist
const fs = require('fs');

// Use shared logs directory from environment or default to relative path
const getLogsDirectory = () => {
  if (process.env.LOG_FILE_PATH) {
    return path.dirname(path.resolve(__dirname, '../..', process.env.LOG_FILE_PATH));
  }
  // Default to shared logs directory
  return path.resolve(__dirname, '../../../logs');
};

const logsDir = getLogsDirectory();
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for console output
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let msg = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
      msg += ` ${JSON.stringify(meta)}`;
    }
    return msg;
  })
);

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: fileFormat,
  defaultMeta: { 
    service: 'agrizy-formulation-api',
    environment: process.env.NODE_ENV || 'development'
  },
  transports: [
    // Error log file
    new winston.transports.File({
      filename: process.env.LOG_ERROR_FILE_PATH 
        ? path.resolve(__dirname, '../..', process.env.LOG_ERROR_FILE_PATH)
        : path.join(logsDir, 'backend-error.log'),
      level: 'error',
      maxsize: parseInt(process.env.LOG_MAX_FILE_SIZE) || 10485760, // 10MB default
      maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5,
      tailable: true
    }),
    
    // Combined log file
    new winston.transports.File({
      filename: process.env.LOG_FILE_PATH 
        ? path.resolve(__dirname, '../..', process.env.LOG_FILE_PATH)
        : path.join(logsDir, 'backend.log'),
      maxsize: parseInt(process.env.LOG_MAX_FILE_SIZE) || 10485760, // 10MB default
      maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5,
      tailable: true
    })
  ],
  
  // Handle uncaught exceptions
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'backend-exceptions.log')
    })
  ],
  
  // Handle unhandled promise rejections
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'backend-rejections.log')
    })
  ]
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: 'debug'
  }));
}

// Helper methods for common logging patterns
logger.logRequest = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const logData = {
      method: req.method,
      url: req.originalUrl,
      status: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    };
    
    if (res.statusCode >= 400) {
      logger.warn('HTTP Request Error', logData);
    } else {
      logger.info('HTTP Request', logData);
    }
  });
  
  next();
};

logger.logError = (error, context = {}) => {
  logger.error('Application Error', {
    message: error.message,
    stack: error.stack,
    ...context
  });
};

logger.logAuth = (action, userId, email, success, context = {}) => {
  logger.info('Authentication Event', {
    action,
    userId,
    email,
    success,
    timestamp: new Date().toISOString(),
    ...context
  });
};

logger.logDatabase = (operation, table, success, duration, context = {}) => {
  logger.info('Database Operation', {
    operation,
    table,
    success,
    duration: `${duration}ms`,
    timestamp: new Date().toISOString(),
    ...context
  });
};

module.exports = logger;