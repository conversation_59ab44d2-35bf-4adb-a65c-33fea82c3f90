/**
 * MongoDB Index Management
 * Creates indexes for all configuration collections
 */

const createIndexes = async (db) => {
  console.log('[Indexes] Creating MongoDB indexes...');

  try {
    // Parameters collection
    await db.collection('parameters').createIndex({ key: 1 }, { unique: true });
    console.log('[Indexes] Created unique index on parameters.key');

    // Param answers collection
    await db.collection('param_answers').createIndex({ key: 1 }, { unique: true });
    console.log('[Indexes] Created unique index on param_answers.key');

    // Taxonomies collection
    await db.collection('taxonomies').createIndex({ level: 1 });
    await db.collection('taxonomies').createIndex({ parent_id: 1 });
    await db.collection('taxonomies').createIndex({ slug: 1 });
    console.log('[Indexes] Created indexes on taxonomies');

    // Param bindings collection
    await db.collection('param_bindings').createIndex({ 'target.level': 1, 'target.slug': 1 });
    await db.collection('param_bindings').createIndex({ parameter_key: 1 });
    console.log('[Indexes] Created indexes on param_bindings');

    // UI flows collection
    await db.collection('ui_flows').createIndex({ slug: 1 }, { unique: true });
    console.log('[Indexes] Created unique index on ui_flows.slug');

    // Guardrails collection
    await db.collection('guardrails').createIndex({ scope: 1, target: 1 });
    console.log('[Indexes] Created indexes on guardrails');

  // Components collection (for reference)
  await db.collection('components').createIndex({ category: 1 });
  await db.collection('components').createIndex({ inci: 1 });
  console.log('[Indexes] Created indexes on components');

    // Jobs queue
    await db.collection('jobs').createIndex({ status: 1, availableAt: 1, priority: -1 });
    await db.collection('jobs').createIndex({ projectId: 1 });
    await db.collection('jobs').createIndex({ type: 1, status: 1 });
    console.log('[Indexes] Created indexes on jobs queue');

    console.log('[Indexes] All indexes created successfully');
    return true;
  } catch (error) {
    console.error('[Indexes] Error creating indexes:', error);
    throw error;
  }
};

module.exports = { createIndexes };
