/**
 * Guardrail Evaluation Engine
 * Safely evaluates guardrail rules without using eval()
 */

class GuardrailEngine {
  /**
   * Evaluate a guardrail rule
   * @param {Object} rule - Guardrail rule object
   * @param {Object} context - Evaluation context (answers, components)
   * @returns {Object|null} Issue object if rule triggers, null otherwise
   */
  evaluateRule(rule, context) {
    try {
      console.log(`[GuardrailEngine] Evaluating rule: ${rule.name}`);

      let triggered = false;

      switch (rule.type) {
        case 'expression':
          triggered = this.evaluateExpression(rule.condition, context);
          break;
        case 'sum_fields':
          triggered = this.evaluateSumFields(rule.sum_config, context);
          break;
        default:
          console.warn(`[GuardrailEngine] Unknown rule type: ${rule.type}`);
          return null;
      }

      if (triggered) {
        console.log(`[GuardrailEngine] Rule triggered: ${rule.name}`);
        return {
          name: rule.name,
          severity: rule.severity || 'warning',
          message: this.interpolateMessage(rule.message, context)
        };
      }

      return null;
    } catch (error) {
      console.error(`[GuardrailEngine] Error evaluating rule ${rule.name}:`, error);
      return null;
    }
  }

  /**
   * Evaluate a simple expression (no eval!)
   * Supports basic comparisons and logical operators
   * @param {string} expression - Expression to evaluate
   * @param {Object} context - Variable context
   * @returns {boolean} Expression result
   */
  evaluateExpression(expression, context) {
    console.log(`[GuardrailEngine] Evaluating expression: ${expression}`);

    // Handle simple comparisons first
    const comparisonRegex = /^(\w+(?:\.\w+)*)\s*(<=?|>=?|===?|!==?)\s*(.+)$/;
    const match = expression.match(comparisonRegex);
    
    if (match) {
      const [, leftPath, operator, rightValue] = match;
      const leftVal = this.getValueFromPath(leftPath, context);
      const rightVal = this.parseValue(rightValue, context);

      return this.compare(leftVal, operator, rightVal);
    }

    // Handle logical operations (AND, OR)
    if (expression.includes('&&')) {
      const parts = expression.split('&&').map(p => p.trim());
      return parts.every(part => this.evaluateExpression(part, context));
    }

    if (expression.includes('||')) {
      const parts = expression.split('||').map(p => p.trim());
      return parts.some(part => this.evaluateExpression(part, context));
    }

    // Handle IF-THEN patterns
    const ifThenRegex = /IF\s+(.+)\s+THEN\s+(.+)/i;
    const ifThenMatch = expression.match(ifThenRegex);
    if (ifThenMatch) {
      const [, condition, consequence] = ifThenMatch;
      // IF A THEN B is equivalent to !A || B
      return !this.evaluateExpression(condition, context) || this.evaluateExpression(consequence, context);
    }

    // Handle boolean values
    if (expression === 'true') return true;
    if (expression === 'false') return false;

    // Try to get value from context
    const value = this.getValueFromPath(expression, context);
    return !!value;
  }

  /**
   * Evaluate sum_fields rule
   * @param {Object} sumConfig - Sum configuration
   * @param {Object} context - Evaluation context
   * @returns {boolean} Whether rule triggers
   */
  evaluateSumFields(sumConfig, context) {
    const { field, filter, operator, value, tolerance = 0 } = sumConfig;

    console.log(`[GuardrailEngine] Evaluating sum_fields: field=${field}, operator=${operator}, value=${value}`);

    // Get components array
    const components = context.components?.ingredients || [];
    
    // Filter components if needed
    let filtered = components;
    if (filter) {
      filtered = this.filterComponents(components, filter);
    }

    // Sum the field
    const sum = filtered.reduce((total, item) => {
      const fieldValue = this.getValueFromPath(field, item);
      return total + (parseFloat(fieldValue) || 0);
    }, 0);

    console.log(`[GuardrailEngine] Sum result: ${sum}`);

    // Apply tolerance
    const targetValue = parseFloat(value);
    const minValue = targetValue - tolerance;
    const maxValue = targetValue + tolerance;

    switch (operator) {
      case '<=':
        return sum > maxValue;
      case '>=':
        return sum < minValue;
      case '==':
        return sum < minValue || sum > maxValue;
      case '<':
        return sum >= targetValue;
      case '>':
        return sum <= targetValue;
      default:
        return false;
    }
  }

  /**
   * Filter components based on filter criteria
   * @param {Array} components - Array of components
   * @param {Object} filter - Filter configuration
   * @returns {Array} Filtered components
   */
  filterComponents(components, filter) {
    if (filter.inci_contains) {
      const searchTerm = filter.inci_contains.toLowerCase();
      return components.filter(c => 
        c.inci && c.inci.toLowerCase().includes(searchTerm)
      );
    }

    if (filter.category) {
      return components.filter(c => c.category === filter.category);
    }

    if (filter.function) {
      return components.filter(c => c.function === filter.function);
    }

    return components;
  }

  /**
   * Get value from nested path
   * @param {string} path - Dot-separated path (e.g., 'answers.ph_target')
   * @param {Object} obj - Object to traverse
   * @returns {*} Value at path
   */
  getValueFromPath(path, obj) {
    const parts = path.split('.');
    let current = obj;

    for (const part of parts) {
      if (current === null || current === undefined) {
        return undefined;
      }
      current = current[part];
    }

    return current;
  }

  /**
   * Parse a value (number, string, or variable reference)
   * @param {string} valueStr - Value string
   * @param {Object} context - Variable context
   * @returns {*} Parsed value
   */
  parseValue(valueStr, context) {
    // Remove quotes if present
    if ((valueStr.startsWith('"') && valueStr.endsWith('"')) ||
        (valueStr.startsWith("'") && valueStr.endsWith("'"))) {
      return valueStr.slice(1, -1);
    }

    // Try to parse as number
    const num = parseFloat(valueStr);
    if (!isNaN(num)) {
      return num;
    }

    // Check for boolean
    if (valueStr === 'true') return true;
    if (valueStr === 'false') return false;
    if (valueStr === 'null') return null;

    // Try to get from context
    if (valueStr.match(/^\w+(\.\w+)*$/)) {
      const value = this.getValueFromPath(valueStr, context);
      if (value !== undefined) {
        return value;
      }
    }

    // Return as string
    return valueStr;
  }

  /**
   * Compare two values with an operator
   * @param {*} left - Left value
   * @param {string} operator - Comparison operator
   * @param {*} right - Right value
   * @returns {boolean} Comparison result
   */
  compare(left, operator, right) {
    switch (operator) {
      case '<':
        return left < right;
      case '<=':
        return left <= right;
      case '>':
        return left > right;
      case '>=':
        return left >= right;
      case '==':
      case '===':
        return left === right;
      case '!=':
      case '!==':
        return left !== right;
      default:
        console.warn(`[GuardrailEngine] Unknown operator: ${operator}`);
        return false;
    }
  }

  /**
   * Interpolate message with context values
   * @param {string} message - Message template
   * @param {Object} context - Variable context
   * @returns {string} Interpolated message
   */
  interpolateMessage(message, context) {
    return message.replace(/\{(\w+(?:\.\w+)*)\}/g, (match, path) => {
      const value = this.getValueFromPath(path, context);
      return value !== undefined ? value : match;
    });
  }
}

module.exports = GuardrailEngine;