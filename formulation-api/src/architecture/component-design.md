# Backend API Component Design

## Server Configuration (server.js)
- **Framework**: Express.js 5.1.0
- **Port**: 7700 (configurable via PORT env)
- **Middleware Stack**: CORS, Helmet, Rate Limiting, Body Parser
- **Features**: Graceful shutdown, health checks, request logging

## Route Architecture

### Authentication Routes (/api/auth)
- `POST /login` - User authentication
- `POST /logout` - Session termination
- `GET /validate` - Token validation
- `POST /refresh` - Token refresh

### Project Routes (/api/projects)
- `GET /` - List user projects
- `POST /` - Create new project
- `GET /:id` - Get project details
- `PUT /:id` - Update project
- `DELETE /:id` - Delete project

### Formulation Routes (/api/formulations)
- `POST /generate` - AI formulation generation
- `PUT /:id/update` - Update formulation
- `GET /:id/versions` - Get version history
- `POST /:id/version` - Create new version

### Playground Routes (/api/playground)
- `POST /chat` - AI chat interaction
- `PUT /modify` - Real-time modifications
- `GET /quality-score` - Calculate quality metrics

## Controller Layer

### authController.js
- **Methods**: login(), logout(), validateToken(), refreshToken()
- **Validation**: Email format, password strength, domain restrictions
- **Security**: BCrypt hashing, JWT generation, session management

### projectController.js
- **Methods**: createProject(), getProjects(), updateProject(), deleteProject()
- **Features**: User isolation, project validation, status management
- **Pagination**: Cursor-based pagination for large datasets

### formulationController.js
- **Methods**: generateFormulation(), updateFormulation(), getVersions()
- **AI Integration**: Claude API calls, prompt engineering
- **Validation**: Ingredient compatibility, regulatory compliance

## Service Layer

### claudeService.js
- **Purpose**: Anthropic Claude API integration
- **Methods**: generateFormulation(), chatInteraction(), validateResponse()
- **Features**: Prompt templates, response parsing, error handling
- **Rate Limiting**: API quota management

### qualityService.js
- **Purpose**: Formulation quality assessment
- **Methods**: calculateScore(), validateIngredients(), checkCompliance()
- **Metrics**: Nutritional balance, cost efficiency, regulatory compliance

### validationService.js
- **Purpose**: Data validation and sanitization
- **Methods**: validateProject(), validateFormulation(), sanitizeInput()
- **Schemas**: AJV JSON schema validation

## Data Models

### User.js (Mongoose Schema)
- **Fields**: email, passwordHash, createdAt, updatedAt
- **Indexes**: email (unique), createdAt
- **Methods**: comparePassword(), generateToken()

### Project.js (Mongoose Schema)
- **Fields**: userId, name, industry, productType, currentFormulation
- **Embedded**: formulation data, conversation history, versions
- **Methods**: addVersion(), updateFormulation(), calculateQuality()

### Session.js (Mongoose Schema)
- **Fields**: userId, token, expiresAt
- **TTL Index**: Automatic cleanup of expired sessions
- **Methods**: isValid(), extend()