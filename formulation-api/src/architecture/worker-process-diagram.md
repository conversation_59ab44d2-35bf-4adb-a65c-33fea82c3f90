# Worker Process Architecture Diagram

## Job Queue System and Worker Types

```mermaid
graph TB
    subgraph "Job Queue System"
        JobQueue[(MongoDB Job Queue)]
        QueueService[queue.service.js]
        QueueService --> JobQueue
        
        JobQueue --> |priority, availableAt| JobScheduler[Job Scheduler]
        JobScheduler --> |claim next| WorkerPool[Worker Pool]
    end
    
    subgraph "Worker Types"
        WorkerPool --> FormulationWorker[Formulation Generation Worker]
        WorkerPool --> ChatWorker[Chat Processing Worker]
        WorkerPool --> ExportWorker[Export Generation Worker]
        WorkerPool --> CleanupWorker[Cleanup & Maintenance Worker]
        WorkerPool --> NotificationWorker[Notification Worker]
    end
    
    subgraph "Formulation Generation Pipeline"
        FormulationWorker --> ValidateInput[Input Validation]
        ValidateInput --> LoadContext[Load Project Context]
        LoadContext --> BuildPrompt[Build AI Prompt]
        BuildPrompt --> CallClaude[Claude API Call]
        CallClaude --> ValidateResponse[Response Validation]
        ValidateResponse --> CalculateQuality[Quality Assessment]
        CalculateQuality --> SaveFormulation[Save to Database]
        SaveFormulation --> UpdateProject[Update Project Status]
        UpdateProject --> NotifyUser[Queue Notification]
    end
    
    subgraph "Chat Processing Pipeline"
        ChatWorker --> ParseMessage[Parse User Message]
        ParseMessage --> AnalyzeIntent[Intent Analysis]
        AnalyzeIntent --> LoadSession[Load Chat Session]
        LoadSession --> BuildChatPrompt[Build Chat Prompt]
        BuildChatPrompt --> ProcessChat[Process with Claude]
        ProcessChat --> UpdateFormulation[Update Formulation]
        UpdateFormulation --> SaveSession[Save Session State]
        SaveSession --> SendResponse[Send Real-time Response]
    end
    
    subgraph "Export Generation Pipeline"
        ExportWorker --> LoadFormulation[Load Formulation Data]
        LoadFormulation --> SelectTemplate[Select Export Template]
        SelectTemplate --> GenerateContent[Generate Export Content]
        GenerateContent --> FormatOutput[Format Output (PDF/JSON)]
        FormatOutput --> SaveFile[Save Export File]
        SaveFile --> GenerateDownloadLink[Generate Download Link]
        GenerateDownloadLink --> NotifyComplete[Notify Export Complete]
    end
    
    subgraph "Cleanup & Maintenance Pipeline"
        CleanupWorker --> CleanExpiredSessions[Clean Expired Sessions]
        CleanupWorker --> CleanOldJobs[Clean Completed Jobs]
        CleanupWorker --> CleanTempFiles[Clean Temporary Files]
        CleanupWorker --> UpdateMetrics[Update System Metrics]
        CleanupWorker --> HealthCheck[System Health Check]
    end
    
    subgraph "Notification Pipeline"
        NotificationWorker --> LoadNotification[Load Notification Data]
        LoadNotification --> SelectChannel[Select Notification Channel]
        SelectChannel --> EmailNotification[Email Notification]
        SelectChannel --> WebSocketNotification[WebSocket Notification]
        SelectChannel --> PushNotification[Push Notification]
        EmailNotification --> TrackDelivery[Track Delivery Status]
        WebSocketNotification --> TrackDelivery
        PushNotification --> TrackDelivery
    end
    
    subgraph "External Services"
        CallClaude --> AnthropicAPI[Anthropic Claude API]
        ProcessChat --> AnthropicAPI
        EmailNotification --> EmailService[Email Service Provider]
        WebSocketNotification --> WebSocketServer[WebSocket Server]
        FormatOutput --> PDFGenerator[PDF Generation Service]
    end
    
    subgraph "Database Operations"
        SaveFormulation --> ProjectsDB[(Projects Collection)]
        UpdateProject --> ProjectsDB
        SaveSession --> SessionsDB[(Sessions Collection)]
        LoadFormulation --> ProjectsDB
        CleanExpiredSessions --> SessionsDB
        CleanOldJobs --> JobQueue
    end
    
    subgraph "Monitoring & Logging"
        WorkerPool --> WorkerMonitor[Worker Monitor]
        WorkerMonitor --> MetricsCollector[Metrics Collector]
        WorkerMonitor --> AlertSystem[Alert System]
        WorkerMonitor --> LogAggregator[Log Aggregator]
        
        MetricsCollector --> MetricsDB[(Metrics Storage)]
        AlertSystem --> NotificationWorker
        LogAggregator --> LogStorage[(Log Storage)]
    end
    
    subgraph "Error Handling & Retry"
        WorkerPool --> ErrorHandler[Error Handler]
        ErrorHandler --> RetryLogic[Retry Logic]
        RetryLogic --> |exponential backoff| JobQueue
        ErrorHandler --> DeadLetterQueue[Dead Letter Queue]
        ErrorHandler --> ErrorNotification[Error Notification]
    end
    
    classDef workerComponent fill:#e8f5e8
    classDef queueComponent fill:#e3f2fd
    classDef pipelineComponent fill:#fff3e0
    classDef externalComponent fill:#f3e5f5
    classDef storageComponent fill:#fce4ec
    
    class FormulationWorker,ChatWorker,ExportWorker,CleanupWorker,NotificationWorker workerComponent
    class JobQueue,QueueService,JobScheduler,WorkerPool queueComponent
    class ValidateInput,LoadContext,BuildPrompt,ParseMessage,AnalyzeIntent,LoadFormulation pipelineComponent
    class AnthropicAPI,EmailService,WebSocketServer,PDFGenerator externalComponent
    class ProjectsDB,SessionsDB,MetricsDB,LogStorage storageComponent
```

## Worker Process Flow

```mermaid
sequenceDiagram
    participant QS as Queue Service
    participant JQ as Job Queue
    participant W as Worker
    participant DB as Database
    participant API as External API
    participant Monitor as Monitor
    
    QS->>JQ: Enqueue job with priority
    JQ->>JQ: Store job with availableAt timestamp
    
    loop Worker Polling
        W->>JQ: Claim next available job
        JQ-->>W: Return job or null
    end
    
    W->>W: Process job payload
    W->>DB: Load required data
    DB-->>W: Return data
    
    alt External API Required
        W->>API: Make API call
        API-->>W: Return response
    end
    
    W->>DB: Save results
    DB-->>W: Confirm save
    
    W->>JQ: Mark job as completed
    W->>Monitor: Report job completion
    
    alt Job Failed
        W->>JQ: Mark job as failed
        W->>JQ: Schedule retry with backoff
        W->>Monitor: Report job failure
    end
```

## Job Priority and Scheduling

```mermaid
graph LR
    subgraph "Job Priority Levels"
        Critical[Critical Priority: 100]
        High[High Priority: 75]
        Normal[Normal Priority: 50]
        Low[Low Priority: 25]
        Maintenance[Maintenance: 0]
    end
    
    subgraph "Job Types by Priority"
        Critical --> UserFormulation[User Formulation Requests]
        Critical --> SystemAlerts[System Alert Notifications]
        
        High --> ChatInteractions[Chat Interactions]
        High --> ExportRequests[Export Requests]
        
        Normal --> BackgroundUpdates[Background Updates]
        Normal --> QualityRecalculation[Quality Recalculation]
        
        Low --> DataMigration[Data Migration]
        Low --> ReportGeneration[Report Generation]
        
        Maintenance --> SessionCleanup[Session Cleanup]
        Maintenance --> LogRotation[Log Rotation]
        Maintenance --> MetricsAggregation[Metrics Aggregation]
    end
    
    subgraph "Scheduling Rules"
        UserFormulation --> |immediate| ImmediateExecution[Immediate Execution]
        ChatInteractions --> |<5s delay| NearImmediate[Near Immediate]
        ExportRequests --> |<30s delay| ShortDelay[Short Delay]
        BackgroundUpdates --> |<5min delay| MediumDelay[Medium Delay]
        SessionCleanup --> |scheduled| ScheduledExecution[Scheduled Execution]
    end
```
