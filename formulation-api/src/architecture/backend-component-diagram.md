# Backend API Component Architecture Diagram

## Complete Backend Service Architecture

```mermaid
graph TB
    subgraph "Server Layer"
        Server[server.js - Express 5.1.0]
        Server --> Middleware[Middleware Stack]
        Middleware --> CORS[CORS Policy]
        Middleware --> Helmet[Security Headers]
        Middleware --> RateLimit[Rate Limiting]
        Middleware --> BodyParser[Body Parser]
        Middleware --> Error<PERSON>andler[<PERSON><PERSON><PERSON>]
    end
    
    subgraph "Route Layer"
        Server --> AuthRoutes[/api/auth - Authentication]
        Server --> ProjectRoutes[/api/projects - Project Management]
        Server --> FormulationRoutes[/api/formulations - Formulation CRUD]
        Server --> PlaygroundRoutes[/api/playground - AI Interaction]
        Server --> ConfigRoutes[/api/configuration - System Config]
        Server --> AdminRoutes[/api/admin - Admin CRUD]
        Server --> BlockRoutes[/api/formulation-blocks - Block Management]
        Server --> LogRoutes[/api/logs - Logging]
    end
    
    subgraph "Controller Layer"
        AuthRoutes --> AuthController[authController.js]
        ProjectRoutes --> ProjectController[Project Route Handlers]
        FormulationRoutes --> FormulationController[Formulation Route Handlers]
        PlaygroundRoutes --> PlaygroundController[Playground Route Handlers]
    end
    
    subgraph "Service Layer - Core"
        AuthController --> AuthService[Authentication Logic]
        ProjectController --> ProjectService[Project Management]
        FormulationController --> FormulationService[formulation.service.js]
        PlaygroundController --> ChatService[ChatService.js]
        PlaygroundController --> ChatSessionManager[ChatSessionManager.js]
    end
    
    subgraph "Service Layer - AI & Processing"
        ChatService --> ClaudeService[claudeService.js]
        FormulationService --> ClaudeService
        ClaudeService --> AnthropicAPI[Anthropic Claude API]
        ChatService --> IntentAnalyzer[IntentAnalyzer.js]
        ClaudeService --> SchemaBuilder[schemaBuilder.js]
        ClaudeService --> TemplateEngine[templateEngine.js]
    end
    
    subgraph "Service Layer - Configuration"
        ConfigRoutes --> TaxonomiesService[taxonomies.service.js]
        ConfigRoutes --> ParametersService[parameters.service.js]
        ConfigRoutes --> GuardrailsService[guardrails.service.js]
        ConfigRoutes --> UiFlowsService[uiFlows.service.js]
        GuardrailsService --> GuardrailEngine[guardrailEngine.js]
    end
    
    subgraph "Service Layer - Infrastructure"
        Server --> QueueService[queue.service.js]
        QueueService --> JobQueue[MongoDB Job Queue]
        Server --> Logger[logger.js]
        Logger --> WinstonLogger[Winston 3.17.0]
    end
    
    subgraph "Data Layer - MongoDB"
        ProjectService --> ProjectModel[Project.js]
        AuthService --> UserModel[User.js]
        AuthService --> SessionModel[Session.js]
        BlockRoutes --> FormulationBlockModel[FormulationBlock.js]
        BlockRoutes --> FormulationBindingModel[FormulationBinding.js]
        
        ProjectModel --> MongoDB[(MongoDB 8.16.4)]
        UserModel --> MongoDB
        SessionModel --> MongoDB
        FormulationBlockModel --> MongoDB
        FormulationBindingModel --> MongoDB
    end
    
    subgraph "Data Layer - Collections"
        TaxonomiesService --> TaxonomiesCollection[taxonomies collection]
        ParametersService --> ParametersCollection[parameters collection]
        ParametersService --> BindingsCollection[param_bindings collection]
        ParametersService --> AnswersCollection[param_answers collection]
        GuardrailsService --> GuardrailsCollection[guardrails collection]
        
        TaxonomiesCollection --> MongoDB
        ParametersCollection --> MongoDB
        BindingsCollection --> MongoDB
        AnswersCollection --> MongoDB
        GuardrailsCollection --> MongoDB
    end
    
    subgraph "Middleware Layer"
        AuthRoutes --> AuthMiddleware[auth.js - JWT Verification]
        AdminRoutes --> AdminMiddleware[requireAdmin]
        Server --> ErrorMiddleware[errorHandler.js]
        AuthMiddleware --> JWTValidation[JWT Token Validation]
        AdminMiddleware --> RoleValidation[Role-based Access Control]
    end
    
    subgraph "Schema & Validation"
        FormulationService --> FormulationSchema[formulationSchema.json]
        ClaudeService --> AJVValidator[AJV Schema Validation]
        ConfigRoutes --> ValidationSchemas[schemas.js]
        ValidationSchemas --> TaxonomyValidation[Taxonomy Validation]
        ValidationSchemas --> ParameterValidation[Parameter Validation]
        ValidationSchemas --> BindingValidation[Binding Validation]
    end
    
    subgraph "Template System"
        ClaudeService --> PromptTemplates[chat-prompts.js]
        ClaudeService --> FormulationPrompt[formulationPrompt.md]
        TemplateEngine --> PromptBuilder[Template Builder]
        PromptBuilder --> ContextInjection[Context Injection]
    end
    
    subgraph "External Integrations"
        AnthropicAPI --> ClaudeModel[Claude 3.5 Sonnet]
        Logger --> FileSystem[Log Files]
        MongoDB --> MongooseODM[Mongoose 8.16.4]
    end
    
    classDef routeComponent fill:#e3f2fd
    classDef serviceComponent fill:#e8f5e8
    classDef dataComponent fill:#fff3e0
    classDef middlewareComponent fill:#fce4ec
    classDef externalComponent fill:#f3e5f5
    
    class AuthRoutes,ProjectRoutes,FormulationRoutes,PlaygroundRoutes,ConfigRoutes,AdminRoutes,BlockRoutes,LogRoutes routeComponent
    class ChatService,ClaudeService,FormulationService,TaxonomiesService,ParametersService,GuardrailsService,QueueService serviceComponent
    class ProjectModel,UserModel,SessionModel,FormulationBlockModel,FormulationBindingModel,MongoDB dataComponent
    class AuthMiddleware,AdminMiddleware,ErrorMiddleware middlewareComponent
    class AnthropicAPI,ClaudeModel,FileSystem externalComponent
```

## Service Communication Flow

```mermaid
sequenceDiagram
    participant Client
    participant Route
    participant Middleware
    participant Controller
    participant Service
    participant Database
    participant External
    
    Client->>Route: HTTP Request
    Route->>Middleware: Apply middleware stack
    Middleware->>Middleware: Auth, validation, rate limiting
    Middleware->>Controller: Validated request
    Controller->>Service: Business logic call
    Service->>Database: Data operations
    Database-->>Service: Query results
    Service->>External: External API calls (if needed)
    External-->>Service: External response
    Service-->>Controller: Processed data
    Controller-->>Route: Response data
    Route-->>Client: HTTP Response
```

## Data Flow Architecture

```mermaid
graph LR
    subgraph "Request Processing"
        A[HTTP Request] --> B[Route Handler]
        B --> C[Middleware Chain]
        C --> D[Controller Method]
        D --> E[Service Layer]
    end
    
    subgraph "Business Logic"
        E --> F[Data Validation]
        F --> G[Business Rules]
        G --> H[External API Calls]
        H --> I[Data Transformation]
    end
    
    subgraph "Data Persistence"
        I --> J[Database Operations]
        J --> K[MongoDB Collections]
        K --> L[Document Updates]
        L --> M[Response Formation]
    end
    
    subgraph "Response Delivery"
        M --> N[Error Handling]
        N --> O[Response Formatting]
        O --> P[HTTP Response]
        P --> Q[Client Delivery]
    end
```
