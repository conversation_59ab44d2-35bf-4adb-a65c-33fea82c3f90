# System Integration Architecture Documentation

## Data Flow Architecture

### Request-Response Flow

```mermaid
sequenceDiagram
    participant Client as Frontend Client
    participant LB as Load Balancer
    participant API as API Server
    participant Auth as Auth Service
    participant Queue as Job Queue
    participant Worker as Background Worker
    participant DB as MongoDB
    participant Claude as Claude API
    
    Client->>LB: HTTP Request
    LB->>API: Route Request
    API->>Auth: Validate JWT Token
    Auth-->>API: User Context
    API->>DB: Query/Update Data
    DB-->>API: Data Response
    
    alt Background Processing Required
        API->>Queue: Enqueue Job
        Queue-->>API: Job ID
        API-->>Client: Accepted (202)
        
        Queue->>Worker: Claim Job
        Worker->>Claude: AI Processing
        Claude-->>Worker: AI Response
        Worker->>DB: Save Results
        Worker->>Queue: Complete Job
    else Synchronous Response
        API-->>Client: Response Data
    end
```

### Data Synchronization Flow

```mermaid
graph LR
    subgraph "Frontend State"
        FrontendState[React State]
        LocalStorage[Local Storage]
        SessionStorage[Session Storage]
    end
    
    subgraph "API Layer"
        RESTEndpoints[REST Endpoints]
        WebSocketServer[WebSocket Server]
        EventEmitter[Event Emitter]
    end
    
    subgraph "Backend State"
        MongoDB[(MongoDB)]
        RedisCache[(Redis Cache)]
        JobQueue[(Job Queue)]
    end
    
    FrontendState <--> RESTEndpoints
    FrontendState <--> WebSocketServer
    RESTEndpoints <--> MongoDB
    WebSocketServer <--> EventEmitter
    EventEmitter <--> MongoDB
    MongoDB <--> RedisCache
    RESTEndpoints <--> JobQueue
```

## Security Architecture

### Authentication & Authorization Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant AuthService
    participant Database
    participant JWTService
    
    User->>Frontend: Login Credentials
    Frontend->>API: POST /api/auth/login
    API->>AuthService: Validate Credentials
    AuthService->>Database: Query User
    Database-->>AuthService: User Data
    AuthService->>AuthService: Verify Password
    AuthService->>JWTService: Generate Tokens
    JWTService-->>AuthService: Access & Refresh Tokens
    AuthService-->>API: Tokens + User Data
    API-->>Frontend: Authentication Response
    Frontend->>Frontend: Store Tokens
    
    Note over Frontend,API: Subsequent Requests
    Frontend->>API: Request with Bearer Token
    API->>JWTService: Verify Token
    JWTService-->>API: Token Valid + User Context
    API->>API: Process Request
    API-->>Frontend: Response
```

### Security Layers

#### 1. Network Security
- **HTTPS/TLS 1.3**: All communications encrypted
- **CORS Policy**: Strict cross-origin resource sharing
- **Rate Limiting**: API endpoint protection
- **DDoS Protection**: Request throttling and filtering
- **IP Whitelisting**: Admin endpoint restrictions

#### 2. Application Security
- **JWT Authentication**: Stateless token-based auth
- **Role-Based Access Control (RBAC)**: Granular permissions
- **Input Validation**: Comprehensive input sanitization
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Content Security Policy headers

#### 3. Data Security
- **Encryption at Rest**: MongoDB encryption
- **Encryption in Transit**: TLS for all communications
- **PII Protection**: Personal data encryption
- **Audit Logging**: Comprehensive access logging
- **Data Retention**: Automated data lifecycle management

### Security Configuration

#### JWT Token Management
```javascript
{
  accessToken: {
    expiresIn: '15m',
    algorithm: 'RS256',
    issuer: 'agrizy-formulation-api',
    audience: 'agrizy-formulation-web'
  },
  refreshToken: {
    expiresIn: '7d',
    algorithm: 'RS256',
    httpOnly: true,
    secure: true,
    sameSite: 'strict'
  }
}
```

#### CORS Configuration
```javascript
{
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:5173'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count']
}
```

## Performance Optimization

### Caching Strategy

#### Multi-Level Caching
1. **Browser Cache**: Static assets and API responses
2. **CDN Cache**: Global content distribution
3. **Application Cache**: In-memory caching with Redis
4. **Database Cache**: MongoDB query result caching

#### Cache Invalidation
- **Time-based**: TTL for configuration data (5 minutes)
- **Event-based**: Invalidate on data updates
- **Manual**: Admin-triggered cache clearing
- **Versioned**: Cache versioning for API responses

### Database Optimization

#### MongoDB Performance
- **Indexing Strategy**: Compound indexes for complex queries
- **Aggregation Pipelines**: Efficient data processing
- **Connection Pooling**: Optimized connection management
- **Read Preferences**: Read from secondaries when appropriate

#### Query Optimization
```javascript
// Optimized project query with indexes
db.projects.find({
  user_id: ObjectId("..."),
  status: "ready",
  is_active: true
}).sort({ updated_at: -1 }).limit(10)

// Compound index
db.projects.createIndex({
  user_id: 1,
  status: 1,
  is_active: 1,
  updated_at: -1
})
```

### API Performance

#### Response Optimization
- **Pagination**: Cursor-based pagination for large datasets
- **Field Selection**: Return only requested fields
- **Compression**: Gzip compression for responses
- **HTTP/2**: Modern protocol support

#### Request Optimization
- **Request Batching**: Batch multiple operations
- **Parallel Processing**: Concurrent API calls
- **Connection Reuse**: HTTP keep-alive
- **Request Deduplication**: Prevent duplicate requests

## External Service Integration

### Anthropic Claude API Integration

#### Configuration
```javascript
{
  apiKey: process.env.ANTHROPIC_API_KEY,
  model: 'claude-3-5-sonnet-20240620',
  maxTokens: 4000,
  temperature: 0.7,
  timeout: 60000,
  retries: 3,
  retryDelay: 1000
}
```

#### Error Handling
- **Rate Limit Handling**: Exponential backoff
- **Timeout Management**: Request timeout with retries
- **Circuit Breaker**: Temporary service disable on failures
- **Fallback Responses**: Graceful degradation

#### Usage Monitoring
- **Token Usage**: Track API token consumption
- **Cost Monitoring**: Monitor API costs
- **Performance Metrics**: Response time tracking
- **Error Rates**: API failure monitoring

### Email Service Integration

#### SMTP Configuration
```javascript
{
  host: process.env.SMTP_HOST,
  port: 587,
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  },
  tls: {
    rejectUnauthorized: false
  }
}
```

#### Email Templates
- **Formulation Complete**: Project generation notifications
- **Export Ready**: Download link notifications
- **System Alerts**: Error and maintenance notifications
- **User Onboarding**: Welcome and setup emails

### File Storage Integration

#### Local File Storage
- **Upload Directory**: Configurable upload path
- **File Validation**: Type and size restrictions
- **Cleanup**: Automatic temporary file cleanup
- **Security**: Path traversal protection

#### Cloud Storage (Future)
- **AWS S3**: Scalable object storage
- **Google Cloud Storage**: Alternative cloud storage
- **Azure Blob Storage**: Microsoft cloud storage
- **CDN Integration**: Global content delivery

## Monitoring and Observability

### Application Monitoring

#### Metrics Collection
- **Request Metrics**: Response times, status codes, throughput
- **Business Metrics**: User registrations, project creations, formulations generated
- **System Metrics**: CPU, memory, disk usage
- **Database Metrics**: Query performance, connection pool status

#### Health Checks
```javascript
// Health check endpoints
GET /health - Basic health status
GET /health/detailed - Comprehensive system status
GET /health/ready - Readiness probe for containers
GET /health/live - Liveness probe for containers
```

#### Alerting Rules
- **High Error Rate**: >5% 4xx/5xx responses for 5 minutes
- **Slow Response Time**: >2s average response time for 5 minutes
- **Database Issues**: Connection failures or slow queries
- **Queue Backup**: >100 pending jobs for 10 minutes
- **External API Failures**: Claude API errors >10% for 5 minutes

### Logging Strategy

#### Structured Logging
```javascript
{
  timestamp: '2024-01-15T10:30:00Z',
  level: 'info',
  service: 'formulation-api',
  requestId: 'req-uuid',
  userId: 'user-uuid',
  method: 'POST',
  path: '/api/formulations/generate',
  statusCode: 200,
  duration: 1250,
  message: 'Formulation generated successfully',
  metadata: {
    projectId: 'project-uuid',
    formulationType: 'beverage'
  }
}
```

#### Log Aggregation
- **Winston Logger**: Structured logging with multiple transports
- **File Rotation**: Daily log rotation with compression
- **Log Levels**: DEBUG, INFO, WARN, ERROR, FATAL
- **Context Injection**: Request ID and user context in all logs

### Error Tracking

#### Error Classification
- **Client Errors (4xx)**: User input errors, authentication failures
- **Server Errors (5xx)**: Application bugs, external service failures
- **Business Logic Errors**: Validation failures, constraint violations
- **Infrastructure Errors**: Database failures, network issues

#### Error Response Format
```javascript
{
  success: false,
  error: 'Validation failed',
  code: 'VALIDATION_ERROR',
  details: {
    field: 'industry',
    message: 'Industry is required'
  },
  requestId: 'req-uuid',
  timestamp: '2024-01-15T10:30:00Z'
}
```

## Deployment Architecture

### Container Strategy
- **Docker Containers**: Application containerization
- **Multi-stage Builds**: Optimized container images
- **Health Checks**: Container health monitoring
- **Resource Limits**: CPU and memory constraints

### Environment Configuration
- **Development**: Local development with hot reload
- **Staging**: Production-like environment for testing
- **Production**: High-availability production deployment
- **Testing**: Automated testing environment

### Configuration Management
- **Environment Variables**: Secure configuration management
- **Secret Management**: Encrypted secret storage
- **Feature Flags**: Runtime feature toggling
- **Configuration Validation**: Startup configuration validation

## Scalability Considerations

### Horizontal Scaling
- **Stateless Design**: No server-side session state
- **Load Balancing**: Request distribution across instances
- **Database Sharding**: Horizontal database scaling
- **Queue Scaling**: Multiple worker instances

### Vertical Scaling
- **Resource Optimization**: Efficient resource utilization
- **Memory Management**: Proper memory cleanup
- **CPU Optimization**: Efficient algorithm implementation
- **I/O Optimization**: Asynchronous I/O operations

### Auto-scaling Triggers
- **CPU Utilization**: Scale up at >70% CPU for 5 minutes
- **Memory Usage**: Scale up at >80% memory for 5 minutes
- **Queue Depth**: Scale workers when queue >50 jobs
- **Response Time**: Scale up when response time >2s average
