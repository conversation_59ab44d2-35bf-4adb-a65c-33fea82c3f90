# Worker Process Architecture Documentation

## Job Queue System

### queue.service.js
- **Purpose**: MongoDB-based job queue management for background processing
- **Methods**: `enqueue()`, `claimNext()`, `complete()`, `fail()`, `retry()`
- **Features**: Priority queuing, retry logic, exponential backoff, job monitoring
- **Storage**: MongoDB collection with compound indexes for performance
- **Concurrency**: Support for multiple worker instances with job locking

#### Job Structure
```javascript
{
  type: 'formulation_generation',
  status: 'queued|processing|completed|failed',
  projectId: ObjectId,
  payload: { /* job-specific data */ },
  priority: 0-100,
  attempts: 0,
  maxAttempts: 3,
  availableAt: Date,
  createdAt: Date,
  updatedAt: Date,
  lockedBy: 'worker-id',
  lockedAt: Date,
  result: { /* job results */ },
  error: { /* error details */ }
}
```

#### Priority Levels
- **Critical (100)**: User formulation requests, system alerts
- **High (75)**: Chat interactions, export requests
- **Normal (50)**: Background updates, quality recalculation
- **Low (25)**: Data migration, report generation
- **Maintenance (0)**: Session cleanup, log rotation

#### Retry Logic
- **Exponential Backoff**: 30s, 60s, 120s, 240s, 300s (max)
- **Max Attempts**: Configurable per job type (default: 3)
- **Dead Letter Queue**: Failed jobs after max attempts
- **Error Classification**: Retryable vs. permanent errors

## Worker Types

### 1. Formulation Generation Worker

#### Purpose
- Process AI formulation generation requests
- Handle complex formulation logic and validation
- Integrate with Claude API for AI-powered generation

#### Pipeline Steps
1. **Input Validation**: Validate project data and parameters
2. **Context Loading**: Load project context, goals, and constraints
3. **Prompt Building**: Construct AI prompts using template system
4. **Claude API Call**: Generate formulation using Anthropic Claude
5. **Response Validation**: Validate AI response against schema
6. **Quality Assessment**: Calculate quality scores and metrics
7. **Database Save**: Save formulation to project document
8. **Status Update**: Update project status to 'ready'
9. **Notification**: Queue user notification

#### Error Handling
- **API Failures**: Retry with exponential backoff
- **Validation Errors**: Log and mark as failed
- **Timeout Handling**: 5-minute timeout per generation
- **Rate Limiting**: Respect Claude API rate limits

#### Performance Metrics
- **Average Processing Time**: 30-60 seconds
- **Success Rate**: Target 95%+
- **Retry Rate**: Target <10%

### 2. Chat Processing Worker

#### Purpose
- Handle real-time chat interactions with AI
- Process formulation modifications and optimizations
- Manage chat session state and context

#### Pipeline Steps
1. **Message Parsing**: Parse and sanitize user message
2. **Intent Analysis**: Analyze user intent and extract entities
3. **Session Loading**: Load chat session and conversation history
4. **Prompt Building**: Build context-aware chat prompt
5. **Claude Processing**: Process with Claude API
6. **Formulation Update**: Apply changes to formulation
7. **Session Save**: Update session state and history
8. **Real-time Response**: Send response via WebSocket

#### Session Management
- **Session Timeout**: 30 minutes of inactivity
- **Context Size**: Maximum 50 messages per session
- **Memory Management**: Automatic cleanup of old sessions
- **Concurrent Sessions**: Support multiple sessions per user

#### Features
- **Intent Classification**: Modify, optimize, explain, compare
- **Entity Extraction**: Ingredients, quantities, properties
- **Context Awareness**: Maintain conversation context
- **Real-time Updates**: WebSocket-based communication

### 3. Export Generation Worker

#### Purpose
- Generate formulation exports in various formats
- Handle PDF generation and file management
- Provide download links and notifications

#### Pipeline Steps
1. **Data Loading**: Load complete formulation data
2. **Template Selection**: Choose appropriate export template
3. **Content Generation**: Generate formatted content
4. **Format Processing**: Convert to PDF, JSON, or other formats
5. **File Storage**: Save export file to storage system
6. **Link Generation**: Create secure download link
7. **Notification**: Notify user of completion

#### Export Formats
- **PDF**: Formatted formulation report with charts
- **JSON**: Structured data export for integration
- **CSV**: Ingredient lists and nutritional data
- **XML**: Regulatory compliance formats

#### File Management
- **Storage**: Temporary file storage with TTL
- **Security**: Signed URLs for secure downloads
- **Cleanup**: Automatic cleanup of expired files
- **Compression**: Automatic compression for large exports

### 4. Cleanup & Maintenance Worker

#### Purpose
- Perform system maintenance and cleanup tasks
- Monitor system health and performance
- Manage data retention and archival

#### Scheduled Tasks
- **Session Cleanup**: Remove expired chat sessions (every 5 minutes)
- **Job Cleanup**: Archive completed jobs older than 30 days (daily)
- **File Cleanup**: Remove temporary files older than 24 hours (hourly)
- **Metrics Update**: Update system performance metrics (every 15 minutes)
- **Health Check**: System health monitoring (every minute)

#### Maintenance Operations
- **Database Optimization**: Index maintenance and query optimization
- **Log Rotation**: Rotate and compress log files
- **Cache Cleanup**: Clear expired cache entries
- **Backup Verification**: Verify backup integrity

#### Monitoring
- **System Metrics**: CPU, memory, disk usage
- **Database Metrics**: Connection pool, query performance
- **API Metrics**: Response times, error rates
- **Queue Metrics**: Job processing rates, queue depth

### 5. Notification Worker

#### Purpose
- Handle all system notifications and communications
- Support multiple notification channels
- Track delivery status and retry failed notifications

#### Notification Types
- **Formulation Complete**: Project generation finished
- **Export Ready**: Export file available for download
- **System Alerts**: Error notifications and system status
- **User Notifications**: Account and project updates

#### Channels
- **Email**: SMTP-based email notifications
- **WebSocket**: Real-time browser notifications
- **Push Notifications**: Mobile app notifications (future)
- **Webhook**: External system integrations

#### Features
- **Template System**: Customizable notification templates
- **Delivery Tracking**: Track open rates and engagement
- **Retry Logic**: Retry failed deliveries with backoff
- **Unsubscribe**: User preference management

## Background Task Scheduling

### Cron-like Scheduling
```javascript
// Example scheduling configuration
const schedules = {
  'session-cleanup': '*/5 * * * *',      // Every 5 minutes
  'job-cleanup': '0 2 * * *',            // Daily at 2 AM
  'file-cleanup': '0 * * * *',           // Every hour
  'metrics-update': '*/15 * * * *',      // Every 15 minutes
  'health-check': '* * * * *',           // Every minute
  'backup-verify': '0 3 * * 0'           // Weekly on Sunday at 3 AM
};
```

### Dynamic Scheduling
- **Load-based**: Adjust frequency based on system load
- **Priority-based**: Higher priority tasks get more resources
- **Dependency-aware**: Respect task dependencies
- **Resource-aware**: Consider available system resources

## Monitoring and Alerting

### Worker Health Monitoring

#### Metrics Collected
- **Job Processing Rate**: Jobs processed per minute
- **Success Rate**: Percentage of successful job completions
- **Average Processing Time**: Mean time to complete jobs
- **Queue Depth**: Number of pending jobs by type
- **Worker Utilization**: Percentage of time workers are busy
- **Error Rate**: Percentage of jobs that fail
- **Retry Rate**: Percentage of jobs that require retries

#### Health Checks
- **Worker Heartbeat**: Regular health pings from workers
- **Database Connectivity**: MongoDB connection status
- **External API Status**: Claude API availability
- **Resource Usage**: CPU, memory, disk utilization
- **Queue Status**: Queue depth and processing rates

#### Alert Conditions
- **High Error Rate**: >5% job failure rate
- **Queue Backup**: >100 pending jobs for >10 minutes
- **Worker Failure**: Worker not responding for >2 minutes
- **API Failure**: External API errors >10% for >5 minutes
- **Resource Exhaustion**: CPU >90% or memory >95% for >5 minutes

### Log Aggregation

#### Log Sources
- **Worker Logs**: Job processing logs and errors
- **Queue Logs**: Job lifecycle and status changes
- **API Logs**: External API calls and responses
- **System Logs**: System events and health checks

#### Log Structure
```javascript
{
  timestamp: '2024-01-15T10:30:00Z',
  level: 'info|warn|error',
  service: 'formulation-worker',
  jobId: 'job-uuid',
  workerId: 'worker-1',
  message: 'Job completed successfully',
  duration: 45000,
  metadata: {
    projectId: 'project-uuid',
    userId: 'user-uuid',
    jobType: 'formulation_generation'
  }
}
```

#### Log Analysis
- **Error Pattern Detection**: Identify recurring error patterns
- **Performance Analysis**: Track processing time trends
- **Usage Analytics**: Monitor job type distribution
- **Capacity Planning**: Predict resource needs

## Error Handling and Recovery

### Error Classification

#### Retryable Errors
- **Network Timeouts**: API call timeouts
- **Rate Limiting**: API rate limit exceeded
- **Temporary Failures**: Database connection issues
- **Resource Constraints**: Temporary resource unavailability

#### Permanent Errors
- **Invalid Input**: Malformed job payload
- **Authentication Errors**: Invalid API credentials
- **Schema Violations**: Data validation failures
- **Business Logic Errors**: Invalid formulation parameters

### Recovery Strategies

#### Automatic Recovery
- **Exponential Backoff**: Increasing delays between retries
- **Circuit Breaker**: Temporary disable failing services
- **Graceful Degradation**: Fallback to simplified processing
- **Load Shedding**: Drop low-priority jobs under high load

#### Manual Recovery
- **Dead Letter Queue**: Manual review of failed jobs
- **Job Replay**: Reprocess failed jobs after fixes
- **Data Repair**: Fix corrupted data and retry
- **System Restart**: Restart workers after configuration changes

## Performance Optimization

### Queue Optimization
- **Compound Indexes**: Optimize job queries with proper indexing
- **Batch Processing**: Process multiple jobs in batches
- **Connection Pooling**: Reuse database connections
- **Query Optimization**: Efficient job claiming queries

### Worker Optimization
- **Connection Reuse**: Reuse HTTP connections to external APIs
- **Caching**: Cache frequently accessed data
- **Parallel Processing**: Process independent tasks in parallel
- **Memory Management**: Efficient memory usage and cleanup

### Scaling Strategies
- **Horizontal Scaling**: Add more worker instances
- **Vertical Scaling**: Increase worker resources
- **Load Balancing**: Distribute jobs across workers
- **Auto-scaling**: Automatically adjust worker count based on load

## Security Considerations

### Job Security
- **Input Validation**: Validate all job payloads
- **Access Control**: Verify user permissions for jobs
- **Data Encryption**: Encrypt sensitive job data
- **Audit Logging**: Log all job operations for audit

### Worker Security
- **Secure Communication**: Use TLS for all external communications
- **Credential Management**: Secure storage of API keys and secrets
- **Process Isolation**: Isolate worker processes
- **Resource Limits**: Prevent resource exhaustion attacks

### Data Protection
- **PII Handling**: Secure handling of personally identifiable information
- **Data Retention**: Automatic cleanup of sensitive data
- **Backup Security**: Encrypted backups with access controls
- **Compliance**: GDPR, HIPAA, and other regulatory compliance
