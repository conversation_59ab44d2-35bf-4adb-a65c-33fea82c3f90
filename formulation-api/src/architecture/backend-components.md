# Backend API Component Architecture Documentation

## Server Configuration

### server.js
- **Framework**: Express.js 5.1.0
- **Port**: 7700 (configurable via PORT env)
- **Middleware Stack**: CORS, Helmet, Rate Limiting, Body Parser, <PERSON>rro<PERSON> Handler
- **Features**: Graceful shutdown, health checks, request logging, MongoDB connection management
- **Security**: Rate limiting, CORS policy, security headers via Helmet

## Route Architecture

### Authentication Routes (/api/auth)
- **File**: `src/routes/auth.js`
- **Controller**: `authController.js`
- **Endpoints**:
  - `POST /login` - User authentication with JWT generation
  - `POST /register` - User registration (admin-only in production)
  - `POST /refresh` - JWT token refresh
  - `POST /logout` - Session termination and token invalidation
  - `GET /me` - Current user profile retrieval
  - `GET /sessions` - User session management
  - `DELETE /sessions/:sessionId` - Session revocation
- **Middleware**: JWT verification for protected endpoints
- **Features**: Session management, token refresh, multi-session support

### Project Routes (/api/projects)
- **File**: `src/routes/projects.js`
- **Endpoints**:
  - `GET /` - List user projects with pagination and filtering
  - `POST /` - Create new project with taxonomy validation
  - `GET /:id` - Get project details with formulation data
  - `PUT /:id` - Update project metadata and configuration
  - `DELETE /:id` - Soft delete project (archive)
- **Features**: User isolation, project validation, status management, job queue integration
- **Validation**: Dynamic taxonomy validation, goal validation

### Formulation Routes (/api/formulations)
- **File**: `src/routes/formulations.js`
- **Endpoints**:
  - `POST /generate` - AI formulation generation with queue system
  - `GET /:projectId` - Get project formulations and version history
  - `PUT /:projectId` - Update formulation with versioning
  - `POST /:id/export` - Export formulation (PDF, JSON formats)
- **Features**: Version control, quality scoring, validation, export capabilities
- **Integration**: Claude AI service, schema validation, job queue

### Playground Routes (/api/playground)
- **File**: `src/routes/playground.js`
- **Endpoints**:
  - `POST /chat` - AI chat interaction with context management
  - `PUT /modify` - Real-time formulation modifications
  - `GET /quality-score` - Calculate quality metrics and recommendations
  - `POST /session` - Create chat session with formulation context
- **Features**: Session management, context control, real-time updates, quality assessment
- **AI Integration**: ChatService, IntentAnalyzer, template-based prompts

### Configuration Routes (/api/configuration)
- **File**: `src/routes/configuration.js`
- **Endpoints**:
  - `GET /taxonomies` - Taxonomy hierarchy retrieval
  - `GET /taxonomies/:slug` - Specific taxonomy with full hierarchy
  - `GET /param-bindings` - Parameter bindings for taxonomy levels
  - `GET /param-answers` - Answer sets for parameters
  - `GET /ui-flows` - UI flow configurations
  - `POST /guardrails/evaluate` - Guardrail evaluation
  - `GET /guardrails` - Applicable guardrails retrieval
- **Features**: Dynamic configuration, caching, validation, hierarchical data

### Admin Routes (/api/admin)
- **File**: `src/routes/admin.js`
- **Access**: Admin role required
- **Endpoints**:
  - CRUD operations for taxonomies, parameters, bindings, answers
  - Bulk operations for data management
  - Validation and schema enforcement
- **Features**: Role-based access control, data validation, audit logging

### Formulation Blocks Routes (/api/formulation-blocks)
- **File**: `src/routes/formulation-blocks.js`
- **Access**: Admin role required
- **Endpoints**:
  - `GET /` - List formulation blocks with filtering
  - `POST /` - Create new formulation block
  - `PUT /:id` - Update formulation block
  - `DELETE /:id` - Delete formulation block
  - Binding management endpoints
- **Features**: Block CRUD, binding management, schema validation, dependency tracking

### Logging Routes (/api/logs)
- **File**: `src/routes/logs.js`
- **Endpoints**:
  - `POST /frontend` - Frontend log ingestion
  - `GET /frontend` - Frontend log retrieval for debugging
- **Features**: Log aggregation, filtering, structured logging

## Controller Layer

### authController.js
- **Methods**: `login()`, `logout()`, `validateToken()`, `refreshToken()`, `register()`, `me()`
- **Validation**: Email format, password strength, domain restrictions
- **Security**: BCrypt hashing (12 rounds), JWT generation, session management
- **Features**: Multi-session support, token refresh, secure logout

## Service Layer

### Core Services

#### ChatService.js
- **Purpose**: LLM-powered formulation optimization chat
- **Methods**: `processMessage()`, `upgradeFormulation()`, `analyzeIntent()`
- **Features**: Template-based prompts, forced upgrade behavior, context management
- **Integration**: Anthropic Claude API, IntentAnalyzer, PromptBuilder
- **Templates**: Multiple prompt templates for different interaction types

#### ChatSessionManager.js
- **Purpose**: Chat session lifecycle management
- **Methods**: `createSession()`, `sendMessage()`, `updateContext()`, `cleanupSessions()`
- **Features**: Session persistence, context tracking, automatic cleanup
- **Storage**: In-memory with Redis option for production
- **Timeout**: 30-minute session timeout with cleanup

#### formulation.service.js
- **Purpose**: Formulation generation and management
- **Methods**: `generateFormulation()`, `updateFormulation()`, `calculateQuality()`
- **Features**: AI integration, quality scoring, version management
- **Validation**: Schema validation, ingredient compatibility, regulatory compliance

#### claudeService.js
- **Purpose**: Anthropic Claude API integration
- **Methods**: `generateFormulation()`, `chatInteraction()`, `validateResponse()`
- **Features**: Prompt templates, response parsing, error handling, schema validation
- **Rate Limiting**: API quota management, retry logic
- **Models**: Claude 3.5 Sonnet integration

### Configuration Services

#### taxonomies.service.js
- **Purpose**: Taxonomy hierarchy management
- **Methods**: `listByLevel()`, `getBySlug()`, `search()`, `getFullHierarchy()`
- **Features**: Hierarchical data retrieval, caching, search functionality
- **Performance**: Indexed queries, result caching

#### parameters.service.js
- **Purpose**: Parameter and binding management
- **Methods**: `getBindings()`, `getAnswerSets()`, `validateParameters()`
- **Features**: Dynamic parameter loading, validation, binding resolution
- **Caching**: Parameter and answer set caching

#### guardrails.service.js
- **Purpose**: Regulatory and safety guardrail evaluation
- **Methods**: `evaluate()`, `getApplicableRules()`, `testRule()`
- **Features**: Rule engine integration, context evaluation, issue reporting
- **Engine**: Custom guardrail engine with rule evaluation

#### uiFlows.service.js
- **Purpose**: UI flow configuration management
- **Methods**: `getFlowConfig()`, `validateFlow()`, `updateFlow()`
- **Features**: Dynamic UI configuration, flow validation

### Infrastructure Services

#### queue.service.js
- **Purpose**: Job queue management for background processing
- **Methods**: `enqueue()`, `claimNext()`, `complete()`, `fail()`, `retry()`
- **Features**: Priority queuing, retry logic, exponential backoff, job monitoring
- **Storage**: MongoDB-based job queue with indexing
- **Workers**: Support for multiple worker types

#### IntentAnalyzer.js
- **Purpose**: User intent analysis for chat interactions
- **Methods**: `analyzeIntent()`, `extractEntities()`, `classifyRequest()`
- **Features**: Intent classification, entity extraction, confidence scoring

#### schemaBuilder.js
- **Purpose**: Dynamic schema generation for formulations
- **Methods**: `buildSchema()`, `validateStructure()`, `generateConstraints()`
- **Features**: Dynamic schema creation, validation rule generation

## Data Models (MongoDB/Mongoose)

### User.js
- **Fields**: `email`, `password_hash`, `first_name`, `last_name`, `company`, `role`
- **Indexes**: Email (unique), created_at, last_login
- **Methods**: `validatePassword()`, `createUser()`, `findByEmail()`
- **Features**: Password hashing, role-based access, preferences management
- **Security**: Password validation, secure token generation

### Project.js
- **Fields**: `user_id`, `name`, `industry`, `status`, `current_formulation`, `goals`
- **Embedded Documents**: Formulation data, conversation history, version history
- **Methods**: `addVersion()`, `updateFormulation()`, `calculateQuality()`
- **Features**: Version control, quality tracking, embedded formulations
- **Indexes**: User ID, status, generation job ID

### Session.js
- **Fields**: `session_id`, `user_id`, `data`, `expires_at`
- **TTL Index**: Automatic cleanup of expired sessions
- **Methods**: `isValid()`, `extend()`, `createSession()`, `cleanupExpiredSessions()`
- **Features**: Session persistence, automatic expiration

### FormulationBlock.js
- **Fields**: `key`, `name`, `description`, `schema`, `category`, `is_required`
- **Methods**: Block validation, dependency checking
- **Features**: Schema validation, categorization, dependency management
- **Types**: Ingredients, analysis, compliance, manufacturing, sustainability

### FormulationBinding.js
- **Fields**: `level`, `slug`, `block_key`, `is_included`, `is_required`, `display_order`
- **Methods**: Binding validation, hierarchy management
- **Features**: Taxonomy-block relationships, ordering, conditional inclusion

## Middleware Components

### auth.js
- **Functions**: `verifyToken()`, `requireAdmin()`, `extractUser()`
- **Features**: JWT validation, role-based access control, user context injection
- **Security**: Token expiration checking, role validation

### errorHandler.js
- **Functions**: `globalErrorHandler()`, `notFoundHandler()`, `validationErrorHandler()`
- **Features**: Centralized error handling, error logging, client-safe error responses
- **Logging**: Structured error logging with context

## Schema & Validation

### formulationSchema.json
- **Purpose**: JSON schema for formulation validation
- **Sections**: Recipes, ingredients, nutritional profiles, cost analysis
- **Validation**: AJV-based validation with custom rules

### schemas.js
- **Functions**: `validateTaxonomy()`, `validateParameter()`, `validateBinding()`
- **Features**: Input validation, data sanitization, error reporting

## Template System

### chat-prompts.js
- **Class**: `PromptBuilder`
- **Methods**: `buildPrompt()`, `injectContext()`, `selectTemplate()`
- **Templates**: Multiple prompt templates for different interaction types
- **Features**: Context injection, template selection, dynamic prompts

### formulationPrompt.md
- **Purpose**: Base formulation generation prompt template
- **Features**: Structured prompts, context placeholders, output formatting

### templateEngine.js
- **Functions**: `renderTemplate()`, `injectVariables()`, `validateTemplate()`
- **Features**: Template rendering, variable injection, template validation

## External Integrations

### Anthropic Claude API
- **Model**: Claude 3.5 Sonnet
- **Features**: Text generation, conversation, structured output
- **Rate Limiting**: API quota management, retry logic
- **Error Handling**: Comprehensive error handling and fallbacks

### MongoDB 8.16.4
- **ODM**: Mongoose 8.16.4
- **Features**: Document modeling, indexing, aggregation
- **Connection**: Connection pooling, automatic reconnection

### Winston Logger 3.17.0
- **Features**: Structured logging, multiple transports, log levels
- **Transports**: File, console, external logging services
- **Formatting**: JSON formatting, timestamp injection

## Performance Optimizations

### Caching Strategy
- **Configuration Data**: In-memory caching with TTL
- **API Responses**: Response caching for static data
- **Session Data**: Session caching for performance

### Database Optimization
- **Indexing**: Strategic indexing for query performance
- **Aggregation**: MongoDB aggregation pipelines for complex queries
- **Connection Pooling**: Optimized connection management

### Queue Processing
- **Background Jobs**: Asynchronous processing for heavy operations
- **Priority Queuing**: Priority-based job processing
- **Retry Logic**: Exponential backoff for failed jobs

## Security Measures

### Authentication & Authorization
- **JWT Tokens**: Secure token-based authentication
- **Role-Based Access**: Granular permission system
- **Session Management**: Secure session handling

### Data Protection
- **Input Validation**: Comprehensive input sanitization
- **SQL Injection Prevention**: Parameterized queries
- **Rate Limiting**: API rate limiting and abuse prevention

### Security Headers
- **Helmet.js**: Security headers implementation
- **CORS**: Cross-origin resource sharing configuration
- **HTTPS**: SSL/TLS encryption support
