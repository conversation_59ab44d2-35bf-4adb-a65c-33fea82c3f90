const { User, Session } = require('../models');
const { generateToken, validateEmailDomain } = require('../middleware/auth');
const logger = require('../utils/logger');
const Joi = require('joi');

// Validation schemas
const loginSchema = Joi.object({
  email: Joi.string().email().required().max(255),
  password: Joi.string().required().min(6).max(255)
});

const registerSchema = Joi.object({
  email: Joi.string().email().required().max(255),
  password: Joi.string().required().min(8).max(255)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]'))
    .message('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  first_name: Jo<PERSON>.string().optional().max(100),
  last_name: Joi.string().optional().max(100)
});

class AuthController {
  // Login user
  static async login(req, res) {
    try {
      // Validate request
      const { error, value } = loginSchema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message
        });
      }

      const { email, password } = value;

      // Validate email domain
      if (!validateEmailDomain(email)) {
        logger.logAuth('login_failed', null, email, false, {
          reason: 'invalid_domain',
          ip: req.ip
        });
        
        return res.status(400).json({
          success: false,
          error: 'Email domain not allowed. Only agrizy.in, agrizywellness.com, and naturalzy.com domains are permitted.'
        });
      }

      // Find user by email
      const user = await User.findByEmail(email);
      if (!user) {
        logger.logAuth('login_failed', null, email, false, {
          reason: 'user_not_found',
          ip: req.ip
        });
        
        return res.status(401).json({
          success: false,
          error: 'Invalid email or password'
        });
      }

      // Check if user is active
      if (!user.is_active) {
        logger.logAuth('login_failed', user._id, email, false, {
          reason: 'account_inactive',
          ip: req.ip
        });
        
        return res.status(401).json({
          success: false,
          error: 'Account is inactive. Please contact administrator.'
        });
      }

      // Validate password
      const isValidPassword = await user.validatePassword(password);
      if (!isValidPassword) {
        logger.logAuth('login_failed', user._id, email, false, {
          reason: 'invalid_password',
          ip: req.ip
        });
        
        return res.status(401).json({
          success: false,
          error: 'Invalid email or password'
        });
      }

      // Create session
      const session = await Session.createSession(
        user._id,
        req.ip,
        req.get('User-Agent')
      );

      // Generate JWT
      const token = generateToken({
        userId: user._id,
        email: user.email,
        sessionId: session.session_id  // Use session_id (UUID) not MongoDB _id
      });

      // Update user login stats
      await user.updateLastLogin();

      logger.logAuth('login_success', user._id, email, true, {
        sessionId: session.session_id,  // Use session_id (UUID) not MongoDB _id
        ip: req.ip
      });

      res.status(200).json({
        success: true,
        message: 'Login successful',
        data: {
          user: {
            id: user._id,
            email: user.email,
            first_name: user.first_name,
            last_name: user.last_name,
            company: user.company,
            role: user.role
          },
          token,
          refresh_token: token, // For compatibility with frontend
          expires_at: session.expires_at
        }
      });

    } catch (error) {
      logger.error('Login error:', {
        error: error.message,
        stack: error.stack,
        ip: req.ip
      });
      
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Register new user (admin only for now)
  static async register(req, res) {
    try {
      // Validate request
      const { error, value } = registerSchema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message
        });
      }

      const { email, password, first_name, last_name } = value;

      // Validate email domain
      if (!validateEmailDomain(email)) {
        return res.status(400).json({
          success: false,
          error: 'Email domain not allowed. Only agrizy.in, agrizywellness.com, and naturalzy.com domains are permitted.'
        });
      }

      // Check if user already exists
      const existingUser = await User.findByEmail(email);
      if (existingUser) {
        return res.status(409).json({
          success: false,
          error: 'User with this email already exists'
        });
      }

      // Create user
      const user = await User.createUser({
        email,
        password,
        first_name,
        last_name
      });

      logger.info('User registered successfully', {
        userId: user.id,
        email: user.email,
        ip: req.ip
      });

      res.status(201).json({
        success: true,
        message: 'User registered successfully',
        data: {
          user: user.toJSON()
        }
      });

    } catch (error) {
      logger.error('Registration error:', {
        error: error.message,
        stack: error.stack,
        ip: req.ip
      });
      
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Logout user
  static async logout(req, res) {
    try {
      if (req.session) {
        await req.session.revoke();
        
        logger.logAuth('logout_success', req.user.id, req.user.email, true, {
          sessionId: req.session.id,
          ip: req.ip
        });
      }

      res.status(200).json({
        success: true,
        message: 'Logout successful'
      });

    } catch (error) {
      logger.error('Logout error:', {
        error: error.message,
        userId: req.user?.id,
        ip: req.ip
      });
      
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get current user info
  static async me(req, res) {
    try {
      // User is already attached by auth middleware
      res.status(200).json({
        success: true,
        data: {
          user: req.user.toJSON(),
          session: {
            id: req.session.id,
            expires_at: req.session.expires_at,
            last_used: req.session.last_used
          }
        }
      });

    } catch (error) {
      logger.error('Me endpoint error:', {
        error: error.message,
        userId: req.user?.id,
        ip: req.ip
      });
      
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Refresh token
  static async refresh(req, res) {
    try {
      const { refresh_token } = req.body;

      if (!refresh_token) {
        return res.status(400).json({
          success: false,
          error: 'Refresh token required'
        });
      }

      // Find session by refresh token
      const session = await Session.findByRefreshToken(refresh_token);
      
      if (!session || session.isRefreshExpired()) {
        return res.status(401).json({
          success: false,
          error: 'Invalid or expired refresh token'
        });
      }

      // Create new session
      const newSession = await Session.createSession(
        session.user_id,
        req.ip,
        req.get('User-Agent')
      );

      // Revoke old session
      await session.revoke();

      // Generate new JWT
      const token = generateToken({
        userId: session.user.id,
        email: session.user.email,
        sessionId: newSession.id
      });

      logger.logAuth('token_refresh', session.user.id, session.user.email, true, {
        oldSessionId: session.id,
        newSessionId: newSession.id,
        ip: req.ip
      });

      res.status(200).json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          token,
          refresh_token: newSession.refresh_token,
          expires_at: newSession.expires_at
        }
      });

    } catch (error) {
      logger.error('Token refresh error:', {
        error: error.message,
        stack: error.stack,
        ip: req.ip
      });
      
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get user's active sessions
  static async sessions(req, res) {
    try {
      const sessions = await Session.getUserActiveSessions(req.user.id);

      res.status(200).json({
        success: true,
        data: {
          sessions,
          current_session_id: req.session.id
        }
      });

    } catch (error) {
      logger.error('Sessions endpoint error:', {
        error: error.message,
        userId: req.user?.id,
        ip: req.ip
      });
      
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Revoke specific session
  static async revokeSession(req, res) {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        return res.status(400).json({
          success: false,
          error: 'Session ID required'
        });
      }

      const session = await Session.findOne({
        _id: sessionId,
        user_id: req.user._id,
        expires_at: { $gt: new Date() }
      });

      if (!session) {
        return res.status(404).json({
          success: false,
          error: 'Session not found'
        });
      }

      await session.revoke();

      logger.logAuth('session_revoked', req.user.id, req.user.email, true, {
        revokedSessionId: sessionId,
        currentSessionId: req.session.id,
        ip: req.ip
      });

      res.status(200).json({
        success: true,
        message: 'Session revoked successfully'
      });

    } catch (error) {
      logger.error('Revoke session error:', {
        error: error.message,
        userId: req.user?.id,
        ip: req.ip
      });
      
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}

module.exports = AuthController;