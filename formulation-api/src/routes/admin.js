const express = require('express');
const { ObjectId } = require('mongodb');
const { verifyToken, requireAdmin } = require('../middleware/auth');
const {
  validateTaxonomy,
  validateParameter,
  validateParamBinding,
  validateParamAnswer
} = require('../models/schemas');

function initializeAdminRoutes(db) {
  const router = express.Router();

  const collections = {
    taxonomies: db.collection('taxonomies'),
    parameters: db.collection('parameters'),
    bindings: db.collection('param_bindings'),
    answers: db.collection('param_answers')
  };

  const validators = {
    taxonomies: validateTaxonomy,
    parameters: validateParameter,
    bindings: validateParamBinding,
    answers: validateParamAnswer
  };

  const ensureValid = (type, payload) => {
    const v = validators[type](payload);
    if (!v.valid) {
      const err = new Error(v.errors.join(', '));
      err.status = 400;
      throw err;
    }
  };

  // Generic error handler wrapper
  const wrap = (fn) => async (req, res) => {
    try {
      await fn(req, res);
    } catch (e) {
      res.status(e.status || 500).json({ success: false, error: e.message || 'Server error' });
    }
  };

  // TAXONOMIES
  router.get('/taxonomies', verifyToken, requireAdmin, wrap(async (req, res) => {
    const { level, parent_id, q } = req.query;
    const filter = {};
    if (level) filter.level = level;
    if (parent_id) filter.parent_id = parent_id;
    if (q) filter.name = { $regex: q, $options: 'i' };
    const data = await collections.taxonomies.find(filter).sort({ name: 1 }).toArray();
    res.json({ success: true, data });
  }));

  router.post('/taxonomies', verifyToken, requireAdmin, wrap(async (req, res) => {
    const payload = req.body;
    ensureValid('taxonomies', payload);
    payload.created_at = new Date();
    payload.updated_at = new Date();
    const result = await collections.taxonomies.insertOne(payload);
    res.json({ success: true, data: { _id: result.insertedId, ...payload } });
  }));

  router.put('/taxonomies/:id', verifyToken, requireAdmin, wrap(async (req, res) => {
    const id = new ObjectId(req.params.id);
    const payload = { ...req.body, updated_at: new Date() };
    ensureValid('taxonomies', { ...payload, _id: id });
    await collections.taxonomies.updateOne({ _id: id }, { $set: payload });
    res.json({ success: true, data: { _id: id, ...payload } });
  }));

  router.delete('/taxonomies/:id', verifyToken, requireAdmin, wrap(async (req, res) => {
    const id = new ObjectId(req.params.id);
    await collections.taxonomies.deleteOne({ _id: id });
    res.json({ success: true, data: { _id: id } });
  }));

  // PARAMETERS
  router.get('/parameters', verifyToken, requireAdmin, wrap(async (req, res) => {
    const { q } = req.query;
    const filter = q ? { name: { $regex: q, $options: 'i' } } : {};
    const data = await collections.parameters.find(filter).sort({ name: 1 }).toArray();
    res.json({ success: true, data });
  }));

  router.post('/parameters', verifyToken, requireAdmin, wrap(async (req, res) => {
    const payload = req.body;
    ensureValid('parameters', payload);
    payload.created_at = new Date();
    payload.updated_at = new Date();
    const result = await collections.parameters.insertOne(payload);
    res.json({ success: true, data: { _id: result.insertedId, ...payload } });
  }));

  router.put('/parameters/:id', verifyToken, requireAdmin, wrap(async (req, res) => {
    const id = new ObjectId(req.params.id);
    const payload = { ...req.body, updated_at: new Date() };
    ensureValid('parameters', { ...payload });
    await collections.parameters.updateOne({ _id: id }, { $set: payload });
    res.json({ success: true, data: { _id: id, ...payload } });
  }));

  router.delete('/parameters/:id', verifyToken, requireAdmin, wrap(async (req, res) => {
    const id = new ObjectId(req.params.id);
    await collections.parameters.deleteOne({ _id: id });
    res.json({ success: true, data: { _id: id } });
  }));

  // PARAM BINDINGS
  router.get('/bindings', verifyToken, requireAdmin, wrap(async (req, res) => {
    const { level, slug, parameter_key } = req.query;
    const filter = {};
    if (level) filter['target.level'] = level;
    if (slug) filter['target.slug'] = slug;
    if (parameter_key) filter.parameter_key = parameter_key;
    const data = await collections.bindings.find(filter).sort({ 'display.order': 1 }).toArray();
    res.json({ success: true, data });
  }));

  router.post('/bindings', verifyToken, requireAdmin, wrap(async (req, res) => {
    const payload = req.body;
    ensureValid('bindings', payload);
    payload.created_at = new Date();
    payload.updated_at = new Date();
    const result = await collections.bindings.insertOne(payload);
    res.json({ success: true, data: { _id: result.insertedId, ...payload } });
  }));

  router.put('/bindings/:id', verifyToken, requireAdmin, wrap(async (req, res) => {
    const id = new ObjectId(req.params.id);
    const payload = { ...req.body, updated_at: new Date() };
    ensureValid('bindings', { ...payload });
    await collections.bindings.updateOne({ _id: id }, { $set: payload });
    res.json({ success: true, data: { _id: id, ...payload } });
  }));

  router.delete('/bindings/:id', verifyToken, requireAdmin, wrap(async (req, res) => {
    const id = new ObjectId(req.params.id);
    await collections.bindings.deleteOne({ _id: id });
    res.json({ success: true, data: { _id: id } });
  }));

  // ANSWER SETS
  router.get('/answers', verifyToken, requireAdmin, wrap(async (req, res) => {
    const { q } = req.query;
    const filter = q ? { name: { $regex: q, $options: 'i' } } : {};
    const data = await collections.answers.find(filter).sort({ name: 1 }).toArray();
    res.json({ success: true, data });
  }));

  router.post('/answers', verifyToken, requireAdmin, wrap(async (req, res) => {
    const payload = req.body;
    ensureValid('answers', payload);
    payload.created_at = new Date();
    payload.updated_at = new Date();
    const result = await collections.answers.insertOne(payload);
    res.json({ success: true, data: { _id: result.insertedId, ...payload } });
  }));

  router.put('/answers/:id', verifyToken, requireAdmin, wrap(async (req, res) => {
    const id = new ObjectId(req.params.id);
    const payload = { ...req.body, updated_at: new Date() };
    ensureValid('answers', { ...payload });
    await collections.answers.updateOne({ _id: id }, { $set: payload });
    res.json({ success: true, data: { _id: id, ...payload } });
  }));

  router.delete('/answers/:id', verifyToken, requireAdmin, wrap(async (req, res) => {
    const id = new ObjectId(req.params.id);
    await collections.answers.deleteOne({ _id: id });
    res.json({ success: true, data: { _id: id } });
  }));

  return router;
}

module.exports = initializeAdminRoutes;

