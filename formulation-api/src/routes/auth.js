const express = require('express');
const AuthController = require('../controllers/authController');
const { verifyToken } = require('../middleware/auth');

const router = express.Router();

// Public routes
router.post('/login', AuthController.login);
router.post('/register', AuthController.register); // Should be admin-only in production
router.post('/refresh', AuthController.refresh);

// Protected routes
router.post('/logout', verifyToken, AuthController.logout);
router.get('/me', verifyToken, AuthController.me);
router.get('/sessions', verifyToken, AuthController.sessions);
router.delete('/sessions/:sessionId', verifyToken, AuthController.revokeSession);

module.exports = router;