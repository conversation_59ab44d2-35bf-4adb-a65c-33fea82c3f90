/**
 * Configuration Routes
 * Handles all MongoDB-driven configuration endpoints
 */

const express = require('express');
const { verifyToken } = require('../middleware/auth');

// Services
const TaxonomiesService = require('../services/taxonomies.service');
const ParametersService = require('../services/parameters.service');
const UIFlowsService = require('../services/uiFlows.service');
const GuardrailsService = require('../services/guardrails.service');

/**
 * Initialize route with database connection
 * @param {Object} db - MongoDB database instance
 */
function initializeRoutes(db) {
  const router = express.Router();
  
  // Initialize services
  const taxonomiesService = new TaxonomiesService(db);
  const parametersService = new ParametersService(db);
  const uiFlowsService = new UIFlowsService(db);
  const guardrailsService = new GuardrailsService(db);

  /**
   * GET /api/taxonomies
   * Fetch taxonomies by level with optional parent filtering
   */
  router.get('/taxonomies', verifyToken, async (req, res) => {
    try {
      const { level, parent_id, status = 'active' } = req.query;

      if (!level) {
        return res.status(400).json({
          success: false,
          error: 'Level parameter is required'
        });
      }

      const taxonomies = await taxonomiesService.listByLevel(level, parent_id, status);

      res.json({
        success: true,
        data: taxonomies
      });
    } catch (error) {
      console.error('[Route] Error fetching taxonomies:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch taxonomies'
      });
    }
  });

  /**
   * GET /api/taxonomies/:slug
   * Get specific taxonomy by slug
   */
  router.get('/taxonomies/:slug', verifyToken, async (req, res) => {
    try {
      const { slug } = req.params;
      const { full_hierarchy } = req.query;

      const taxonomy = await taxonomiesService.getBySlug(slug);
      
      if (!taxonomy) {
        return res.status(404).json({
          success: false,
          error: 'Taxonomy not found'
        });
      }

      let result = taxonomy;
      if (full_hierarchy === 'true') {
        result = await taxonomiesService.getHierarchy(slug);
      }

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('[Route] Error fetching taxonomy:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch taxonomy'
      });
    }
  });

  /**
   * GET /api/param-bindings
   * Fetch parameter bindings for a specific taxonomy level and slug
   */
  router.get('/param-bindings', verifyToken, async (req, res) => {
    try {
      const { level, slug } = req.query;

      if (!level || !slug) {
        return res.status(400).json({
          success: false,
          error: 'Level and slug parameters are required'
        });
      }

      const bindings = await parametersService.getBindingsForTarget(level, slug);

      res.json({
        success: true,
        data: bindings
      });
    } catch (error) {
      console.error('[Route] Error fetching param bindings:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch parameter bindings'
      });
    }
  });

  /**
   * GET /api/param-answers
   * Fetch answer sets for parameters
   */
  router.get('/param-answers', verifyToken, async (req, res) => {
    try {
      const { keys } = req.query;

      if (!keys) {
        return res.status(400).json({
          success: false,
          error: 'Keys parameter is required'
        });
      }

      const keyList = keys.split(',');
      const answerSets = await parametersService.getAnswerSets(keyList);

      res.json({
        success: true,
        data: answerSets
      });
    } catch (error) {
      console.error('[Route] Error fetching param answers:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch parameter answers'
      });
    }
  });

  /**
   * GET /api/ui-flows/:slug
   * Fetch UI flow configuration
   */
  router.get('/ui-flows/:slug', verifyToken, async (req, res) => {
    try {
      const { slug } = req.params;
      
      const flow = await uiFlowsService.getBySlug(slug);

      if (!flow) {
        return res.status(404).json({
          success: false,
          error: 'UI flow not found'
        });
      }

      res.json({
        success: true,
        data: flow
      });
    } catch (error) {
      console.error('[Route] Error fetching UI flow:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch UI flow'
      });
    }
  });

  /**
   * GET /api/ui-flows/:slug/step/:stepSlug
   * Fetch specific step in a UI flow
   */
  router.get('/ui-flows/:slug/step/:stepSlug', verifyToken, async (req, res) => {
    try {
      const { slug, stepSlug } = req.params;
      
      const step = await uiFlowsService.getFlowStep(slug, stepSlug);

      if (!step) {
        return res.status(404).json({
          success: false,
          error: 'Step not found in UI flow'
        });
      }

      res.json({
        success: true,
        data: step
      });
    } catch (error) {
      console.error('[Route] Error fetching UI flow step:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch UI flow step'
      });
    }
  });

  /**
   * POST /api/guardrails/evaluate
   * Evaluate guardrails for given parameters
   */
  router.post('/guardrails/evaluate', verifyToken, async (req, res) => {
    try {
      const { industry, category, sub_category, answers, ingredients } = req.body;

      if (!industry || !category || !sub_category) {
        return res.status(400).json({
          success: false,
          error: 'Industry, category, and sub_category are required'
        });
      }

      const evaluation = await guardrailsService.evaluate({
        industry,
        category,
        sub_category,
        answers: answers || {},
        ingredients: ingredients || []
      });

      res.json({
        success: true,
        data: evaluation
      });
    } catch (error) {
      console.error('[Route] Error evaluating guardrails:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to evaluate guardrails'
      });
    }
  });

  /**
   * GET /api/guardrails
   * Fetch applicable guardrails
   */
  router.get('/guardrails', verifyToken, async (req, res) => {
    try {
      const { industry, category, sub_category } = req.query;

      if (!industry || !category || !sub_category) {
        return res.status(400).json({
          success: false,
          error: 'Industry, category, and sub_category are required'
        });
      }

      const rules = await guardrailsService.getApplicableRules(
        industry,
        category,
        sub_category
      );

      res.json({
        success: true,
        data: rules
      });
    } catch (error) {
      console.error('[Route] Error fetching guardrails:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch guardrails'
      });
    }
  });

  /**
   * GET /api/taxonomies/search
   * Search taxonomies by query
   */
  router.get('/taxonomies/search', verifyToken, async (req, res) => {
    try {
      const { query, level } = req.query;

      if (!query) {
        return res.status(400).json({
          success: false,
          error: 'Query parameter is required'
        });
      }

      const results = await taxonomiesService.search(query, level);

      res.json({
        success: true,
        data: results
      });
    } catch (error) {
      console.error('[Route] Error searching taxonomies:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to search taxonomies'
      });
    }
  });

  return router;
}

module.exports = initializeRoutes;