const express = require('express');
const { verifyToken } = require('../middleware/auth');
const logger = require('../utils/logger');
const { Project } = require('../models');
const QueueService = require('../services/queue.service');
const { mongoose } = require('../config/mongodb');

const router = express.Router();

// Helper: validate taxonomy hierarchy dynamically from DB (no hardcoded enums)
async function validateTaxonomyChain(db, { industry, category, sub_category }) {
  const taxCol = db.collection('taxonomies');

  // custom flows bypass validation
  if (industry === 'custom' || category === 'custom' || sub_category === 'custom') {
    return { ok: true };
  }

  // Validate industry
  const industryDoc = await taxCol.findOne({ level: 'industry', slug: industry, status: { $ne: 'inactive' } });
  if (!industryDoc) return { ok: false, error: 'Invalid industry slug' };

  // If category provided, validate parent
  let categoryDoc = null;
  if (category) {
    categoryDoc = await taxCol.findOne({ level: 'category', slug: category, status: { $ne: 'inactive' } });
    if (!categoryDoc || String(categoryDoc.parent_id) !== String(industryDoc._id)) {
      return { ok: false, error: 'Invalid category for the given industry' };
    }
  }

  // If sub_category provided, validate parent
  if (sub_category) {
    const subDoc = await taxCol.findOne({ level: 'sub_category', slug: sub_category, status: { $ne: 'inactive' } });
    if (!subDoc) return { ok: false, error: 'Invalid product type (sub_category) slug' };

    // If category known, ensure it matches; else derive
    if (categoryDoc) {
      if (String(subDoc.parent_id) !== String(categoryDoc._id)) {
        return { ok: false, error: 'Product type does not belong to the selected category' };
      }
    } else {
      // Derive category from sub_category and ensure it belongs to industry
      const parentCategory = await taxCol.findOne({ _id: subDoc.parent_id });
      if (!parentCategory || String(parentCategory.parent_id) !== String(industryDoc._id)) {
        return { ok: false, error: 'Product type does not belong to the selected industry' };
      }
    }
  }

  return { ok: true };
}

// All project routes require authentication
router.use(verifyToken);

// Get all projects for the authenticated user
router.get('/', async (req, res) => {
  try {
    const userId = req.user._id;
    const { limit = 10, offset = 0, status } = req.query;

    const query = { user_id: userId };
    if (status) {
      query.status = status;
    }

    const projects = await Project.find(query)
      .sort({ updated_at: -1 })
      .limit(parseInt(limit))
      .skip(parseInt(offset))
      .lean();

    // Transform projects to include formulation summary
    const transformedProjects = projects.map(project => ({
      _id: project._id,
      name: project.name,
      description: project.description,
      industry: project.industry,
      product_type: project.product_type,
      status: project.status,
      created_at: project.created_at,
      updated_at: project.updated_at,
      last_accessed: project.last_accessed,
      current_formulation: project.current_formulation ? {
        version: project.current_formulation.version,
        quality_score: project.current_formulation.quality_score,
        quality_level: project.current_formulation.quality_level,
        ready_for_results: project.current_formulation.ready_for_results,
        generated_at: project.current_formulation.generated_at
      } : null
    }));

    logger.info('Projects fetched successfully', {
      userId,
      projectCount: projects.length
    });

    res.json({
      success: true,
      data: transformedProjects
    });

  } catch (error) {
    logger.error('Error fetching projects:', {
      error: error.message,
      stack: error.stack,
      userId: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Failed to fetch projects'
    });
  }
});

// Create project and enqueue generation job
router.post('/', async (req, res) => {
  try {
    const userId = req.user._id;
    const {
      name,
      description = '',
      industry,
      category,
      sub_category,
      goals = {}
    } = req.body || {};

    if (!industry || !name) {
      return res.status(400).json({ success: false, error: 'name and industry are required' });
    }

    // Dynamic taxonomy validation (no hardcoded enums)
    const v = await validateTaxonomyChain(mongoose.connection.db, { industry, category, sub_category });
    if (!v.ok) {
      return res.status(400).json({ success: false, error: v.error });
    }

    const project = new Project({
      user_id: userId,
      name,
      description,
      industry,
      product_type: sub_category || category || 'custom',
      custom_product_type: sub_category === 'custom' ? goals?.description || '' : undefined,
      goals,
      status: 'queued'
    });
    await project.save();

    const queue = new QueueService();
    await queue.ensureIndexes();
    const jobId = await queue.enqueue({
      type: 'generate_project',
      projectId: project._id,
      payload: { userId, industry, category, sub_category, goals },
      maxAttempts: 3,
      priority: 0
    });

    await Project.updateOne({ _id: project._id }, { $set: { generation_job_id: jobId, status: 'queued' } });

    logger.info('Project created and job enqueued', { userId, projectId: project._id, jobId });
    res.json({ success: true, data: { projectId: project._id, jobId } });
  } catch (error) {
    logger.error('Error creating project:', { error: error.message, stack: error.stack });
    res.status(500).json({ success: false, error: 'Failed to create project' });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const userId = req.user._id;
    const projectId = req.params.id;
    const project = await Project.findOne({ _id: projectId, user_id: userId }).lean();
    if (!project) return res.status(404).json({ success: false, error: 'Project not found' });
    res.json({ success: true, data: project });
  } catch (error) {
    res.status(500).json({ success: false, error: 'Failed to fetch project' });
  }
});

// Project status (used by overlay polling)
router.get('/:id/status', async (req, res) => {
  try {
    const userId = req.user._id;
    const projectId = req.params.id;
    const project = await Project.findOne({ _id: projectId, user_id: userId })
      .select('status generation_job_id generation_started_at generation_completed_at updated_at')
      .lean();
    if (!project) return res.status(404).json({ success: false, error: 'Project not found' });
    res.json({ success: true, data: project });
  } catch (error) {
    res.status(500).json({ success: false, error: 'Failed to fetch project status' });
  }
});

router.put('/:id', (req, res) => {
  res.json({ success: true, message: 'Update project endpoint - coming soon' });
});

router.delete('/:id', async (req, res) => {
  try {
    const userId = req.user._id;
    const projectId = req.params.id;

    const project = await Project.findOne({ _id: projectId, user_id: userId });
    
    if (!project) {
      return res.status(404).json({ 
        success: false, 
        error: 'Project not found' 
      });
    }

    // Remove any queued or processing jobs for this project
    const queue = new QueueService();
    const removedJobsCount = await queue.removeByProjectId(projectId);

    await Project.deleteOne({ _id: projectId, user_id: userId });

    logger.info('Project deleted successfully', { 
      userId, 
      projectId, 
      projectName: project.name, 
      removedJobs: removedJobsCount 
    });

    res.json({ 
      success: true, 
      message: 'Project deleted successfully' 
    });
  } catch (error) {
    logger.error('Error deleting project:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to delete project' 
    });
  }
});

// POST /:id/retry - Retry a failed project
router.post('/:id/retry', async (req, res) => {
  try {
    const userId = req.user._id;
    const projectId = req.params.id;

    const project = await Project.findOne({ _id: projectId, user_id: userId });
    
    if (!project) {
      return res.status(404).json({ 
        success: false, 
        error: 'Project not found' 
      });
    }

    // Only allow retry for failed projects
    if (project.status !== 'failed') {
      return res.status(400).json({ 
        success: false, 
        error: 'Only failed projects can be retried' 
      });
    }

    // Reset project status to queued for regeneration
    project.status = 'queued';
    project.updated_at = new Date();
    // Clear failure-related fields
    project.generation_failed_at = undefined;
    project.failure_reason = undefined;
    await project.save();

    // Add job to queue for regeneration
    const queue = new QueueService();
    await queue.addJob({
      type: 'generate_project',
      projectId: projectId,
      userId: userId,
      priority: 'high'
    });

    logger.info('Project retried successfully', { 
      userId, 
      projectId, 
      projectName: project.name
    });

    res.json({ 
      success: true, 
      message: 'Project retried successfully',
      data: { status: 'queued' }
    });
  } catch (error) {
    logger.error('Error retrying project:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to retry project' 
    });
  }
});

module.exports = router;
