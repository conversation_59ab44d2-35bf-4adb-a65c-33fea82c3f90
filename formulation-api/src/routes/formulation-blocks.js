const express = require('express');
const { verifyToken, requireAdmin } = require('../middleware/auth');
const { FormulationBlock, FormulationBinding } = require('../models/mongo');

function initializeFormulationBlockRoutes() {
  const router = express.Router();

  // Generic error handler wrapper
  const wrap = (fn) => async (req, res) => {
    try {
      await fn(req, res);
    } catch (e) {
      console.error('Formulation block route error:', e);
      res.status(e.status || 500).json({ 
        success: false, 
        error: e.message || 'Server error' 
      });
    }
  };

  // Validation helper
  const validateBlock = (data) => {
    const errors = [];
    if (!data.key) errors.push('Key is required');
    if (!data.name) errors.push('Name is required');
    if (!data.description) errors.push('Description is required');
    if (!data.schema_path) errors.push('Schema path is required');
    
    if (data.key && !/^[a-z_]+$/.test(data.key)) {
      errors.push('Key must contain only lowercase letters and underscores');
    }
    
    if (errors.length > 0) {
      const error = new Error(errors.join(', '));
      error.status = 400;
      throw error;
    }
  };

  const validateBinding = (data) => {
    const errors = [];
    if (!data.level) errors.push('Level is required');
    if (!data.slug) errors.push('Slug is required');
    if (!data.block_key) errors.push('Block key is required');
    
    if (data.level && !['industry', 'category', 'sub_category'].includes(data.level)) {
      errors.push('Level must be industry, category, or sub_category');
    }
    
    if (errors.length > 0) {
      const error = new Error(errors.join(', '));
      error.status = 400;
      throw error;
    }
  };

  // FORMULATION BLOCKS CRUD
  
  // GET /api/formulation-blocks - List all blocks
  router.get('/', verifyToken, requireAdmin, wrap(async (req, res) => {
    const { q, category, active } = req.query;
    const filter = {};
    
    if (q) {
      filter.$or = [
        { name: { $regex: q, $options: 'i' } },
        { key: { $regex: q, $options: 'i' } },
        { description: { $regex: q, $options: 'i' } }
      ];
    }
    
    if (category) filter.category = category;
    if (active !== undefined) filter.is_active = active === 'true';
    
    const blocks = await FormulationBlock.find(filter)
      .sort({ display_order: 1, name: 1 })
      .lean();
    
    res.json({ success: true, data: blocks });
  }));

  // GET /api/formulation-blocks/:id - Get single block
  router.get('/:id', verifyToken, requireAdmin, wrap(async (req, res) => {
    const block = await FormulationBlock.findById(req.params.id).lean();
    if (!block) {
      return res.status(404).json({ success: false, error: 'Block not found' });
    }
    res.json({ success: true, data: block });
  }));

  // POST /api/formulation-blocks - Create new block
  router.post('/', verifyToken, requireAdmin, wrap(async (req, res) => {
    const payload = req.body;
    validateBlock(payload);
    
    // Check if key already exists
    const existing = await FormulationBlock.findOne({ key: payload.key });
    if (existing) {
      return res.status(400).json({ 
        success: false, 
        error: 'Block with this key already exists' 
      });
    }
    
    const block = new FormulationBlock(payload);
    await block.save();
    
    res.json({ success: true, data: block.toJSON() });
  }));

  // PUT /api/formulation-blocks/:id - Update block
  router.put('/:id', verifyToken, requireAdmin, wrap(async (req, res) => {
    const payload = req.body;
    validateBlock(payload);
    
    // Check if key already exists (excluding current block)
    const existing = await FormulationBlock.findOne({ 
      key: payload.key, 
      _id: { $ne: req.params.id } 
    });
    if (existing) {
      return res.status(400).json({ 
        success: false, 
        error: 'Block with this key already exists' 
      });
    }
    
    const block = await FormulationBlock.findByIdAndUpdate(
      req.params.id,
      payload,
      { new: true, runValidators: true }
    );
    
    if (!block) {
      return res.status(404).json({ success: false, error: 'Block not found' });
    }
    
    res.json({ success: true, data: block.toJSON() });
  }));

  // DELETE /api/formulation-blocks/:id - Delete block
  router.delete('/:id', verifyToken, requireAdmin, wrap(async (req, res) => {
    const block = await FormulationBlock.findById(req.params.id);
    if (!block) {
      return res.status(404).json({ success: false, error: 'Block not found' });
    }

    // Check if block is used in any bindings
    const bindingsCount = await FormulationBinding.countDocuments({ 
      block_key: block.key 
    });
    
    if (bindingsCount > 0) {
      return res.status(400).json({ 
        success: false, 
        error: `Cannot delete block. It is used in ${bindingsCount} binding(s)` 
      });
    }
    
    await FormulationBlock.findByIdAndDelete(req.params.id);
    res.json({ success: true, data: { _id: req.params.id } });
  }));

  // FORMULATION BINDINGS CRUD
  
  // GET /bindings - List bindings
  router.get('/bindings', verifyToken, requireAdmin, wrap(async (req, res) => {
    const { level, slug, block_key, active } = req.query;
    const filter = {};
    
    if (level) filter.level = level;
    if (slug) filter.slug = slug;
    if (block_key) filter.block_key = block_key;
    if (active !== undefined) filter.is_active = active === 'true';
    
    const bindings = await FormulationBinding.find(filter)
      .sort({ level: 1, slug: 1, display_order: 1, block_key: 1 })
      .lean();
    
    res.json({ success: true, data: bindings });
  }));

  // GET /bindings/taxonomy/:level/:slug - Get bindings for taxonomy
  router.get('/bindings/taxonomy/:level/:slug', verifyToken, wrap(async (req, res) => {
    const { level, slug } = req.params;
    const bindings = await FormulationBinding.getBlocksForTaxonomy(level, slug);
    res.json({ success: true, data: bindings });
  }));

  // POST /bindings - Create binding
  router.post('/bindings', verifyToken, requireAdmin, wrap(async (req, res) => {
    const payload = req.body;
    validateBinding(payload);
    
    // Check if block exists
    const block = await FormulationBlock.findOne({ 
      key: payload.block_key, 
      is_active: true 
    });
    if (!block) {
      return res.status(400).json({ 
        success: false, 
        error: 'Block not found or inactive' 
      });
    }
    
    // Check if binding already exists
    const existing = await FormulationBinding.findOne({
      level: payload.level,
      slug: payload.slug,
      block_key: payload.block_key
    });
    if (existing) {
      return res.status(400).json({ 
        success: false, 
        error: 'Binding already exists for this taxonomy and block' 
      });
    }
    
    const binding = new FormulationBinding(payload);
    await binding.save();
    
    res.json({ success: true, data: binding.toJSON() });
  }));

  // PUT /bindings/:id - Update binding
  router.put('/bindings/:id', verifyToken, requireAdmin, wrap(async (req, res) => {
    const payload = req.body;
    validateBinding(payload);
    
    // Check if block exists
    const block = await FormulationBlock.findOne({ 
      key: payload.block_key, 
      is_active: true 
    });
    if (!block) {
      return res.status(400).json({ 
        success: false, 
        error: 'Block not found or inactive' 
      });
    }
    
    const binding = await FormulationBinding.findByIdAndUpdate(
      req.params.id,
      payload,
      { new: true, runValidators: true }
    );
    
    if (!binding) {
      return res.status(404).json({ success: false, error: 'Binding not found' });
    }
    
    res.json({ success: true, data: binding.toJSON() });
  }));

  // DELETE /bindings/:id - Delete binding
  router.delete('/bindings/:id', verifyToken, requireAdmin, wrap(async (req, res) => {
    const binding = await FormulationBinding.findByIdAndDelete(req.params.id);
    if (!binding) {
      return res.status(404).json({ success: false, error: 'Binding not found' });
    }
    res.json({ success: true, data: { _id: req.params.id } });
  }));

  // BULK OPERATIONS
  
  // POST /bindings/bulk - Bulk create/update bindings
  router.post('/bindings/bulk', verifyToken, requireAdmin, wrap(async (req, res) => {
    const { level, slug, blocks } = req.body;
    
    if (!level || !slug || !Array.isArray(blocks)) {
      return res.status(400).json({ 
        success: false, 
        error: 'Level, slug, and blocks array are required' 
      });
    }
    
    const results = [];
    
    for (const block of blocks) {
      try {
        const existing = await FormulationBinding.findOne({
          level,
          slug,
          block_key: block.block_key
        });
        
        if (existing) {
          // Update existing
          await FormulationBinding.findByIdAndUpdate(existing._id, {
            is_included: block.is_included,
            is_required: block.is_required || false,
            display_order: block.display_order || 0,
            custom_rules: block.custom_rules || ''
          });
          results.push({ action: 'updated', block_key: block.block_key });
        } else {
          // Create new
          const binding = new FormulationBinding({
            level,
            slug,
            block_key: block.block_key,
            is_included: block.is_included,
            is_required: block.is_required || false,
            display_order: block.display_order || 0,
            custom_rules: block.custom_rules || ''
          });
          await binding.save();
          results.push({ action: 'created', block_key: block.block_key });
        }
      } catch (error) {
        results.push({ 
          action: 'error', 
          block_key: block.block_key, 
          error: error.message 
        });
      }
    }
    
    res.json({ success: true, data: results });
  }));

  return router;
}

module.exports = initializeFormulationBlockRoutes;