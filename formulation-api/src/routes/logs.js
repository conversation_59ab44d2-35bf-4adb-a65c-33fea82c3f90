const express = require('express');
const logger = require('../utils/logger');
const fs = require('fs');
const path = require('path');

const router = express.Router();

// Frontend logging endpoint
router.post('/frontend', async (req, res) => {
  try {
    const logData = req.body;
    
    // Validate log data
    if (!logData || !logData.message || !logData.level) {
      return res.status(400).json({
        success: false,
        error: 'Invalid log data. Message and level are required.'
      });
    }

    // Sanitize and prepare log entry
    const frontendLogEntry = {
      timestamp: logData.timestamp || new Date().toISOString(),
      level: logData.level,
      message: logData.message,
      service: 'agrizy-formulation-web',
      environment: logData.environment || 'development',
      url: logData.url,
      userAgent: logData.userAgent,
      category: logData.category,
      ...logData.context
    };

    // Write to frontend-specific log file
    const frontendLogPath = process.env.FRONTEND_LOG_PATH 
      ? path.resolve(__dirname, '../..', process.env.FRONTEND_LOG_PATH)
      : path.resolve(__dirname, '../../../logs/frontend.log');
    
    // Ensure directory exists
    const logDir = path.dirname(frontendLogPath);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // Append to frontend log file
    const logLine = JSON.stringify(frontendLogEntry) + '\n';
    fs.appendFileSync(frontendLogPath, logLine);

    // Also log through backend logger for centralized logging
    switch (logData.level) {
      case 'error':
        logger.error(`[FRONTEND] ${logData.message}`, frontendLogEntry);
        break;
      case 'warn':
        logger.warn(`[FRONTEND] ${logData.message}`, frontendLogEntry);
        break;
      case 'info':
        logger.info(`[FRONTEND] ${logData.message}`, frontendLogEntry);
        break;
      case 'debug':
        logger.debug(`[FRONTEND] ${logData.message}`, frontendLogEntry);
        break;
      default:
        logger.info(`[FRONTEND] ${logData.message}`, frontendLogEntry);
    }

    res.json({
      success: true,
      message: 'Log entry recorded successfully'
    });

  } catch (error) {
    logger.error('Error processing frontend log:', {
      error: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      success: false,
      error: 'Failed to process log entry'
    });
  }
});

// Get frontend logs (for debugging)
router.get('/frontend', async (req, res) => {
  try {
    const { limit = 100, level, category } = req.query;
    
    const frontendLogPath = process.env.FRONTEND_LOG_PATH 
      ? path.resolve(__dirname, '../..', process.env.FRONTEND_LOG_PATH)
      : path.resolve(__dirname, '../../../logs/frontend.log');
    
    if (!fs.existsSync(frontendLogPath)) {
      return res.json({
        success: true,
        data: [],
        message: 'No frontend logs found'
      });
    }

    // Read log file
    const logContent = fs.readFileSync(frontendLogPath, 'utf8');
    const logLines = logContent.trim().split('\n').filter(line => line.trim());
    
    // Parse and filter logs
    let logs = logLines
      .map(line => {
        try {
          return JSON.parse(line);
        } catch {
          return null;
        }
      })
      .filter(log => log !== null);

    // Apply filters
    if (level) {
      logs = logs.filter(log => log.level === level);
    }
    if (category) {
      logs = logs.filter(log => log.category === category);
    }

    // Sort by timestamp (newest first) and limit
    logs = logs
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, parseInt(limit));

    res.json({
      success: true,
      data: logs,
      total: logs.length
    });

  } catch (error) {
    logger.error('Error retrieving frontend logs:', {
      error: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve frontend logs'
    });
  }
});

// Clear frontend logs (for maintenance)
router.delete('/frontend', async (req, res) => {
  try {
    const frontendLogPath = process.env.FRONTEND_LOG_PATH 
      ? path.resolve(__dirname, '../..', process.env.FRONTEND_LOG_PATH)
      : path.resolve(__dirname, '../../../logs/frontend.log');
    
    if (fs.existsSync(frontendLogPath)) {
      fs.writeFileSync(frontendLogPath, '');
    }

    logger.info('Frontend logs cleared', {
      action: 'clear-frontend-logs',
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Frontend logs cleared successfully'
    });

  } catch (error) {
    logger.error('Error clearing frontend logs:', {
      error: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      success: false,
      error: 'Failed to clear frontend logs'
    });
  }
});

module.exports = router;