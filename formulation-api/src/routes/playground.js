const express = require('express');
const { verifyToken } = require('../middleware/auth');
const logger = require('../utils/logger');
const { Project } = require('../models');
const claudeService = require('../services/claudeService');
const ChatService = require('../services/ChatService');
const ChatSessionManager = require('../services/ChatSessionManager');
const IntentAnalyzer = require('../services/IntentAnalyzer');

const router = express.Router();

// Initialize services
const chatService = new ChatService();
const sessionManager = new ChatSessionManager();
const intentAnalyzer = new IntentAnalyzer();

// All playground routes require authentication
router.use(verifyToken);

/**
 * Suggest short R&D-style notes based on current wizard inputs
 */
router.post('/suggest-notes', async (req, res) => {
  try {
    const { industry, category, sub_category, goals = {} } = req.body || {};
    if (!industry) {
      return res.status(400).json({ success: false, error: 'industry is required' });
    }

    const context = {
      industry,
      category,
      subCategory: sub_category,
      goals
    };

    const notes = await claudeService.generateNotes(context);
    return res.json({ success: true, data: { notes } });
  } catch (e) {
    logger.error('suggest-notes failed', { error: e.message });
    return res.status(500).json({ success: false, error: 'Failed to generate notes' });
  }
});

/**
 * Resilient formulation generation with caching and partial rendering
 */
router.post('/generate', async (req, res) => {
  try {
    const { formData } = req.body;
    const userId = req.user.id;

    if (!formData || !formData.industry || !formData.productType) {
      return res.status(400).json({
        success: false,
        error: 'Industry and product type are required'
      });
    }

    logger.info('Generating resilient formulation', {
      userId,
      industry: formData.industry,
      productType: formData.productType,
      goals: formData.goals
    });

    // Create or find project
    const projectName = `${formData.productType.replace(/[^a-zA-Z0-9]/g, ' ')} Project`;
    const project = await Project.createProject(userId, {
      name: projectName,
      description: `AI-generated formulation for ${formData.productType}`,
      industry: formData.industry,
      product_type: formData.productType,
      custom_product_type: formData.productDescription,
      goals: formData.goals,
      constraints: { compliance: formData.goals?.compliance || 'fda' }
    });

    let claudeResponse = null;
    let validationErrors = [];
    let rawResponse = null;

    try {
      // Generate formulation using Claude AI
      claudeResponse = await claudeService.generateFormulation(formData);
      rawResponse = claudeResponse;
      logger.info('Claude generation successful', { projectId: project.id });
    } catch (error) {
      logger.error('Claude generation failed', { 
        error: error.message,
        projectId: project.id 
      });
      
      // Extract validation errors if available
      if (error.message.includes('validation failed')) {
        const errorMatch = error.message.match(/validation failed: (.+)$/);
        if (errorMatch) {
          validationErrors = errorMatch[1].split(', ').map(err => ({
            field: err.split(':')[0],
            message: err.split(':')[1]?.trim() || 'Validation error'
          }));
        }
      }
      
      // Set fallback response structure
      claudeResponse = {
        recipes: [],
        validationErrors: validationErrors,
        rawError: error.message
      };
      rawResponse = error.message;
    }

    // Store the response data directly in MongoDB project

    // Parse components individually for partial rendering
    const parsedBlocks = parseFormulationBlocks(claudeResponse, validationErrors);

    // Evaluate formulation quality to determine success threshold
    const qualityAssessment = evaluateFormulationQuality(parsedBlocks, validationErrors);

    // Update project with formulation data
    const formulationData = {
      version: 1,
      generated_at: new Date(),
      quality_score: qualityAssessment.score,
      quality_level: qualityAssessment.level,
      ready_for_results: qualityAssessment.readyForResults,
      recipes: [{
        id: 'recipe_1',
        name: parsedBlocks.mainRecipe?.name || `${formData.productType} Formula`,
        description: parsedBlocks.mainRecipe?.description || 'AI-generated formulation',
        ingredients: parsedBlocks.mainRecipe?.ingredients || [],
        nutritional_profile: parsedBlocks.nutritionFacts || {},
        cost_analysis: {
          total_cogs: parsedBlocks.costBreakdown?.totalCost || 0,
          margin_percentage: 50
        },
        scores: parsedBlocks.scores || {
          nutrition: 75,
          sustainability: 70,
          cost_efficiency: 80,
          compliance: 85,
          market_appeal: 75
        }
      }],
      recipes: parsedBlocks.recipes || [],
      validation_errors: validationErrors,
      conversation_history: [{
        role: 'system',
        message: 'Initial formulation generated',
        timestamp: new Date(),
        raw_response: rawResponse
      }]
    };

    await Project.updateOne(
      { _id: project._id },
      { 
        current_formulation: formulationData,
        $push: {
          formulation_versions: {
            version: 1,
            formulation_data: formulationData,
            created_at: new Date(),
            changes_summary: 'Initial AI-generated formulation'
          }
        },
        updated_at: new Date()
      }
    );

    // Return response with component status and quality assessment
    const response = {
      success: true,
      projectId: project._id,
      data: {
        components: parsedBlocks,
        validationErrors: validationErrors,
        hasErrors: validationErrors.length > 0,
        qualityScore: qualityAssessment.score,
        qualityLevel: qualityAssessment.level,
        readyForResults: qualityAssessment.readyForResults,
        recommendPlayground: qualityAssessment.recommendPlayground,
        qualityIssues: qualityAssessment.issues,
        version: 1,
        formulation: formulationData
      }
    };

    logger.info('Resilient formulation generated', {
      userId,
      projectId: project.id,
      hasErrors: validationErrors.length > 0,
      validComponents: Object.keys(parsedBlocks).filter(k => parsedBlocks[k] !== null).length
    });

    res.json(response);

  } catch (error) {
    logger.error('Error in resilient formulation generation:', {
      error: error.message,
      stack: error.stack,
      userId: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: `Failed to generate formulation: ${error.message}`
    });
  }
});

/**
 * Load formulation from MongoDB persistence for playground
 */
router.get('/load/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = req.user.id;

    // Verify project belongs to user and get formulation
    const project = await Project.findOne({
      _id: projectId,
      user_id: userId
    });

    if (!project) {
      return res.status(404).json({
        success: false,
        error: 'Project not found'
      });
    }

    // Get current formulation from project
    const formulation = project.current_formulation;
    
    if (!formulation) {
      return res.status(404).json({
        success: false,
        error: 'No formulation found for this project'
      });
    }

    // Parse components for current state
    const parsedBlocks = parseFormulationBlocks(formulation, formulation.validation_errors);

    res.json({
      success: true,
      data: {
        components: parsedBlocks,
        validationErrors: formulation.validation_errors || [],
        hasErrors: (formulation.validation_errors || []).length > 0,
        persisted: true,
        timestamp: formulation.generated_at,
        version: formulation.version || 1,
        qualityScore: formulation.quality_score,
        qualityLevel: formulation.quality_level,
        // Include project metadata
        projectInfo: {
          id: project._id,
          name: project.name,
          description: project.description,
          industry: project.industry,
          product_type: project.product_type,
          goals: project.goals,
          status: project.status,
          created_at: project.created_at,
          updated_at: project.updated_at
        },
        // Include conversation history for playground
        conversationHistory: project.current_formulation.conversation_history || []
      }
    });

  } catch (error) {
    logger.error('Error loading persisted formulation:', {
      error: error.message,
      projectId: req.params.projectId,
      userId: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Failed to load formulation from database'
    });
  }
});

/**
 * Get project formulation status for navigation decisions
 */
router.get('/status/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = req.user.id;

    // Get project with minimal formulation data
    const project = await Project.findOne({
      _id: projectId,
      user_id: userId
    }).select('name description industry product_type status current_formulation.version current_formulation.quality_score current_formulation.ready_for_results current_formulation.generated_at formulation_versions');

    if (!project) {
      return res.status(404).json({
        success: false,
        error: 'Project not found'
      });
    }

    const hasFormulation = project.current_formulation && project.current_formulation.version;
    const versionCount = project.formulation_versions ? project.formulation_versions.length : 0;

    res.json({
      success: true,
      data: {
        projectId: project._id,
        name: project.name,
        description: project.description,
        industry: project.industry,
        product_type: project.product_type,
        status: project.status,
        hasFormulation,
        currentVersion: project.current_formulation?.version || 0,
        versionCount,
        qualityScore: project.current_formulation?.quality_score,
        qualityLevel: project.current_formulation?.quality_level,
        readyForResults: project.current_formulation?.ready_for_results || false,
        lastUpdated: project.current_formulation?.generated_at
      }
    });

  } catch (error) {
    logger.error('Error fetching project status:', {
      error: error.message,
      projectId: req.params.projectId,
      userId: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Failed to fetch project status'
    });
  }
});

// Legacy chat endpoints consolidated - see /chat endpoint below

/**
 * Get conversation history
 */
router.get('/conversation/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = req.user.id;

    // Get project and conversation history
    const project = await Project.findOne({
      _id: projectId,
      user_id: userId
    });

    if (!project || !project.current_formulation) {
      return res.status(404).json({
        success: false,
        error: 'Project or formulation not found'
      });
    }

    const conversation = project.current_formulation.conversation_history || [];

    res.json({
      success: true,
      data: conversation
    });

  } catch (error) {
    logger.error('Error fetching conversation:', {
      error: error.message,
      projectId: req.params.projectId,
      userId: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Failed to fetch conversation'
    });
  }
});

/**
 * Get formulation versions for rollback
 */
router.get('/versions/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = req.user.id;

    // Get project and version history
    const project = await Project.findOne({
      _id: projectId,
      user_id: userId
    });

    if (!project) {
      return res.status(404).json({
        success: false,
        error: 'Project not found'
      });
    }

    const versions = project.formulation_versions || [];

    res.json({
      success: true,
      data: versions
    });

  } catch (error) {
    logger.error('Error fetching versions:', {
      error: error.message,
      projectId: req.params.projectId,
      userId: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Failed to fetch versions'
    });
  }
});

/**
 * Rollback to a specific version
 */
router.post('/rollback/:projectId/:version', async (req, res) => {
  try {
    const { projectId, version } = req.params;
    const userId = req.user.id;

    // Get project and find target version
    const project = await Project.findOne({
      _id: projectId,
      user_id: userId
    });

    if (!project) {
      return res.status(404).json({
        success: false,
        error: 'Project not found'
      });
    }

    const versions = project.formulation_versions || [];
    const targetVersion = versions.find(v => v.version === parseInt(version));

    if (!targetVersion) {
      return res.status(404).json({
        success: false,
        error: 'Version not found'
      });
    }

    // Update current formulation with rolled back version
    const newVersion = Math.max(...versions.map(v => v.version)) + 1;
    const rolledBackFormulation = {
      ...targetVersion.formulation_data,
      version: newVersion,
      generated_at: new Date(),
      conversation_history: [
        ...(targetVersion.formulation_data.conversation_history || []),
        {
          role: 'system',
          message: `Rolled back to version ${version}`,
          timestamp: new Date()
        }
      ]
    };

    await Project.updateOne(
      { _id: projectId },
      {
        current_formulation: rolledBackFormulation,
        $push: {
          formulation_versions: {
            version: newVersion,
            formulation_data: rolledBackFormulation,
            created_at: new Date(),
            changes_summary: `Rolled back to version ${version}`
          }
        },
        updated_at: new Date()
      }
    );

    logger.info('Formulation rolled back', {
      userId,
      projectId,
      fromVersion: version,
      toVersion: newVersion
    });

    res.json({
      success: true,
      data: {
        message: `Rolled back to version ${version}`,
        currentVersion: newVersion
      }
    });

  } catch (error) {
    logger.error('Error during rollback:', {
      error: error.message,
      projectId: req.params.projectId,
      version: req.params.version,
      userId: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Failed to rollback formulation'
    });
  }
});

/**
 * Parse formulation blocks individually for partial rendering
 */
function parseFormulationBlocks(claudeResponse, validationErrors = []) {
  const blocks = {
    mainRecipe: null,
    recipes: [],
    nutritionalProfile: null,
    costAnalysis: null,
    manufacturingProcedure: null,
    bomDetails: null,
    complianceDetails: null,
    marketAnalysis: null,
    errors: {}
  };

  // Parse main recipe
  try {
    if (claudeResponse?.recipes?.[0]) {
      blocks.mainRecipe = claudeResponse.recipes[0];
    }
  } catch (error) {
    blocks.errors.mainRecipe = 'Failed to parse main recipe';
  }

  // Parse recipes - should be at root level according to schema
  try {
    // According to formulationSchema.json, recipes should be at root level
    if (claudeResponse?.recipes && Array.isArray(claudeResponse.recipes)) {
      blocks.recipes = claudeResponse.recipes; // Get all recipes as per schema
      console.log(`[parseFormulationBlocks] Found ${blocks.recipes.length} recipes at root level`);
    } else {
      // Fallback: If no recipes found, initialize as empty array
      blocks.recipes = [];
      console.log('[parseFormulationBlocks] No recipes found in formulation');
    }
  } catch (error) {
    blocks.errors.recipes = 'Failed to parse recipes';
    console.error('[parseFormulationBlocks] Error parsing recipes:', error);
  }

  // Parse nutritional profile
  try {
    if (blocks.mainRecipe?.nutritional_profile) {
      blocks.nutritionalProfile = blocks.mainRecipe.nutritional_profile;
    }
  } catch (error) {
    blocks.errors.nutritionalProfile = 'Failed to parse nutritional profile';
  }

  // Parse cost analysis
  try {
    if (blocks.mainRecipe?.cost_analysis) {
      blocks.costAnalysis = blocks.mainRecipe.cost_analysis;
    }
  } catch (error) {
    blocks.errors.costAnalysis = 'Failed to parse cost analysis';
  }

  // Parse manufacturing procedure
  try {
    if (blocks.mainRecipe?.production_specs?.detailed_manufacturing_procedure) {
      blocks.manufacturingProcedure = blocks.mainRecipe.production_specs.detailed_manufacturing_procedure;
    }
  } catch (error) {
    blocks.errors.manufacturingProcedure = 'Failed to parse manufacturing procedure';
  }

  // Parse BOM details
  try {
    if (blocks.mainRecipe?.ingredients) {
      blocks.bomDetails = blocks.mainRecipe.ingredients.map(ing => ing.bom_details).filter(Boolean);
    }
  } catch (error) {
    blocks.errors.bomDetails = 'Failed to parse BOM details';
  }

  // Map validation errors to blocks
  validationErrors.forEach(error => {
    const field = error.field || error;
    if (field.includes('recipes')) blocks.errors.mainRecipe = error.message || 'Validation error';
    if (field.includes('recipes')) blocks.errors.recipes = error.message || 'Validation error';
    if (field.includes('nutritional_profile')) blocks.errors.nutritionalProfile = error.message || 'Validation error';
    if (field.includes('cost_analysis')) blocks.errors.costAnalysis = error.message || 'Validation error';
  });

  return blocks;
}

/**
 * Evaluate formulation quality to determine success thresholds
 */
function evaluateFormulationQuality(blocks, validationErrors = []) {
  let score = 0;
  let issues = [];
  
  // Check main recipe completeness (40 points max)
  if (components.mainRecipe) {
    score += 20;
    
    // Check ingredients count (minimum 3 for basic formulation)
    if (components.mainRecipe.ingredients && components.mainRecipe.ingredients.length >= 3) {
      score += 10;
    } else {
      issues.push('Insufficient ingredients (minimum 3 required)');
    }
    
    // Check nutritional profile
    if (components.nutritionalProfile) {
      score += 10;
    } else {
      issues.push('Missing nutritional profile');
    }
  } else {
    issues.push('Missing main recipe');
  }
  
  // Check recipes completeness (30 points max)
  if (components.recipes && components.recipes.length >= 2) {
    score += 20;
    if (components.recipes.length >= 3) {
      score += 10; // Bonus for complete set
    }
  } else {
    issues.push('Insufficient recipes (minimum 2 required)');
  }
  
  // Check cost analysis (15 points max)
  if (components.costAnalysis) {
    score += 15;
  } else {
    issues.push('Missing cost analysis');
  }
  
  // Check manufacturing procedure (15 points max)
  if (components.manufacturingProcedure) {
    score += 15;
  } else {
    issues.push('Missing manufacturing procedure');
  }
  
  // Deduct points for validation errors (max -20 points)
  const errorPenalty = Math.min(validationErrors.length * 5, 20);
  score = Math.max(0, score - errorPenalty);
  
  if (validationErrors.length > 0) {
    issues.push(`${validationErrors.length} validation errors present`);
  }
  
  // Determine quality level and recommendations
  let level, readyForResults, recommendPlayground;
  
  if (score >= 85) {
    level = 'excellent';
    readyForResults = true;
    recommendPlayground = false;
  } else if (score >= 70) {
    level = 'good';
    readyForResults = true;
    recommendPlayground = false;
  } else if (score >= 50) {
    level = 'acceptable';
    readyForResults = false;
    recommendPlayground = true;
  } else {
    level = 'needs_optimization';
    readyForResults = false;
    recommendPlayground = true;
  }
  
  return {
    score,
    level,
    readyForResults,
    recommendPlayground,
    issues
  };
}

/**
 * MAIN CHAT ENDPOINT - Template-based with forced upgrades and context control
 * POST /api/playground/chat
 * Process chat message and return upgraded formulation
 */
router.post('/chat', async (req, res) => {
  try {
    const { 
      message, 
      projectId, 
      variantIndex = 0, 
      conversationHistory = [], 
      contextControl = {} 
    } = req.body;
    const userId = req.user.id;
    
    // Extract context control settings
    const {
      variantScope = 'current',
      componentScope = 'all',
      selectedVariantId = 'main',
      selectedVariantIndex = 0
    } = contextControl;

    // Validation
    if (!message || typeof message !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Message is required and must be a string'
      });
    }

    if (!projectId) {
      return res.status(400).json({
        success: false,
        error: 'Project ID is required'
      });
    }

    // Load project data
    const project = await Project.findOne({ _id: projectId, user_id: userId });
    if (!project) {
      return res.status(404).json({
        success: false,
        error: 'Project not found or access denied'
      });
    }

    // SIMPLIFIED: All recipes in single array
    const currentFormulation = project.current_formulation;
    
    // Ensure recipes array exists and is populated
    if (!currentFormulation.recipes || currentFormulation.recipes.length === 0) {
      // Migration: Move variations to recipes if needed
      if (currentFormulation.variations && currentFormulation.variations.length > 0) {
        console.log('[Migration] Moving variations to recipes array');
        currentFormulation.recipes = [...currentFormulation.variations];
        // Clear variations to avoid confusion
        delete currentFormulation.variations;
        // Save the migration
        project.current_formulation.recipes = currentFormulation.recipes;
        delete project.current_formulation.variations;
        await project.save();
      } else {
        return res.status(400).json({
          success: false,
          error: 'No formulation data found in project'
        });
      }
    }

    // Get current recipe - now super simple!
    let currentRecipe;
    const selectedIndex = contextControl?.selectedVariantIndex ?? 0;
    
    console.log('[DEBUG] Simplified recipe selection:', {
      requestedIndex: selectedIndex,
      totalRecipes: currentFormulation.recipes.length,
      recipeNames: currentFormulation.recipes.map(v => v.name)
    });
    
    // Direct index access - no confusion!
    currentRecipe = currentFormulation.recipes[selectedIndex];
    
    if (!currentRecipe) {
      console.error(`[ERROR] Recipe not found at index ${selectedIndex}`);
      return res.status(400).json({
        success: false,
        error: `No recipe found at index ${selectedIndex}`,
        availableIndices: currentFormulation.recipes.map((v, i) => i)
      });
    }
    
    // Prepare comprehensive project context for LLM
    const projectData = {
      name: project.name,
      created_at: project.created_at,
      user: {
        company: req.user.company,
        role: req.user.role
      },
      journey: {
        industry: project.industry,
        productType: project.product_type,
        targetMarket: project.target_market,
        budgetRange: project.budget_range,
        keyRequirements: project.key_requirements,
        regulatoryRequirements: project.regulatory_requirements,
        sustainabilityLevel: project.sustainability_level
      },
      currentFormulation: currentFormulation,
      recipes: currentFormulation.recipes || [],
      generationMethod: 'AI-assisted',
      iterationCount: project.formulation_versions?.length || 1,
      userProjectCount: await Project.countDocuments({ user_id: userId })
    };

    // Process chat message with enhanced service
    logger.info('Processing enhanced chat message with two-pass system', { 
      projectId, 
      userId, 
      messageLength: message.length,
      templateSystem: 'v2-twopass',
      approach: 'intent-first-then-modify'
    });

    // Debug log to check what's in currentRecipe
    console.log('[DEBUG] Current recipe being sent to ChatService:', {
      name: currentRecipe.name,
      ingredientsCount: currentRecipe.ingredients?.length || 0,
      allIngredients: currentRecipe.ingredients?.map(i => ({ 
        name: i.name, 
        percentage: i.percentage 
      })) || [],
      hasAmla: currentRecipe.ingredients?.some(i => i.name?.toLowerCase().includes('amla')),
      recipeType: selectedVariantIndex === 0 ? 'Main Recipe' : `Alternative Recipe ${selectedVariantIndex}`
    });
    
    // Extra debug: Log the exact structure
    if (currentRecipe.ingredients && currentRecipe.ingredients.length > 0) {
      console.log('[DEBUG] Full ingredients list:');
      currentRecipe.ingredients.forEach((ing, idx) => {
        console.log(`  ${idx + 1}. ${ing.name} (${ing.percentage}%)`);
      });
    } else {
      console.log('[DEBUG] WARNING: No ingredients found in current recipe!');
    }

    const chatResponse = await chatService.processChatMessage(
      message,
      {
        name: currentRecipe.name,
        ingredients: currentRecipe.ingredients,
        cost_analysis: currentRecipe.cost_analysis,
        nutritional_profile: currentRecipe.nutritional_profile,
        production_specs: currentRecipe.production_specs,
        packaging_specs: currentRecipe.packaging_specs,
        sustainability_metrics: currentRecipe.sustainability_metrics,
        regulatory_compliance: currentRecipe.regulatory_compliance,
        shelf_life: currentRecipe.shelf_life,
        quality_score: currentFormulation.quality_score,
        version: currentFormulation.version || '1.0.0'
      },
      conversationHistory,
      projectData,
      {
        variantScope,
        componentScope,
        selectedVariantId,
        selectedVariantIndex,
        allRecipes: currentFormulation.recipes || []
      }
    );

    if (!chatResponse.success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to process chat message',
        details: chatResponse.error,
        fallback: chatResponse.fallback
      });
    }

    // Log the efficiency improvement
    const fullContextSize = JSON.stringify(currentRecipe).length;
    const modifiedComponents = chatResponse.changes?.componentsModified || [];
    console.log(`[V2 Chat] Efficiency metrics:`, {
      fullContextSize,
      componentsModified: modifiedComponents,
      contextReduction: modifiedComponents.includes('all') ? 
        '0%' : 
        `${Math.round((1 - (JSON.stringify(chatResponse.upgradedFormulation).length / fullContextSize)) * 100)}%`
    });

    // Get existing conversation history from database, fall back to frontend if not available
    const existingHistory = project.current_formulation.conversation_history || conversationHistory || [];
    
    // Update conversation history with enhanced tracking
    const enhancedConversation = [
      ...existingHistory,
      // User message
      {
        role: 'user',
        message: message,
        timestamp: new Date(),
        action: 'modify',
        version: chatResponse.metadata.versionIncrement
      },
      // Assistant response
      {
        role: 'assistant',
        message: chatResponse.message,
        timestamp: new Date(),
        action: 'modify',
        changes: chatResponse.changes.changesList,
        version: chatResponse.metadata.versionIncrement
      }
    ];

    // Always save new formulation version (forced upgrade system)
    if (!project.formulation_versions) {
      project.formulation_versions = [];
    }
    
    const newVersion = chatResponse.metadata.versionIncrement;
    project.formulation_versions.push({
      version: newVersion,
      created_at: new Date(),
      formulation_data: chatResponse.upgradedFormulation,
      changes_summary: chatResponse.changes.summary,
      parent_version: currentFormulation.version,
      diff: chatResponse.changes.diff,
      template_used: chatResponse.metadata.template.name,
      confidence_score: chatResponse.metadata.confidence
    });

    // Update current formulation to new version
    project.current_formulation.version = newVersion;
    project.current_formulation.active_version = newVersion;
    project.current_formulation.conversation_history = enhancedConversation;
    
    // SIMPLIFIED: Update the specific recipe directly
    console.log(`[DEBUG] Updating recipe at index ${selectedIndex}`);
    
    if (project.current_formulation.recipes && project.current_formulation.recipes[selectedIndex]) {
      project.current_formulation.recipes[selectedIndex] = {
        ...project.current_formulation.recipes[selectedIndex],
        name: chatResponse.upgradedFormulation.name || currentRecipe.name,
        ingredients: chatResponse.upgradedFormulation.ingredients || currentRecipe.ingredients,
        cost_analysis: chatResponse.upgradedFormulation.cost_analysis || currentRecipe.cost_analysis,
        nutritional_profile: chatResponse.upgradedFormulation.nutritional_profile || currentRecipe.nutritional_profile,
        production_specs: chatResponse.upgradedFormulation.production_specs || currentRecipe.production_specs,
        sustainability_metrics: chatResponse.upgradedFormulation.sustainability_metrics || currentRecipe.sustainability_metrics,
        packaging_specs: chatResponse.upgradedFormulation.packaging_specs || currentRecipe.packaging_specs,
        regulatory_compliance: chatResponse.upgradedFormulation.regulatory_compliance || currentRecipe.regulatory_compliance,
        shelf_life: chatResponse.upgradedFormulation.shelf_life || currentRecipe.shelf_life,
        last_modified: new Date(),
        version: newVersion
      };
      console.log(`[DEBUG] Recipe at index ${selectedIndex} updated successfully`);
    } else {
      console.log(`[DEBUG] ERROR: Could not find recipe at index ${selectedIndex} to update`);
    }

    // Update project timestamp
    project.updated_at = new Date();
    await project.save();

    logger.info('Enhanced chat processing completed', {
      projectId,
      userId,
      newVersion,
      changesCount: chatResponse.changes.changesList.length,
      confidence: chatResponse.metadata.confidence
    });

    // Return enhanced response
    res.json({
      success: true,
      message: chatResponse.message,
      upgradedFormulation: chatResponse.upgradedFormulation,
      changes: {
        summary: chatResponse.changes.summary,
        changesList: chatResponse.changes.changesList,
        additionalImprovements: chatResponse.changes.additionalImprovements,
        diff: chatResponse.changes.diff,
        componentsModified: chatResponse.changes.componentsModified || [],
        efficiencyMetrics: {
          originalContextSize: fullContextSize,
          componentsAnalyzed: modifiedComponents.length,
          contextReduction: modifiedComponents.includes('all') ? 
            'Full context used' : 
            `Focused on ${modifiedComponents.join(', ')}`,
          twoPassSystem: true
        }
      },
      warnings: chatResponse.warnings,
      metadata: {
        ...chatResponse.metadata,
        projectUpdated: true,
        variantIndex: variantIndex,
        newVersion: newVersion,
        previousVersion: currentFormulation.version
      }
    });

  } catch (error) {
    logger.error('Enhanced chat error:', { error: error.message, projectId: req.body.projectId });
    res.status(500).json({
      success: false,
      error: 'Internal server error processing enhanced chat message',
      details: error.message
    });
  }
});

/**
 * POST /api/playground/chat/initialize
 * Initialize chat with comprehensive product context
 */
router.post('/chat/initialize', async (req, res) => {
  try {
    const { projectId, variantIndex = 0 } = req.body;
    const userId = req.user.id;

    if (!projectId) {
      return res.status(400).json({
        success: false,
        error: 'Project ID is required'
      });
    }

    // Load project data
    const project = await Project.findOne({ _id: projectId, user_id: userId });
    if (!project) {
      return res.status(404).json({
        success: false,
        error: 'Project not found or access denied'
      });
    }

    const currentFormulation = project.current_formulation;
    if (!currentFormulation || !currentFormulation.recipes || currentFormulation.recipes.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No formulation data found in project'
      });
    }

    const currentVariant = currentFormulation.recipes[variantIndex] || currentFormulation.recipes[0];
    
    // Prepare comprehensive project context
    const projectData = {
      name: project.name,
      created_at: project.created_at,
      user: {
        company: req.user.company,
        role: req.user.role
      },
      journey: {
        industry: project.industry,
        productType: project.product_type,
        targetMarket: project.target_market,
        budgetRange: project.budget_range,
        keyRequirements: project.key_requirements,
        regulatoryRequirements: project.regulatory_requirements,
        sustainabilityLevel: project.sustainability_level
      },
      currentFormulation: currentFormulation,
      recipes: currentFormulation.recipes || [],
      generationMethod: 'AI-assisted',
      iterationCount: project.formulation_versions?.length || 1,
      userProjectCount: await Project.countDocuments({ user_id: userId })
    };

    // Initialize enhanced chat
    logger.info('Initializing enhanced chat', { 
      projectId, 
      userId,
      templateSystem: 'v2_with_context'
    });

    const initResponse = await chatService.initializeChat(
      {
        name: currentVariant.name,
        ingredients: currentVariant.ingredients,
        costAnalysis: currentVariant.cost_analysis,
        nutritionalProfile: currentVariant.nutritional_profile,
        manufacturingProcedure: currentVariant.production_specs,
        version: currentFormulation.version
      },
      projectData
    );

    if (!initResponse.success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to initialize enhanced chat',
        details: initResponse.error
      });
    }

    // Initialize conversation history in project
    if (!project.current_formulation.conversation_history) {
      project.current_formulation.conversation_history = [];
    }

    project.current_formulation.conversation_history.push({
      role: 'assistant',
      message: initResponse.message,
      timestamp: new Date(),
      action: 'initialize',
      changes: [],
      version: null
    });

    await project.save();

    res.json({
      success: true,
      welcomeMessage: initResponse.message,
      optimizationOpportunities: initResponse.changes?.changesList || [],
      projectAnalysis: {
        currentVersion: currentFormulation.version,
        totalVariants: currentFormulation.recipes.length,
        projectAge: Math.floor((new Date() - new Date(project.created_at)) / (1000 * 60 * 60 * 24)),
        iterationCount: project.formulation_versions?.length || 1,
        industry: project.industry,
        productType: project.product_type
      },
      metadata: initResponse.metadata,
      templateInfo: {
        system: 'enhanced_v2',
        richContextEnabled: true,
        forcedUpgradesEnabled: true,
        availableTemplates: chatService.getAvailableTemplates()
      }
    });

  } catch (error) {
    logger.error('Enhanced chat initialization error:', { error: error.message, projectId: req.body.projectId });
    res.status(500).json({
      success: false,
      error: 'Internal server error initializing enhanced chat',
      details: error.message
    });
  }
});

// Legacy v1 and v3 endpoints removed - consolidated to single /chat endpoint

/**
 * GET /api/playground/chat/config
 * Get chat service configuration and available templates
 */
router.get('/chat/config', async (req, res) => {
  try {
    const config = chatService.validateConfiguration();
    const templates = chatService.getAvailableTemplates();

    res.json({
      success: true,
      system: 'enhanced_chat_v2',
      configuration: config,
      availableTemplates: templates,
      features: {
        richInitialization: true,
        forcedUpgrades: true,
        versionControl: true,
        diffTracking: true,
        templateSelection: true
      },
      serviceStatus: config.serviceReady ? 'ready' : 'not_configured'
    });

  } catch (error) {
    logger.error('Enhanced chat config error:', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Internal server error getting enhanced chat configuration',
      details: error.message
    });
  }
});

module.exports = router;
