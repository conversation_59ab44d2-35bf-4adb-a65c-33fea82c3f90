const express = require('express');
const { verifyToken } = require('../middleware/auth');
const logger = require('../utils/logger');
const { Project } = require('../models');
const claudeService = require('../services/claudeService');

const router = express.Router();

// All formulation routes require authentication
router.use(verifyToken);

// Generate new formulation
router.post('/generate', async (req, res) => {
  try {
    const { formData } = req.body;
    const userId = req.user.id;

    if (!formData || !formData.industry || !formData.productType) {
      return res.status(400).json({
        success: false,
        error: 'Industry and product type are required'
      });
    }

    logger.info('Generating formulation', {
      userId,
      industry: formData.industry,
      productType: formData.productType,
      goals: formData.goals
    });

    // Create new project
    const projectName = `${formData.productType.replace(/[^a-zA-Z0-9]/g, ' ')} Project`;
    const project = new Project({
      user_id: userId,
      name: projectName,
      description: `AI-generated formulation for ${formData.productType}`,
      industry: formData.industry,
      product_type: formData.productType,
      custom_product_type: formData.productDescription,
      status: 'generating',  // Use valid enum value
      goals: formData.goals || {},
      constraints: { compliance: formData.goals?.compliance || 'fda' },
      is_active: true
    });
    
    await project.save();

    // Generate formulation using Claude AI
    const claudeResponse = await claudeService.generateFormulation(formData);
    
    // Create formulation data structure for MongoDB
    const formulationData = {
      version: 1,
      generated_at: new Date(),
      quality_score: calculateQualityScore(claudeResponse),
      quality_level: determineQualityLevel(claudeResponse),
      ready_for_results: true,
      recipes: claudeResponse.recipes.map((recipe, index) => ({
        id: `recipe_${index + 1}`,
        name: recipe.name,
        description: recipe.description,
        ingredients: recipe.ingredients || [],
        nutritional_profile: recipe.nutritional_profile || {},
        cost_analysis: recipe.cost_analysis || {},
        scores: recipe.scores || {}
      })),
      variations: claudeResponse.variations || [],
      validation_errors: [],
      conversation_history: [{
        role: 'system',
        message: 'Initial formulation generated',
        timestamp: new Date()
      }]
    };

    // Update project with formulation data
    await Project.updateOne(
      { _id: project._id },
      { 
        current_formulation: formulationData,
        $push: {
          formulation_versions: {
            version: 1,
            formulation_data: formulationData,
            created_at: new Date(),
            changes_summary: 'Initial AI-generated formulation'
          }
        },
        updated_at: new Date()
      }
    );

    // Response structure
    const response = {
      success: true,
      projectId: project._id,
      formulation: formulationData,
      claudeResponse: claudeResponse
    };

    logger.info('Formulation generated successfully', {
      userId,
      projectId: project._id,
      formulationVersion: formulationData.version,
      qualityScore: formulationData.quality_score
    });

    res.json({
      success: true,
      data: response
    });

  } catch (error) {
    logger.error('Error generating formulation:', {
      error: error.message,
      stack: error.stack,
      userId: req.user?.id
    });

    // Temporarily return detailed error for debugging
    res.status(500).json({
      success: false,
      error: `Failed to generate formulation: ${error.message}`,
      details: error.stack
    });
  }
});

// Get formulations for a project
router.get('/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = req.user._id;
    const { limit = 10, offset = 0, status } = req.query;

    // Verify project belongs to user and get formulation data
    const project = await Project.findOne({
      _id: projectId,
      user_id: userId
    });

    if (!project) {
      return res.status(404).json({
        success: false,
        error: 'Project not found'
      });
    }

    // Return current formulation and version history
    const formulations = [];
    
    if (project.current_formulation) {
      formulations.push({
        id: 'current',
        version: project.current_formulation.version,
        name: project.current_formulation.recipes[0]?.name || 'Current Formulation',
        status: 'active',
        quality_score: project.current_formulation.quality_score,
        quality_level: project.current_formulation.quality_level,
        generated_at: project.current_formulation.generated_at,
        ready_for_results: project.current_formulation.ready_for_results
      });
    }
    
    // Add version history if requested
    if (project.formulation_versions) {
      const versions = project.formulation_versions
        .slice(parseInt(offset), parseInt(offset) + parseInt(limit))
        .map(version => ({
          id: `version_${version.version}`,
          version: version.version,
          name: version.formulation_data.recipes[0]?.name || `Version ${version.version}`,
          status: 'archived',
          quality_score: version.formulation_data.quality_score,
          quality_level: version.formulation_data.quality_level,
          generated_at: version.created_at,
          changes_summary: version.changes_summary
        }));
      
      formulations.push(...versions);
    }

    res.json({
      success: true,
      data: formulations
    });

  } catch (error) {
    logger.error('Error fetching formulations:', {
      error: error.message,
      projectId: req.params.projectId,
      userId: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Failed to fetch formulations'
    });
  }
});

// Update formulation
router.put('/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = req.user._id;
    const updateData = req.body;

    // Find project and verify ownership
    const project = await Project.findOne({
      _id: projectId,
      user_id: userId
    });

    if (!project || !project.current_formulation) {
      return res.status(404).json({
        success: false,
        error: 'Project or formulation not found'
      });
    }

    // Update formulation data
    const currentFormulation = project.current_formulation;
    const newVersion = (currentFormulation.version || 1) + 1;
    
    const updatedFormulation = {
      ...currentFormulation.toObject(),
      ...updateData,
      version: newVersion,
      generated_at: new Date()
    };

    await Project.updateOne(
      { _id: projectId },
      {
        current_formulation: updatedFormulation,
        $push: {
          formulation_versions: {
            version: newVersion,
            formulation_data: updatedFormulation,
            created_at: new Date(),
            changes_summary: 'Manual update via API'
          }
        },
        updated_at: new Date()
      }
    );

    logger.info('Formulation updated', {
      userId,
      projectId,
      newVersion,
      changes: Object.keys(updateData)
    });

    res.json({
      success: true,
      data: updatedFormulation
    });

  } catch (error) {
    logger.error('Error updating formulation:', {
      error: error.message,
      projectId: req.params.projectId,
      userId: req.user?._id
    });

    res.status(500).json({
      success: false,
      error: 'Failed to update formulation'
    });
  }
});

// Export formulation
router.post('/:id/export', async (req, res) => {
  try {
    const { id } = req.params;
    const { format = 'pdf' } = req.body;
    const userId = req.user.id;

    // Find formulation and verify ownership
    const formulation = await Formulation.findOne({
      where: { id },
      include: [{
        model: Project,
        as: 'project',
        where: { user_id: userId }
      }]
    });

    if (!formulation) {
      return res.status(404).json({
        success: false,
        error: 'Formulation not found'
      });
    }

    // TODO: Implement actual export functionality
    logger.info('Formulation export requested', {
      userId,
      formulationId: id,
      format
    });

    res.json({
      success: true,
      message: `Export in ${format.toUpperCase()} format requested`,
      downloadUrl: `/api/formulations/${id}/download?format=${format}`
    });

  } catch (error) {
    logger.error('Error exporting formulation:', {
      error: error.message,
      formulationId: req.params.id,
      userId: req.user?.id
    });

    res.status(500).json({
      success: false,
      error: 'Failed to export formulation'
    });
  }
});

// AI-powered formulation generation logic
async function generateFormulationData(formData) {
  const { industry, productType, goals } = formData;
  
  // Ingredient databases for different industries
  const ingredientDB = {
    beverages: {
      'antioxidant-juice': [
        { name: 'Pomegranate Extract', basePercentage: 35, costPerUnit: 1.20, function: 'Primary antioxidant source', supplier: 'NaturSource Inc' },
        { name: 'Acai Berry Powder', basePercentage: 20, costPerUnit: 2.25, function: 'Superfruit antioxidants', supplier: 'Amazon Naturals' },
        { name: 'Green Tea Extract', basePercentage: 15, costPerUnit: 2.53, function: 'Natural caffeine & EGCG', supplier: 'TeaLife Co' },
        { name: 'Goji Berry Extract', basePercentage: 12, costPerUnit: 2.67, function: 'Immune support', supplier: 'Himalayan Herbs' },
        { name: 'Vitamin C (Ascorbic Acid)', basePercentage: 8, costPerUnit: 1.88, function: 'Antioxidant stability', supplier: 'VitaCorp' },
        { name: 'Natural Flavoring', basePercentage: 5, costPerUnit: 4.00, function: 'Taste enhancement', supplier: 'FlavorCraft' },
        { name: 'Stevia Extract', basePercentage: 3, costPerUnit: 6.00, function: 'Natural sweetener', supplier: 'SweetLeaf Co' },
        { name: 'Citric Acid', basePercentage: 2, costPerUnit: 3.50, function: 'pH adjustment & preservation', supplier: 'ChemPure Ltd' }
      ],
      'energy-drink': [
        { name: 'Caffeine Anhydrous', basePercentage: 25, costPerUnit: 12.00, function: 'Energy boost', supplier: 'EnergyMax Co' },
        { name: 'Taurine', basePercentage: 20, costPerUnit: 8.50, function: 'Performance enhancement', supplier: 'AminoSource' },
        { name: 'Guarana Extract', basePercentage: 15, costPerUnit: 5.25, function: 'Natural stimulant', supplier: 'Amazon Botanicals' },
        { name: 'B-Vitamin Complex', basePercentage: 12, costPerUnit: 15.00, function: 'Energy metabolism', supplier: 'VitaBlend Inc' },
        { name: 'Ginseng Extract', basePercentage: 10, costPerUnit: 18.75, function: 'Adaptogenic support', supplier: 'Asian Herbs Ltd' },
        { name: 'Natural Cherry Flavor', basePercentage: 8, costPerUnit: 6.25, function: 'Taste profile', supplier: 'FlavorCraft' },
        { name: 'Sucralose', basePercentage: 5, costPerUnit: 22.00, function: 'Zero-calorie sweetener', supplier: 'SweetTech Co' },
        { name: 'Sodium Citrate', basePercentage: 5, costPerUnit: 4.50, function: 'Electrolyte balance', supplier: 'ElectroLyte Inc' }
      ]
    },
    nutraceuticals: {
      'multivitamin': [
        { name: 'Vitamin D3', basePercentage: 20, costPerUnit: 25.00, function: 'Bone health support', supplier: 'SunVit Corporation' },
        { name: 'Magnesium Oxide', basePercentage: 18, costPerUnit: 3.75, function: 'Muscle & nerve function', supplier: 'MineralMax Co' },
        { name: 'Vitamin B12', basePercentage: 15, costPerUnit: 45.00, function: 'Energy & nervous system', supplier: 'B-Complex Ltd' },
        { name: 'Iron Fumarate', basePercentage: 12, costPerUnit: 8.25, function: 'Blood health support', supplier: 'IronWorks Inc' },
        { name: 'Zinc Gluconate', basePercentage: 10, costPerUnit: 12.50, function: 'Immune system support', supplier: 'ZincPure Co' },
        { name: 'Vitamin C (Calcium Ascorbate)', basePercentage: 10, costPerUnit: 6.75, function: 'Antioxidant protection', supplier: 'C-VitaMax' },
        { name: 'Folic Acid', basePercentage: 8, costPerUnit: 35.00, function: 'Cell division support', supplier: 'FolicAid Corp' },
        { name: 'Microcrystalline Cellulose', basePercentage: 7, costPerUnit: 2.25, function: 'Binding agent', supplier: 'CellTech Industries' }
      ]
    },
    cosmetics: {
      'face-serum': [
        { name: 'Hyaluronic Acid', basePercentage: 30, costPerUnit: 85.00, function: 'Deep hydration', supplier: 'HydroLux Cosmetics' },
        { name: 'Vitamin C (L-Ascorbic Acid)', basePercentage: 20, costPerUnit: 65.00, function: 'Anti-aging & brightening', supplier: 'VitaGlow Inc' },
        { name: 'Niacinamide', basePercentage: 15, costPerUnit: 28.50, function: 'Pore refinement', supplier: 'DermaTech Solutions' },
        { name: 'Retinol Palmitate', basePercentage: 12, costPerUnit: 125.00, function: 'Cell renewal', supplier: 'RetinoMax Corp' },
        { name: 'Aloe Vera Extract', basePercentage: 10, costPerUnit: 15.75, function: 'Soothing & healing', supplier: 'AloeNature Co' },
        { name: 'Jojoba Oil', basePercentage: 8, costPerUnit: 32.50, function: 'Natural moisturization', supplier: 'Desert Botanicals' },
        { name: 'Peptide Complex', basePercentage: 3, costPerUnit: 280.00, function: 'Collagen stimulation', supplier: 'PeptidePro Labs' },
        { name: 'Preservative System', basePercentage: 2, costPerUnit: 45.00, function: 'Product stability', supplier: 'PreserveTech Inc' }
      ]
    }
  };

  // Get base ingredients for the specific product type
  const baseIngredients = ingredientDB[industry]?.[productType] || ingredientDB[industry]?.['antioxidant-juice'] || [];
  
  // Apply AI optimization based on goals
  const optimizedIngredients = baseIngredients.map(ingredient => {
    let percentage = ingredient.basePercentage;
    let cost = ingredient.costPerUnit * percentage / 100 * 1000; // Cost for 1000L batch
    
    // Adjust based on goals
    if (goals) {
      // Budget optimization
      if (goals.budget < 30000) {
        percentage *= 0.85; // Reduce expensive ingredients
      } else if (goals.budget > 100000) {
        percentage *= 1.15; // Allow premium ingredients
      }
      
      // Nutrition target optimization
      if (goals.targetNutrition > 90) {
        if (ingredient.function.includes('antioxidant') || ingredient.function.includes('vitamin')) {
          percentage *= 1.2;
        }
      }
      
      // Sustainability optimization
      if (goals.sustainability > 80) {
        if (ingredient.supplier.includes('Natural') || ingredient.supplier.includes('Organic')) {
          percentage *= 1.1;
        }
      }
    }
    
    // Normalize percentage and recalculate cost
    percentage = Math.round(percentage * 10) / 10;
    cost = Math.round(ingredient.costPerUnit * percentage / 100 * 1000);
    
    return {
      ...ingredient,
      percentage,
      cost
    };
  });

  // Normalize percentages to total 100%
  const totalPercentage = optimizedIngredients.reduce((sum, ing) => sum + ing.percentage, 0);
  const normalizedIngredients = optimizedIngredients.map(ingredient => ({
    ...ingredient,
    percentage: Math.round((ingredient.percentage / totalPercentage * 100) * 10) / 10
  }));

  // Calculate totals
  const totalCost = normalizedIngredients.reduce((sum, ing) => sum + ing.cost, 0);
  const estimatedRetail = Math.round((totalCost * 4) / 100) / 100; // 4x markup

  // Generate scores based on ingredients and goals
  const scores = {
    nutrition: Math.min(100, Math.round(65 + (goals?.targetNutrition || 85) * 0.3)),
    sustainability: Math.min(100, Math.round(55 + (goals?.sustainability || 75) * 0.35)),
    costEfficiency: Math.min(100, Math.round(90 - (totalCost / goals?.budget * 50))),
    compliance: Math.min(100, Math.round(85 + Math.random() * 15)),
    marketAppeal: Math.min(100, Math.round(75 + Math.random() * 20))
  };

  // Generate product name based on type and key ingredients
  const productNames = {
    'antioxidant-juice': 'VitaBoost Antioxidant Blend',
    'energy-drink': 'PowerSurge Energy Formula',
    'wellness-shot': 'WellnessMax Immunity Shot',
    'multivitamin': 'CompleteVita Daily Formula',
    'face-serum': 'YouthGlow Anti-Aging Serum'
  };

  const formulation = {
    name: productNames[productType] || `Premium ${productType.replace('-', ' ')} Formula`,
    description: `AI-optimized ${industry} formulation with advanced ${productType.replace('-', ' ')} benefits`,
    ingredients: normalizedIngredients,
    totalCost,
    estimatedRetail,
    batchSize: '1000L',
    shelfLife: industry === 'cosmetics' ? '24 months' : '12 months',
    scores,
    nutritionalProfile: generateNutritionalProfile(industry, productType),
    complianceDetails: generateComplianceDetails(goals?.compliance || 'fda')
  };

  return formulation;
}

function generateNutritionalProfile(industry, productType) {
  const profiles = {
    beverages: {
      'antioxidant-juice': { calories: 45, sugar: 8, vitaminC: 120, antioxidants: 850, caffeine: 25 },
      'energy-drink': { calories: 12, sugar: 0, vitaminC: 60, antioxidants: 320, caffeine: 150 }
    },
    nutraceuticals: {
      'multivitamin': { vitaminD: '1000 IU', vitaminB12: '25 mcg', iron: '18 mg', calcium: '500 mg', magnesium: '200 mg' }
    },
    cosmetics: {
      'face-serum': { vitaminC: '15%', hyaluronicAcid: '2%', niacinamide: '5%', retinol: '0.5%', pH: '5.5' }
    }
  };

  return profiles[industry]?.[productType] || profiles[industry]?.['antioxidant-juice'] || {};
}

function generateComplianceDetails(complianceType) {
  const compliance = {
    fda: { fda: 'Approved', nonGmo: 'Verified', glutenFree: 'Certified', organic: 'Pending Certification' },
    fssai: { fssai: 'Approved', ayush: 'Certified', organic: 'Verified', halal: 'Certified' },
    eu: { ce: 'Approved', organic: 'EU Certified', vegan: 'Verified', allergenFree: 'Tested' },
    organic: { organic: 'USDA Certified', nonGmo: 'Verified', natural: 'Certified', sustainable: 'Verified' }
  };

  return compliance[complianceType] || compliance.fda;
}

// Helper functions for quality assessment
function calculateQualityScore(claudeResponse) {
  if (!claudeResponse || !claudeResponse.recipes || !claudeResponse.recipes[0]) {
    return 50; // Default score
  }
  
  const recipe = claudeResponse.recipes[0];
  const scores = recipe.scores || {};
  
  const weightedScore = (
    (scores.nutrition || 70) * 0.25 +
    (scores.sustainability || 70) * 0.2 +
    (scores.cost_efficiency || 70) * 0.2 +
    (scores.compliance || 80) * 0.2 +
    (scores.market_appeal || 70) * 0.15
  );
  
  return Math.round(weightedScore);
}

function determineQualityLevel(claudeResponse) {
  const score = calculateQualityScore(claudeResponse);
  
  if (score >= 85) return 'excellent';
  if (score >= 70) return 'good';
  if (score >= 55) return 'acceptable';
  return 'needs_optimization';
}

module.exports = router;