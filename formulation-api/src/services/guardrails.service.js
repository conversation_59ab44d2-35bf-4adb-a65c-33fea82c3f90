/**
 * Guardrails Service
 * Fetches and evaluates guardrail rules from MongoDB
 */

const GuardrailEngine = require('../lib/guardrailEngine');
const { validateGuardrail, validateGuardrailRequest } = require('../models/schemas');

class GuardrailsService {
  constructor(db) {
    this.db = db;
    this.collection = db.collection('guardrails');
    this.engine = new GuardrailEngine();
  }

  /**
   * Evaluate guardrails for given context
   * @param {Object} request - Guardrail evaluation request
   * @returns {Object} Evaluation result with issues array
   */
  async evaluate(request) {
    try {
      console.log('[GuardrailsService] Evaluating guardrails');

      // Validate request
      const validation = validateGuardrailRequest(request);
      if (!validation.valid) {
        console.error('[GuardrailsService] Invalid request:', validation.errors);
        return {
          success: false,
          errors: validation.errors
        };
      }

      const { industry, category, sub_category, answers, components } = request;

      // Build evaluation context
      const context = {
        answers: answers || {},
        components: components || {},
        industry,
        category,
        sub_category
      };

      // Fetch applicable guardrails
      const rules = await this.getApplicableRules(industry, category, sub_category);
      console.log(`[GuardrailsService] Found ${rules.length} applicable rules`);

      // Evaluate each rule
      const issues = [];
      for (const rule of rules) {
        const issue = this.engine.evaluateRule(rule, context);
        if (issue) {
          issues.push(issue);
        }
      }

      console.log(`[GuardrailsService] Evaluation complete: ${issues.length} issues found`);

      // Sort issues by severity
      const severityOrder = { error: 1, warning: 2, info: 3 };
      issues.sort((a, b) => 
        (severityOrder[a.severity] || 999) - (severityOrder[b.severity] || 999)
      );

      return {
        success: true,
        issues,
        stats: {
          rulesEvaluated: rules.length,
          errors: issues.filter(i => i.severity === 'error').length,
          warnings: issues.filter(i => i.severity === 'warning').length,
          info: issues.filter(i => i.severity === 'info').length
        }
      };
    } catch (error) {
      console.error('[GuardrailsService] Error evaluating guardrails:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get applicable guardrail rules for the given taxonomy
   * @param {string} industry - Industry slug
   * @param {string} category - Category slug
   * @param {string} subCategory - Sub-category slug
   * @returns {Array} Array of applicable guardrail rules
   */
  async getApplicableRules(industry, category, subCategory) {
    try {
      console.log(`[GuardrailsService] Fetching rules for ${industry}/${category}/${subCategory}`);

      // Build queries for different scope levels
      const queries = [
        // All scope
        { scope: 'all', status: { $ne: 'inactive' } },
        // Industry scope
        { scope: 'industry', target: industry, status: { $ne: 'inactive' } },
        // Category scope
        { scope: 'category', target: `${industry}/${category}`, status: { $ne: 'inactive' } },
        // Sub-category scope
        { scope: 'sub_category', target: `${industry}/${category}/${subCategory}`, status: { $ne: 'inactive' } }
      ];

      // Special scopes (e.g., functional-food)
      if (category === 'functional-food') {
        queries.push({ scope: 'functional-food', status: { $ne: 'inactive' } });
      }

      // Fetch all matching rules
      const rules = await this.collection
        .find({ $or: queries })
        .sort({ priority: 1 })
        .toArray();

      console.log(`[GuardrailsService] Found ${rules.length} rules`);

      // Validate rules
      const validRules = [];
      for (const rule of rules) {
        const validation = validateGuardrail(rule);
        if (validation.valid) {
          validRules.push(rule);
        } else {
          console.warn(`[GuardrailsService] Invalid rule ${rule.name}:`, validation.errors);
        }
      }

      return validRules;
    } catch (error) {
      console.error('[GuardrailsService] Error fetching rules:', error);
      throw error;
    }
  }

  /**
   * Get a single guardrail by ID
   * @param {string} id - Guardrail ID
   * @returns {Object|null} Guardrail object or null
   */
  async getById(id) {
    try {
      const rule = await this.collection.findOne({ _id: id });
      
      if (!rule) {
        return null;
      }

      const validation = validateGuardrail(rule);
      if (!validation.valid) {
        console.warn(`[GuardrailsService] Invalid rule ${id}:`, validation.errors);
        return null;
      }

      return rule;
    } catch (error) {
      console.error('[GuardrailsService] Error fetching rule:', error);
      throw error;
    }
  }

  /**
   * List all guardrails with optional filtering
   * @param {Object} filter - Optional filter criteria
   * @returns {Array} Array of guardrail rules
   */
  async list(filter = {}) {
    try {
      const query = { ...filter };
      if (!query.status) {
        query.status = { $ne: 'inactive' };
      }

      const rules = await this.collection
        .find(query)
        .project({
          _id: 1,
          name: 1,
          scope: 1,
          target: 1,
          type: 1,
          severity: 1,
          message: 1
        })
        .sort({ scope: 1, target: 1, priority: 1 })
        .toArray();

      return rules;
    } catch (error) {
      console.error('[GuardrailsService] Error listing rules:', error);
      throw error;
    }
  }

  /**
   * Test a specific rule against provided context
   * @param {string} ruleId - Rule ID to test
   * @param {Object} context - Test context
   * @returns {Object} Test result
   */
  async testRule(ruleId, context) {
    try {
      const rule = await this.getById(ruleId);
      if (!rule) {
        return {
          success: false,
          error: 'Rule not found'
        };
      }

      const issue = this.engine.evaluateRule(rule, context);

      return {
        success: true,
        triggered: !!issue,
        issue: issue || null,
        rule: {
          name: rule.name,
          type: rule.type,
          condition: rule.condition || rule.sum_config
        }
      };
    } catch (error) {
      console.error('[GuardrailsService] Error testing rule:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = GuardrailsService;