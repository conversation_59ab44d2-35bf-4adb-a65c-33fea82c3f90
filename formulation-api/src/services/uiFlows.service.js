/**
 * UI Flows Service
 * Manages UI flow configurations from MongoDB
 */

const { validateUIFlow } = require('../models/schemas');

class UIFlowsService {
  constructor(db) {
    this.db = db;
    this.collection = db.collection('ui_flows');
  }

  /**
   * Get UI flow configuration by slug
   * @param {string} slug - Flow slug (e.g., 'formulation_wizard_v2')
   * @returns {Object|null} UI flow configuration or null
   */
  async getBySlug(slug) {
    try {
      console.log(`[UIFlowsService] Fetching UI flow: ${slug}`);

      const flow = await this.collection.findOne({ slug });

      if (!flow) {
        console.log(`[UIFlowsService] UI flow not found: ${slug}`);
        return null;
      }

      const validation = validateUIFlow(flow);
      if (!validation.valid) {
        console.warn(`[UIFlowsService] Invalid UI flow ${slug}:`, validation.errors);
        return null;
      }

      console.log(`[UIFlowsService] Found flow with ${flow.steps.length} steps`);
      return flow;
    } catch (error) {
      console.error('[UIFlowsService] Error fetching UI flow:', error);
      throw error;
    }
  }

  /**
   * Get all available UI flows
   * @returns {Array} Array of UI flow objects
   */
  async listAll() {
    try {
      console.log('[UIFlowsService] Fetching all UI flows');

      const flows = await this.collection
        .find({ status: { $ne: 'inactive' } })
        .project({
          slug: 1,
          name: 1,
          description: 1,
          version: 1,
          status: 1
        })
        .toArray();

      console.log(`[UIFlowsService] Found ${flows.length} UI flows`);
      return flows;
    } catch (error) {
      console.error('[UIFlowsService] Error fetching UI flows:', error);
      throw error;
    }
  }

  /**
   * Get step configuration from a flow
   * @param {string} flowSlug - Flow slug
   * @param {string} stepSlug - Step slug
   * @returns {Object|null} Step configuration or null
   */
  async getStep(flowSlug, stepSlug) {
    try {
      console.log(`[UIFlowsService] Fetching step ${stepSlug} from flow ${flowSlug}`);

      const flow = await this.getBySlug(flowSlug);
      if (!flow) {
        return null;
      }

      const step = flow.steps.find(s => s.slug === stepSlug);
      if (!step) {
        console.log(`[UIFlowsService] Step not found: ${stepSlug}`);
        return null;
      }

      return step;
    } catch (error) {
      console.error('[UIFlowsService] Error fetching step:', error);
      throw error;
    }
  }

  /**
   * Get global bindings from a flow
   * @param {string} flowSlug - Flow slug
   * @returns {Object} Global bindings configuration
   */
  async getGlobalBindings(flowSlug) {
    try {
      console.log(`[UIFlowsService] Fetching global bindings for flow: ${flowSlug}`);

      const flow = await this.getBySlug(flowSlug);
      if (!flow) {
        return {};
      }

      return flow.bindings?.global || {};
    } catch (error) {
      console.error('[UIFlowsService] Error fetching global bindings:', error);
      throw error;
    }
  }

  /**
   * Validate if a step transition is allowed
   * @param {string} flowSlug - Flow slug
   * @param {string} currentStep - Current step slug
   * @param {string} nextStep - Next step slug
   * @returns {boolean} Whether transition is allowed
   */
  async validateTransition(flowSlug, currentStep, nextStep) {
    try {
      const flow = await this.getBySlug(flowSlug);
      if (!flow) {
        return false;
      }

      const currentIndex = flow.steps.findIndex(s => s.slug === currentStep);
      const nextIndex = flow.steps.findIndex(s => s.slug === nextStep);

      if (currentIndex === -1 || nextIndex === -1) {
        return false;
      }

      // For now, allow forward progression and backward navigation
      // Can add more complex validation rules here
      return nextIndex === currentIndex + 1 || nextIndex < currentIndex;
    } catch (error) {
      console.error('[UIFlowsService] Error validating transition:', error);
      return false;
    }
  }

  /**
   * Get the next step in the flow
   * @param {string} flowSlug - Flow slug
   * @param {string} currentStep - Current step slug
   * @returns {Object|null} Next step configuration or null
   */
  async getNextStep(flowSlug, currentStep) {
    try {
      const flow = await this.getBySlug(flowSlug);
      if (!flow) {
        return null;
      }

      const currentIndex = flow.steps.findIndex(s => s.slug === currentStep);
      if (currentIndex === -1 || currentIndex >= flow.steps.length - 1) {
        return null;
      }

      return flow.steps[currentIndex + 1];
    } catch (error) {
      console.error('[UIFlowsService] Error getting next step:', error);
      return null;
    }
  }

  /**
   * Get the previous step in the flow
   * @param {string} flowSlug - Flow slug
   * @param {string} currentStep - Current step slug
   * @returns {Object|null} Previous step configuration or null
   */
  async getPreviousStep(flowSlug, currentStep) {
    try {
      const flow = await this.getBySlug(flowSlug);
      if (!flow) {
        return null;
      }

      const currentIndex = flow.steps.findIndex(s => s.slug === currentStep);
      if (currentIndex <= 0) {
        return null;
      }

      return flow.steps[currentIndex - 1];
    } catch (error) {
      console.error('[UIFlowsService] Error getting previous step:', error);
      return null;
    }
  }
}

module.exports = UIFlowsService;