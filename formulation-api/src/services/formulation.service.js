const logger = require('../utils/logger');
const claudeService = require('./claudeService');
const Project = require('../models/mongo/Project');

function calculateQualityScore(resp) {
  try {
    if (resp?.scores?.overall) return resp.scores.overall;
  } catch {}
  return 70;
}

function determineQualityLevel(resp) {
  const score = calculateQualityScore(resp);
  if (score >= 85) return 'excellent';
  if (score >= 70) return 'good';
  if (score >= 50) return 'acceptable';
  return 'needs_optimization';
}

async function generateAndPersist({ projectId, userId, formData }) {
  const project = await Project.findById(projectId);
  if (!project) throw new Error(`Project not found: ${projectId}`);

  logger.info('Starting formulation generation (service)', {
    projectId,
    userId,
    industry: formData?.industry,
    productType: formData?.productType || formData?.sub_category || formData?.category
  });

  const claudeResponse = await claudeService.generateFormulation(formData);

  const formulationData = {
    version: 1,
    generated_at: new Date(),
    quality_score: calculateQualityScore(claudeResponse),
    quality_level: determineQualityLevel(claudeResponse),
    ready_for_results: true,
    recipes: (claudeResponse.recipes || []).map((recipe, index) => ({
      id: `recipe_${index + 1}`,
      name: recipe.name,
      description: recipe.description,
      ingredients: recipe.ingredients || [],
      nutritional_profile: recipe.nutritional_profile || {},
      cost_analysis: recipe.cost_analysis || {},
      scores: recipe.scores || {}
    })),
    variations: claudeResponse.variations || [],
    validation_errors: [],
    conversation_history: [{
      role: 'system',
      message: 'Initial formulation generated',
      timestamp: new Date()
    }]
  };

  await Project.updateOne(
    { _id: project._id },
    {
      current_formulation: formulationData,
      $push: {
        formulation_versions: {
          version: 1,
          formulation_data: formulationData,
          created_at: new Date(),
          changes_summary: 'Initial AI-generated formulation'
        }
      },
      updated_at: new Date(),
      status: 'ready',
      generation_completed_at: new Date()
    }
  );

  logger.info('Formulation generated (service) successfully', { projectId, version: 1 });

  return { projectId, formulation: formulationData };
}

module.exports = { generateAndPersist };

