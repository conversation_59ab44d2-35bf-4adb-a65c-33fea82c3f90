const Anthropic = require('@anthropic-ai/sdk');
const logger = require('../utils/logger');
const templateEngine = require('../utils/templateEngine');
const Ajv = require('ajv');
const formulationSchema = require('../schemas/formulationSchema.json');
const FormulationBinding = require('../models/mongo/FormulationBinding');
const FormulationBlock = require('../models/mongo/FormulationBlock');
const SchemaBuilder = require('./schemaBuilder');

class ClaudeService {
  constructor() {
    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY || process.env.CLAUDE_API_KEY,
    });
    
    // Initialize JSON schema validator
    this.ajv = new Ajv({ allErrors: true });
    this.validateSchema = this.ajv.compile(formulationSchema);
  }

  /**
   * Fetch formulation blocks based on taxonomy hierarchy
   */
  async getFormulationBlocks(formData) {
    try {
      const { industry, category, subCategory } = formData;
      const blocks = new Set();
      
      // Fetch blocks for industry level
      if (industry) {
        const industryBlocks = await FormulationBinding.getBlocksForTaxonomy('industry', industry);
        industryBlocks.forEach(block => blocks.add(block));
      }
      
      // Fetch blocks for category level (more specific)
      if (category) {
        const categoryBlocks = await FormulationBinding.getBlocksForTaxonomy('category', category);
        categoryBlocks.forEach(block => blocks.add(block));
      }
      
      // Fetch blocks for sub_category level (most specific)
      if (subCategory) {
        const subCategoryBlocks = await FormulationBinding.getBlocksForTaxonomy('sub_category', subCategory);
        subCategoryBlocks.forEach(block => blocks.add(block));
      }
      
      return Array.from(blocks).sort((a, b) => {
        // Sort by category priority, then by display_order
        const categoryOrder = { 'core': 1, 'analysis': 2, 'compliance': 3, 'optional': 4 };
        const aCategoryOrder = categoryOrder[a.category] || 5;
        const bCategoryOrder = categoryOrder[b.category] || 5;
        
        if (aCategoryOrder !== bCategoryOrder) {
          return aCategoryOrder - bCategoryOrder;
        }
        
        return (a.display_order || 0) - (b.display_order || 0);
      });
    } catch (error) {
      logger.error('Failed to fetch formulation blocks', {
        error: error.message,
        formData: { industry: formData.industry, category: formData.category, subCategory: formData.subCategory }
      });
      return [];
    }
  }

  /**
   * Build formulation blocks context for prompt
   */
  buildBlocksContext(blocks) {
    if (!blocks || blocks.length === 0) {
      return '';
    }
    
    const coreBlocks = blocks.filter(c => c.category === 'core');
    const analysisBlocks = blocks.filter(c => c.category === 'analysis');
    const complianceBlocks = blocks.filter(c => c.category === 'compliance');
    const optionalBlocks = blocks.filter(c => c.category === 'optional');
    
    let context = '\n## Formulation Blocks Requirements\n';
    
    if (coreBlocks.length > 0) {
      context += '\n### Core Blocks (Required)\n';
      coreBlocks.forEach(block => {
        context += `- **${block.name}**: ${block.description}`;
        if (block.rules) {
          context += ` (${block.rules})`;
        }
        context += '\n';
      });
    }
    
    if (analysisBlocks.length > 0) {
      context += '\n### Analysis Blocks\n';
      analysisBlocks.forEach(block => {
        context += `- **${block.name}**: ${block.description}`;
        if (block.rules) {
          context += ` (${block.rules})`;
        }
        context += '\n';
      });
    }
    
    if (complianceBlocks.length > 0) {
      context += '\n### Compliance Blocks\n';
      complianceBlocks.forEach(block => {
        context += `- **${block.name}**: ${block.description}`;
        if (block.rules) {
          context += ` (${block.rules})`;
        }
        context += '\n';
      });
    }
    
    if (optionalBlocks.length > 0) {
      context += '\n### Optional Blocks\n';
      optionalBlocks.forEach(block => {
        context += `- **${block.name}**: ${block.description}`;
        if (block.rules) {
          context += ` (${block.rules})`;
        }
        context += '\n';
      });
    }
    
    return context;
  }

  /**
   * Generate a comprehensive LLM prompt using template engine
   */
  buildFormulationPrompt(formData) {
    const { industry, productType, productDescription, goals = {}, industryParams = {} } = formData;
    
    // Prepare template data
    const templateData = {
      industry: industry || 'beverages',
      productType: productType || 'custom',
      productDescription: productDescription || 'Premium health formulation',
      budgetPerUnit: goals.budgetPerUnit || 2000,
      nutritionScore: goals.nutritionScore || 80,
      sustainability: goals.sustainability || 70,
      shelfLifeDays: goals.shelfLifeDays || 365,
      veganFriendly: goals.veganFriendly || false,
      organicCertified: goals.organicCertified || false,
      lowCalorie: goals.lowCalorie || false,
      industryParams: Object.entries(industryParams).map(([key, value]) => ({
        label: this.formatParameterLabel(key),
        value: value,
        unit: this.getParameterUnit(key),
        description: this.getParameterDescription(key)
      }))
    };

    return templateEngine.render('formulationPrompt', templateData);
  }

  formatParameterLabel(key) {
    const labelMap = {
      oracValue: 'ORAC Target (Antioxidant Capacity)',
      brixLevel: 'Brix Level (Sugar Content)',
      phLevel: 'pH Level',
      caffeineContent: 'Caffeine Content',
      rdaPercentage: 'RDA Percentage Target',
      bioavailability: 'Bioavailability Enhancement',
      activeConcentration: 'Active Ingredient Concentration',
      skinType: 'Target Skin Type',
      texture: 'Texture Preference'
    };
    return labelMap[key] || key;
  }

  getParameterUnit(key) {
    const unitMap = {
      oracValue: 'μmol TE/100g',
      brixLevel: '°Bx',
      phLevel: 'pH',
      caffeineContent: 'mg/serving',
      rdaPercentage: '% RDA',
      bioavailability: '%',
      activeConcentration: 'mg/serving'
    };
    return unitMap[key] || '';
  }

  getParameterDescription(key) {
    const descMap = {
      oracValue: 'Oxygen Radical Absorbance Capacity - industry standard for antioxidant measurement',
      brixLevel: 'Total soluble solids content - critical for taste and preservation',
      phLevel: 'Acidity/alkalinity balance for stability and efficacy',
      caffeineContent: 'Natural stimulant content for energy formulations'
    };
    return descMap[key] || '';
  }

  /**
   * Generate 3-5 line R&D assistant style notes
   */
  async generateNotes(context) {
    const { industry, category, subCategory, goals = {} } = context || {};
    const prompt = `You are a senior formulation PM writing a quick handoff note for colleagues.
Rewrite the goals into 2–4 short lines in plain, conversational industry language.
Style: friendly, practical, and easy to act on. Avoid heavy lab jargon.
Guidelines:
- Prefer everyday terms (e.g. "light cream" over "oil-in-water emulsion").
- Keep key numbers but use natural phrasing ("around", "about", "keep at").
- Mention packaging and skin type in simple words if present.
- No greetings, no bullets, no headings — just 2–4 sentences of plain text.

Context:
- Industry: ${industry || 'n/a'}
- Category: ${category || 'n/a'}
- Product type: ${subCategory || 'n/a'}
- Goals: ${JSON.stringify(goals)}

Return only the rewritten note as plain text.`;

    try {
      const resp = await this.anthropic.messages.create({
        model: process.env.CLAUDE_MODEL || 'claude-3-5-sonnet-20240620',
        max_tokens: 220,
        temperature: 0.5,
        messages: [
          { role: 'user', content: prompt }
        ]
      });
      const text = resp?.content?.[0]?.text || '';
      logger.info('Claude notes generated');
      return text.trim();
    } catch (e) {
      logger.error('Claude notes generation error', { error: e.message });
      throw e;
    }
  }

  /**
   * Legacy method for backwards compatibility
   */
  buildFormulationPromptLegacy(formData, blocksContext = '') {
    const { industry, productType, productDescription, goals, industryParams } = formData;
    
    // Build industry-specific context
    const industryContext = {
      beverages: "functional beverages including juices, energy drinks, wellness shots, and enhanced waters",
      nutraceuticals: "dietary supplements, vitamins, health formulations including capsules, tablets, powders, and liquids",
      cosmetics: "natural skincare, Ayurvedic formulations including serums, creams, oils, and cleansers"
    };

    // Build compliance requirements from boolean flags
    const buildComplianceRequirements = (compliance) => {
      const requirements = [];
      if (compliance?.veganFriendly) requirements.push("Vegan-friendly (no animal-derived ingredients)");
      if (compliance?.organicCertified) requirements.push("Organic certification compliance (USDA/EU standards)");
      if (compliance?.lowCalorie) requirements.push("Low calorie formulation");
      return requirements.length > 0 ? requirements.join(', ') : 'Standard regulatory compliance';
    };

    // Build industry-specific parameters context dynamically
    const buildIndustryContext = (industry, params) => {
      if (!params || Object.keys(params).length === 0) return '';
      
      const contextLines = [];
      Object.entries(params).forEach(([key, value]) => {
        if (value) {
          switch (key) {
            case 'flavor':
              contextLines.push(`- Flavor Profile: ${value.charAt(0).toUpperCase() + value.slice(1)}`);
              break;
            case 'absorption':
              contextLines.push(`- Bioavailability Target: ${value}%`);
              break;
            case 'packaging':
              contextLines.push(`- Packaging Form: ${value.charAt(0).toUpperCase() + value.slice(1)}`);
              break;
            case 'skinType':
              contextLines.push(`- Target Skin Type: ${value.charAt(0).toUpperCase() + value.slice(1)}`);
              break;
            case 'texture':
              contextLines.push(`- Texture Preference: ${value.charAt(0).toUpperCase() + value.slice(1)}`);
              break;
          }
        }
      });
      return contextLines.length > 0 ? '\n' + contextLines.join('\n') : '';
    };

    const industrySpecificContext = buildIndustryContext(industry, industryParams);

    // Format shelf life in readable format
    const formatShelfLife = (days) => {
      if (days >= 365) {
        const years = Math.round(days / 365 * 10) / 10;
        return `${years} year${years !== 1 ? 's' : ''}`;
      }
      if (days >= 30) {
        const months = Math.round(days / 30);
        return `${months} month${months !== 1 ? 's' : ''}`;
      }
      return `${days} days`;
    };

    const prompt = `
CRITICAL: You MUST respond with VALID JSON ONLY. No explanatory text, no apologies, no questions. Start immediately with { and end with }.

You are an expert formulation scientist with deep knowledge in ${industryContext[industry]}. Create a comprehensive, scientifically-backed formulation recipe based on the following requirements:

## Product Information
- **Industry**: ${industry.charAt(0).toUpperCase() + industry.slice(1)}
- **Product Type**: ${productType}
- **Product Description**: ${productDescription}${industrySpecificContext}

## Goals & Requirements
- **Budget per Unit**: ₹${goals?.budgetPerUnit || 2000}
- **Nutrition Score Target**: ${goals?.nutritionScore || 80}%
- **Sustainability Priority**: ${goals?.sustainability || 70}%
- **Shelf Life Requirement**: ${formatShelfLife(goals?.shelfLifeDays || 365)}
- **Compliance Requirements**: ${buildComplianceRequirements(goals?.compliance)}${blocksContext}

## Response Requirements
Provide a JSON response with exactly this structure:

{
  "recipes": [
    {
      "name": "Primary Recipe Name",
      "recipe_type": "main",
      "description": "Detailed description of the formulation",
      "targetMarket": "Primary target geography and market segment",
      "ingredients": [
        {
          "name": "Ingredient Name",
          "percentage": 25.5,
          "function": "Primary function in formulation",
          "cost_per_kg": 15.50,
          "supplier_suggestions": [
            {
              "name": "Premium Supplier Co",
              "location": "Mumbai, Maharashtra, India",
              "quality_grade": "Food Grade",
              "certification": "ISO 22000",
              "lead_time_days": 15
            },
            {
              "name": "BioSource Industries",
              "location": "Bangalore, Karnataka, India",
              "quality_grade": "Pharma Grade", 
              "certification": "GMP Certified",
              "lead_time_days": 12
            }
          ],
          "sourcing_region": "Primary sourcing location",
          "bom_details": {
            "quantity_per_batch": "255kg",
            "lead_time_days": 14,
            "storage_requirements": "Cool, dry place",
            "quality_grade": "Food Grade"
          }
        }
      ],
      "nutritional_profile": {
        "macronutrients": {
          "protein_g": 8.5,
          "carbohydrates_g": 18.5,
          "fat_g": 0.8,
          "fiber_g": 3.2,
          "calories_per_serving": 125
        },
        "micronutrients": {
          "vitamin_c_mg": 85,
          "vitamin_d_iu": 400,
          "calcium_mg": 150,
          "iron_mg": 8,
          "magnesium_mg": 65
        },
        "bioactive_compounds": {
          "antioxidants_orac": 8500,
          "polyphenols_mg": 125,
          "omega3_mg": 250
        },
        "daily_value_percentages": {
          "vitamin_c": 94,
          "vitamin_d": 100,
          "calcium": 15,
          "iron": 44
        }
      },
      "production_specs": {
        "batch_size": "1000L or equivalent",
        "shelf_life": "Expected shelf life",
        "storage_conditions": "Storage requirements",
        "manufacturing_process": "Brief process overview",
        "detailed_manufacturing_procedure": {
          "pre_processing": ["Step 1", "Step 2", "Step 3"],
          "mixing_procedure": ["Mixing step 1", "Mixing step 2"],
          "quality_control": ["QC checkpoint 1", "QC checkpoint 2"],
          "packaging_steps": ["Package step 1", "Package step 2"],
          "total_time_hours": 8,
          "equipment_required": ["Equipment 1", "Equipment 2"],
          "critical_control_points": ["CCP 1", "CCP 2"]
        }
      },
      "compliance_details": {
        "regulatory_status": {
          "fda": "GRAS approved",
          "fssai": "Approved for food use",
          "eu": "Novel food approved",
          "organic": "USDA Organic certified"
        },
        "certifications_needed": ["List of required certifications"],
        "labeling_requirements": "Key labeling considerations"
      },
      "cost_analysis": {
        "raw_material_cost": 1500.00,
        "processing_cost": 300.00,
        "packaging_cost": 200.00,
        "total_cogs": 2000.00,
        "suggested_retail": 8000.00,
        "margin_percentage": 75
      },
      "scores": {
        "nutrition": 88,
        "sustainability": 82,
        "cost_efficiency": 78,
        "compliance": 95,
        "market_appeal": 85
      }
    }
  ],
  "recipes": [
    {
      "name": "Creative Regional Market Name (e.g., 'Ayurvedic Heritage Plus', 'Mumbai Energy Blend')",
      "recipe_type": "regional",
      "target_geography": "india_traditional",
      "key_differences": "Specific regional advantages and adaptations that make this unique for Indian market",
      "ingredients": [
        {
          "name": "Ingredient Name",
          "percentage": 25.5,
          "function": "Primary function in formulation",
          "cost_per_kg": 15.50,
          "supplier_suggestions": [
            {
              "name": "Premium Supplier Co",
              "location": "Mumbai, Maharashtra, India",
              "quality_grade": "Food Grade",
              "certification": "ISO 22000",
              "lead_time_days": 15
            },
            {
              "name": "BioSource Industries",
              "location": "Bangalore, Karnataka, India",
              "quality_grade": "Pharma Grade", 
              "certification": "GMP Certified",
              "lead_time_days": 12
            }
          ],
          "sourcing_region": "Primary sourcing location",
          "bom_details": {
            "quantity_per_batch": "255kg",
            "lead_time_days": 14,
            "storage_requirements": "Cool, dry place",
            "quality_grade": "Food Grade"
          }
        }
      ],
      "cost_analysis": {
        "raw_material_cost": 1200.00,
        "processing_cost": 250.00,
        "packaging_cost": 150.00,
        "total_cogs": 1600.00,
        "suggested_retail": 6400.00,
        "margin_percentage": 75
      },
      "scores": {
        "nutrition": 88,
        "sustainability": 82,
        "cost_efficiency": 78,
        "compliance": 95,
        "market_appeal": 85
      }
    },
    {
      "name": "Creative Premium Name (e.g., 'Elite Performance Formula', 'Premium Gold Series')",
      "recipe_type": "premium",
      "target_geography": "urban_premium",
      "key_differences": "Premium features and enhanced benefits that justify higher price point",
      "ingredients": [
        {
          "name": "Ingredient Name",
          "percentage": 25.5,
          "function": "Primary function in formulation",
          "cost_per_kg": 15.50,
          "supplier_suggestions": [
            {
              "name": "Premium Supplier Co",
              "location": "Mumbai, Maharashtra, India",
              "quality_grade": "Food Grade",
              "certification": "ISO 22000",
              "lead_time_days": 15
            },
            {
              "name": "BioSource Industries",
              "location": "Bangalore, Karnataka, India",
              "quality_grade": "Pharma Grade", 
              "certification": "GMP Certified",
              "lead_time_days": 12
            }
          ],
          "sourcing_region": "Primary sourcing location",
          "bom_details": {
            "quantity_per_batch": "255kg",
            "lead_time_days": 14,
            "storage_requirements": "Cool, dry place",
            "quality_grade": "Food Grade"
          }
        }
      ],
      "cost_analysis": {
        "raw_material_cost": 2000.00,
        "processing_cost": 400.00,
        "packaging_cost": 300.00,
        "total_cogs": 2700.00,
        "suggested_retail": 10800.00,
        "margin_percentage": 75
      },
      "scores": {
        "nutrition": 95,
        "sustainability": 85,
        "cost_efficiency": 65,
        "compliance": 96,
        "market_appeal": 92
      }
    },
    {
      "name": "Creative Value Name (e.g., 'Essential Wellness', 'Smart Choice Formula')",
      "recipe_type": "budget",
      "target_geography": "mass_market",
      "key_differences": "Cost-optimized benefits and value proposition for price-conscious consumers",
      "ingredients": [
        {
          "name": "Ingredient Name",
          "percentage": 25.5,
          "function": "Primary function in formulation",
          "cost_per_kg": 15.50,
          "supplier_suggestions": [
            {
              "name": "Premium Supplier Co",
              "location": "Mumbai, Maharashtra, India",
              "quality_grade": "Food Grade",
              "certification": "ISO 22000",
              "lead_time_days": 15
            },
            {
              "name": "BioSource Industries",
              "location": "Bangalore, Karnataka, India",
              "quality_grade": "Pharma Grade", 
              "certification": "GMP Certified",
              "lead_time_days": 12
            }
          ],
          "sourcing_region": "Primary sourcing location",
          "bom_details": {
            "quantity_per_batch": "255kg",
            "lead_time_days": 14,
            "storage_requirements": "Cool, dry place",
            "quality_grade": "Food Grade"
          }
        }
      ],
      "cost_analysis": {
        "raw_material_cost": 800.00,
        "processing_cost": 200.00,
        "packaging_cost": 100.00,
        "total_cogs": 1100.00,
        "suggested_retail": 4400.00,
        "margin_percentage": 75
      },
      "scores": {
        "nutrition": 75,
        "sustainability": 70,
        "cost_efficiency": 92,
        "compliance": 94,
        "market_appeal": 78
      }
    }
  ],
  "sustainability_report": {
    "carbon_footprint": "Estimated carbon footprint",
    "sustainable_sourcing": "Percentage of sustainably sourced ingredients",
    "packaging_recommendations": "Eco-friendly packaging suggestions",
    "certifications": ["Organic", "Fair Trade", etc.]
  },
  "market_positioning": {
    "unique_selling_points": ["Key differentiators"],
    "target_consumer": "Primary consumer demographics",
    "price_positioning": "Premium/Mid-tier/Value",
    "marketing_claims": ["Scientifically-backed marketing claims"]
  }
}

## CRITICAL REQUIREMENTS:
1. **EXACTLY 3 VARIATIONS** - No more, no less. Do not add a 4th or 5th variation under any circumstances.
2. **COMPLETE INGREDIENT LISTS** - Each recipe and variation must have 4-8 ingredients with percentages that sum to 100%
3. **DISTINCTIVE NAMES** - Create market-appropriate names for each variation
4. **SUBSTANTIAL DIFFERENCES** - Each variation must have clear, meaningful differences
5. **DETAILED MANUFACTURING PROCEDURES** - Include step-by-step manufacturing procedures for each recipe
6. **COMPLETE BOM DETAILS** - Provide comprehensive Bill of Materials with quantities, lead times, and specifications
7. **QUALITY GRADES** - Use ONLY these exact values: "Food Grade", "Pharma Grade", "Cosmetic Grade", "Organic Certified"

## Variation Requirements:
- **Variation 1**: Regional/Traditional market focus with complete nutritional profile
- **Variation 2**: Premium market with enhanced features and superior nutritional values
- **Variation 3**: Value market with cost optimization but adequate nutritional content
- **Each Variation**: Must include complete nutritional profile following the same requirements as main recipe

## Additional Guidelines:
- All ingredients must be food-grade/cosmetic-grade and legally allowed in target markets
- **CRITICAL**: quality_grade MUST be EXACTLY one of: "Food Grade", "Pharma Grade", "Cosmetic Grade", "Organic Certified"
- Ensure realistic cost structures and proper ingredient sourcing for each variation
- Consider synergistic effects between ingredients in all formulations
- Key differences must clearly explain what makes each variation unique

## Manufacturing & BOM Requirements:
- Include detailed manufacturing procedures with step-by-step processes
- Provide BOM details with quantities, lead times, and storage requirements

## Nutritional Profile Requirements:
- **MANDATORY**: Provide complete nutritional values for ALL required fields - no zero, null, or empty values allowed
- **Realistic Values**: Base nutritional content on actual ingredient composition and industry standards
- **Per 250ml Serving**: All values must be calculated for a standard 250ml serving size
- **Required Macronutrients**: protein_g (minimum 1.0), carbohydrates_g (minimum 5.0), fat_g (minimum 0.1), fiber_g (minimum 0.5), calories_per_serving (minimum 25)
- **Required Micronutrients**: vitamin_c_mg (minimum 10), vitamin_d_iu (if applicable, minimum 100), calcium_mg (minimum 25), iron_mg (minimum 1), magnesium_mg (minimum 10)
- **Bioactive Compounds**: antioxidants_orac (minimum 1000), polyphenols_mg (minimum 25), omega3_mg (if applicable, minimum 50)
- **Daily Value %**: Calculate realistic daily value percentages - vitamin_c (minimum 10), vitamin_d (if present, minimum 25), calcium (minimum 5), iron (minimum 10)
- **Calculation Base**: Sum up nutritional contributions from ALL ingredients in the formulation
- **Industry Context**: For beverages - higher carbs/vitamins, for nutraceuticals - higher active compounds, for cosmetics - focus on bioactives
- **No Placeholder Values**: Every nutritional value must be realistic and ingredient-derived

**GENERATE EXACTLY 10 RECIPES** - Include 1 main recipe and 9 alternative recipes (regional, premium, budget, etc.).

Create formulations that are innovative yet practical, scientifically sound, and commercially viable.

FINAL INSTRUCTION: Respond IMMEDIATELY with complete JSON object starting with { and ending with }. NO OTHER TEXT ALLOWED.
`;

    return prompt;
  }

  /**
   * Build formulation prompt with dynamic schema
   */
  buildFormulationPromptWithSchema(formData, blocksContext = '', dynamicSchema = {}) {
    const schemaString = JSON.stringify(dynamicSchema, null, 2);
    
    const prompt = `You are an expert food scientist and formulation specialist. Create a comprehensive product formulation based on the following requirements:

## Product Requirements:
- **Industry**: ${formData.industry}
- **Category**: ${formData.category || 'Not specified'}
- **Sub-Category**: ${formData.subCategory || 'Not specified'}
- **Product Type**: ${formData.productType}
- **Product Description**: ${formData.productDescription || 'Not provided'}
- **Target Market**: ${formData.targetMarket || 'General consumer'}
- **Budget per Unit**: ${formData.goals?.budgetPerUnit || 'Not specified'}
- **Compliance Requirements**: ${formData.goals?.compliance || 'Standard food safety'}

## Available Formulation Blocks:
${blocksContext}

## Dynamic Schema Requirements:
You MUST structure your response according to this exact JSON schema:
\`\`\`json
${schemaString}
\`\`\`

## Instructions:
1. **Follow the schema exactly** - Include all required fields and respect the structure
2. **Use formulation blocks** - Incorporate relevant blocks from the available list
3. **Be scientifically accurate** - Ensure all formulations are technically feasible
4. **Consider regulations** - Ensure compliance with food safety and labeling requirements
5. **Optimize for goals** - Balance cost, quality, and market requirements
6. **Generate multiple recipes** - Provide variations for different market segments

## Response Format:
- Respond with ONLY a valid JSON object
- No additional text or explanations outside the JSON
- Ensure all numeric values are realistic and achievable
- Include detailed ingredient specifications with suppliers when possible

FINAL INSTRUCTION: Respond IMMEDIATELY with complete JSON object starting with { and ending with }. NO OTHER TEXT ALLOWED.
`;

    return prompt;
  }

  /**
   * Generate formulation using Claude AI with comprehensive prompt
   */
  async generateFormulation(formData) {
    try {
      logger.info('Generating comprehensive formulation with Claude AI', {
        industry: formData.industry,
        productType: formData.productType,
        budget: formData.goals?.budgetPerUnit,
        compliance: formData.goals?.compliance
      });

      // Build dynamic schema based on taxonomy and formulation blocks
      const schemaBuilder = new SchemaBuilder();
      const dynamicSchema = await schemaBuilder.getSchemaForTaxonomy(
        formData.industry,
        formData.category,
        formData.subCategory
      );
      
      logger.debug('Built dynamic schema', {
        industry: formData.industry,
        category: formData.category,
        subCategory: formData.subCategory,
        schemaKeys: Object.keys(dynamicSchema.properties || {})
      });

      // Fetch formulation blocks based on taxonomy
      const blocks = await this.getFormulationBlocks(formData);
      const blocksContext = this.buildBlocksContext(blocks);
      
      logger.debug('Fetched formulation blocks', {
        blockCount: blocks.length,
        blocks: blocks.map(c => ({ key: c.key, name: c.name, category: c.category }))
      });

      const prompt = this.buildFormulationPromptWithSchema(formData, blocksContext, dynamicSchema);
      
      logger.debug('Generated prompt length', { 
        promptLength: prompt.length,
        industry: formData.industry 
      });

      const response = await this.anthropic.messages.create({
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 8000, // Maximum tokens for comprehensive responses
        temperature: 0.1, // Very low for consistent JSON structure
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      });

      const content = response.content[0].text;
      
      // Parse JSON response
      let parsedResponse;
      try {
        // Extract JSON from response (in case there's additional text)
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          parsedResponse = JSON.parse(jsonMatch[0]);
        } else {
          parsedResponse = JSON.parse(content);
        }
      } catch (parseError) {
        logger.error('Failed to parse Claude response as JSON', {
          error: parseError.message,
          content: content.substring(0, 500)
        });
        throw new Error('Invalid JSON response from AI');
      }

      // Ensure maximum 10 recipes (truncate if Claude provided more)
      if (parsedResponse.recipes && parsedResponse.recipes.length > 10) {
        logger.warn('Claude returned more than 10 recipes, truncating to first 10', {
          originalCount: parsedResponse.recipes.length
        });
        parsedResponse.recipes = parsedResponse.recipes.slice(0, 10);
      }

      // Validate response structure using dynamic schema
      this.validateResponseWithSchema(parsedResponse, dynamicSchema);

      // Validate we have between 4-10 recipes
      const totalRecipes = parsedResponse.recipes?.length || 0;
      
      logger.info('Claude AI formulation generated successfully', {
        mainRecipes: parsedResponse.recipes?.length || 0,
        alternative_recipes: (parsedResponse.recipes?.length || 0) - 1,
        totalRecipes: totalRecipes
      });

      // Use recipes array directly
      const allRecipes = parsedResponse.recipes || [];

      return {
        ...parsedResponse,
        allRecipes: allRecipes // Provide easy access to all 4 recipes
      };

    } catch (error) {
      logger.error('Claude AI formulation generation failed', {
        error: error.message,
        stack: error.stack
      });

      // Return error instead of mock data - force real Claude usage
      throw new Error(`Claude AI formulation generation failed: ${error.message}`);
    }
  }

  /**
   * Validate Claude AI response structure using JSON schema
   */
  validateResponseWithSchema(response, schema = null) {
    // Use provided schema or fall back to static schema
    const validationSchema = schema || formulationSchema;
    
    // Create validator for the specific schema
    const ajv = new Ajv({ allErrors: true });
    const validate = ajv.compile(validationSchema);
    
    const isValid = validate(response);
    
    if (!isValid) {
      const errors = validate.errors;
      const errorMessages = errors.map(error => 
        `${error.instancePath}: ${error.message}`
      ).join(', ');
      
      logger.error('Claude response validation failed', {
        errors: errors,
        errorMessages: errorMessages,
        usingDynamicSchema: !!schema
      });
      
      throw new Error(`Claude response validation failed: ${errorMessages}`);
    }

    // Additional business logic validation
    const totalRecipes = response.recipes?.length || 0;
    if (totalRecipes < 4 || totalRecipes > 10) {
      throw new Error(`Expected 4-10 recipes, got ${totalRecipes}`);
    }

    logger.info('Claude response validated successfully against schema', {
      usingDynamicSchema: !!schema
    });
    return true;
  }

  /**
   * Legacy validation method - kept for backwards compatibility
   */
  validateClaudeResponse(response) {
    const required = ['recipes'];
    const missing = required.filter(field => !response[field]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required fields in Claude response: ${missing.join(', ')}`);
    }

    if (!Array.isArray(response.recipes) || response.recipes.length === 0) {
      throw new Error('Invalid recipes array in Claude response');
    }

    if (!Array.isArray(response.recipes) || response.recipes.length < 4 || response.recipes.length > 10) {
      throw new Error('Claude response must include 4-10 recipes');
    }

    return true;
  }

  /**
   * Legacy mock formulation generator - DEPRECATED
   * This method is kept for reference but should not be used
   */
  generateMockFormulationDEPRECATED(formData) {
    const { industry, productType, goals, productDescription } = formData;
    
    // Generate dynamic ingredients based on industry and compliance
    const generateIngredients = (industry, goals) => {
      const baseIngredients = {
        beverages: [
          { name: "Natural Fruit Extract", percentage: 35.0, function: "Primary flavor and nutrients", cost_per_kg: 15.50 },
          { name: "Organic Vitamin C", percentage: 20.0, function: "Antioxidant support", cost_per_kg: 25.00 },
          { name: "Natural Sweetener", percentage: 15.0, function: "Taste optimization", cost_per_kg: 18.75 },
          { name: "Mineral Complex", percentage: 12.0, function: "Nutritional enhancement", cost_per_kg: 22.00 },
          { name: "Natural Preservative", percentage: 8.0, function: "Shelf stability", cost_per_kg: 35.00 }
        ],
        nutraceuticals: [
          { name: "Active Botanical Extract", percentage: 40.0, function: "Primary therapeutic compound", cost_per_kg: 45.00 },
          { name: "Bioavailability Enhancer", percentage: 20.0, function: "Absorption optimization", cost_per_kg: 38.50 },
          { name: "Vitamin Complex", percentage: 15.0, function: "Synergistic nutrients", cost_per_kg: 28.75 },
          { name: "Mineral Chelates", percentage: 12.0, function: "Enhanced mineral absorption", cost_per_kg: 52.00 },
          { name: "Natural Coating Agent", percentage: 8.0, function: "Stability and delivery", cost_per_kg: 15.25 }
        ],
        cosmetics: [
          { name: "Hyaluronic Acid Complex", percentage: 30.0, function: "Deep hydration", cost_per_kg: 85.00 },
          { name: "Plant-Based Active", percentage: 25.0, function: "Skin rejuvenation", cost_per_kg: 65.00 },
          { name: "Natural Emulsifier", percentage: 15.0, function: "Texture and stability", cost_per_kg: 28.50 },
          { name: "Botanical Oil Blend", percentage: 12.0, function: "Nourishment and protection", cost_per_kg: 42.00 },
          { name: "Natural Preservative System", percentage: 8.0, function: "Product safety", cost_per_kg: 55.00 }
        ]
      };

      let ingredients = baseIngredients[industry] || baseIngredients.beverages;

      // Adjust ingredients based on compliance requirements
      if (goals?.compliance?.veganFriendly) {
        ingredients = ingredients.map(ing => ({
          ...ing,
          name: ing.name.replace('Complex', 'Plant Complex'),
          function: ing.function + ' (vegan-certified)'
        }));
      }

      if (goals?.compliance?.organicCertified) {
        ingredients = ingredients.map(ing => ({
          ...ing,
          name: 'Organic ' + ing.name,
          cost_per_kg: ing.cost_per_kg * 1.2
        }));
      }

      return ingredients.map(ing => ({
        ...ing,
        supplier_suggestions: ["Premium Naturals Co", "BioSource Industries", "Global Ingredients Ltd"],
        sourcing_region: "USA"
      }));
    };

    const formatShelfLife = (days) => {
      if (days >= 365) return `${Math.round(days / 365 * 10) / 10} years`;
      if (days >= 30) return `${Math.round(days / 30)} months`;
      return `${days} days`;
    };

    const budgetPerUnit = goals?.budgetPerUnit || 2000;
    const ingredients = generateIngredients(industry, goals);

    const baseRecipe = {
      id: "recipe_1",
      name: `Premium ${productType.replace(/[^a-zA-Z0-9]/g, ' ')} Formula`,
      variation: "standard",
      description: productDescription || `Scientifically-optimized ${industry} formulation with superior bioavailability`,
      targetMarket: "Health-conscious professionals and wellness enthusiasts",
      ingredients: ingredients,
      nutritional_profile: {
        macronutrients: {
          protein_g: industry === 'beverages' ? 8.5 : industry === 'nutraceuticals' ? 12.0 : 2.5,
          carbohydrates_g: industry === 'beverages' ? 25.2 : industry === 'nutraceuticals' ? 5.8 : 0.8,
          fat_g: industry === 'beverages' ? 1.2 : industry === 'nutraceuticals' ? 3.5 : 8.2,
          fiber_g: industry === 'beverages' ? 4.1 : industry === 'nutraceuticals' ? 8.5 : 0.3,
          calories_per_serving: industry === 'beverages' ? 145 : industry === 'nutraceuticals' ? 95 : 45
        },
        micronutrients: {
          vitamin_c_mg: industry === 'beverages' ? 120 : industry === 'nutraceuticals' ? 250 : 85,
          vitamin_d_iu: industry === 'nutraceuticals' ? 1000 : 400,
          calcium_mg: industry === 'beverages' ? 150 : industry === 'nutraceuticals' ? 500 : 75,
          iron_mg: industry === 'nutraceuticals' ? 18 : 5.5,
          magnesium_mg: industry === 'beverages' ? 45 : industry === 'nutraceuticals' ? 200 : 25
        },
        bioactive_compounds: {
          antioxidants_orac: industry === 'beverages' ? 8500 : industry === 'nutraceuticals' ? 12000 : 6500,
          polyphenols_mg: industry === 'beverages' ? 150 : industry === 'cosmetics' ? 85 : 250,
          omega3_mg: industry === 'nutraceuticals' ? 500 : 125
        },
        daily_value_percentages: {
          vitamin_c: industry === 'beverages' ? 133 : industry === 'nutraceuticals' ? 278 : 94,
          vitamin_d: industry === 'nutraceuticals' ? 250 : 100,
          calcium: industry === 'nutraceuticals' ? 50 : 15,
          iron: industry === 'nutraceuticals' ? 100 : 31
        }
      },
        production_specs: {
          batch_size: "1000L",
          shelf_life: formatShelfLife(goals?.shelfLifeDays || 365),
          storage_conditions: "Cool, dry place away from direct sunlight",
          manufacturing_process: "Advanced processing with quality control at each stage"
        },
        compliance_details: {
          regulatory_status: {
            fda: "GRAS approved",
            fssai: "Approved for food use",
            eu: "Novel food approved", 
            organic: "USDA Organic certified"
          },
          certifications_needed: [
            ...(goals?.compliance?.veganFriendly ? ["Vegan Certified"] : []),
            ...(goals?.compliance?.organicCertified ? ["Organic Certified"] : []),
            ...(goals?.compliance?.lowCalorie ? ["Low Calorie Verified"] : [])
          ],
          labeling_requirements: "Standard formulation labeling with applicable certifications"
        },
        cost_analysis: {
          raw_material_cost: Math.round(budgetPerUnit * 0.4 * 100) / 100,
          estimated_cogs: Math.round(budgetPerUnit * 0.6 * 100) / 100,
          suggested_retail: Math.round(budgetPerUnit * 2.5 * 100) / 100,
          margin_percentage: 76
        },
        scores: {
          nutrition: Math.min(100, goals?.nutritionScore || 80),
          sustainability: Math.min(100, goals?.sustainability || 70),
          cost_efficiency: Math.min(100, Math.round(85 - (budgetPerUnit / 1000 * 15))),
          compliance: 95,
          market_appeal: 88,
          innovation_index: 79,
          scalability: 91
        },
        sustainability_metrics: {
          carbon_footprint_kg: 2.8,
          water_usage_liters: 150,
          renewable_energy_percentage: 65,
          sustainable_sourcing_percentage: goals?.sustainability || 70,
          packaging_recyclability: 85,
          biodegradability_score: 72
        }
      };

    // Generate 4 additional recipe variations
    const variations = [
      {
        id: "recipe_2",
        name: "Premium Enhanced Formula",
        recipe_type: "premium",
        target_geography: "urban_premium",
        key_differences: "Enhanced bioactives with superior absorption technology and premium ingredients",
        ingredients: ingredients.map(ing => ({
          ...ing,
          name: `Premium ${ing.name}`,
          cost_per_kg: ing.cost_per_kg * 1.4,
          supplier_suggestions: ["Premium Naturals Co", "Elite BioSource", "Platinum Ingredients Ltd"],
          sourcing_region: "European Union"
        })),
        cost_analysis: {
          raw_material_cost: Math.round(budgetPerUnit * 0.6),
          processing_cost: Math.round(budgetPerUnit * 0.15),
          packaging_cost: Math.round(budgetPerUnit * 0.08),
          total_cogs: Math.round(budgetPerUnit * 0.83),
          suggested_retail: Math.round(budgetPerUnit * 3.2),
          margin_percentage: 74
        },
        scores: {
          nutrition: 95,
          sustainability: 85,
          cost_efficiency: 65,
          compliance: 96,
          market_appeal: 92,
          innovation_index: 88,
          scalability: 78
        }
      },
      {
        id: "recipe_3",
        name: "Value-Optimized Formula",
        recipe_type: "budget",
        target_geography: "mass_market",
        key_differences: "Cost-optimized formulation while maintaining efficacy and quality standards",
        ingredients: ingredients.map(ing => ({
          ...ing,
          cost_per_kg: ing.cost_per_kg * 0.7,
          supplier_suggestions: ["Value Ingredients Co", "EconoSource Ltd", "Budget Naturals Inc"],
          sourcing_region: "India"
        })),
        cost_analysis: {
          raw_material_cost: Math.round(budgetPerUnit * 0.35),
          processing_cost: Math.round(budgetPerUnit * 0.12),
          packaging_cost: Math.round(budgetPerUnit * 0.05),
          total_cogs: Math.round(budgetPerUnit * 0.52),
          suggested_retail: Math.round(budgetPerUnit * 1.8),
          margin_percentage: 71
        },
        scores: {
          nutrition: 75,
          sustainability: 70,
          cost_efficiency: 92,
          compliance: 94,
          market_appeal: 78,
          innovation_index: 65,
          scalability: 95
        }
      },
      {
        id: "recipe_4",
        name: "Ayurvedic Heritage Formula",
        recipe_type: "regional",
        target_geography: "india_traditional",
        key_differences: "Incorporates traditional Indian herbs and Ayurvedic principles for holistic wellness",
        ingredients: [
          ...ingredients.slice(0, 3),
          { name: "Ashwagandha Extract", percentage: 15.0, function: "Adaptogenic stress support", cost_per_kg: 2850, supplier_suggestions: ["Himalayan Herbs Ltd", "Ayurveda Organics", "Traditional Botanicals Co"], sourcing_region: "Rajasthan, India" },
          { name: "Turmeric (95% Curcumin)", percentage: 10.0, function: "Anti-inflammatory support", cost_per_kg: 3200, supplier_suggestions: ["Kerala Spice Co", "Golden Root Extracts", "Curcumin Specialists Ltd"], sourcing_region: "Kerala, India" },
          { name: "Triphala Extract", percentage: 8.0, function: "Digestive wellness", cost_per_kg: 1850, supplier_suggestions: ["Ayurvedic Extracts Ltd", "Traditional Formulations Co", "Herbal Heritage Inc"], sourcing_region: "Uttarakhand, India" }
        ],
        traditional_benefits: "Based on 5000-year-old Ayurvedic principles for balanced wellness and vitality",
        cost_analysis: {
          raw_material_cost: Math.round(budgetPerUnit * 0.42),
          processing_cost: Math.round(budgetPerUnit * 0.13),
          packaging_cost: Math.round(budgetPerUnit * 0.06),
          total_cogs: Math.round(budgetPerUnit * 0.61),
          suggested_retail: Math.round(budgetPerUnit * 2.4),
          margin_percentage: 75
        },
        scores: {
          nutrition: 82,
          sustainability: 88,
          cost_efficiency: 78,
          compliance: 92,
          market_appeal: 85,
          innovation_index: 72,
          scalability: 88
        }
      },
      {
        id: "recipe_5",
        name: "Innovation-Forward Formula",
        recipe_type: "alternative",
        target_geography: "tech_early_adopters",
        key_differences: "Cutting-edge ingredients with novel delivery systems and bioenhancement technology",
        ingredients: ingredients.map(ing => ({
          ...ing,
          name: `Nano-Enhanced ${ing.name}`,
          function: `${ing.function} with liposomal delivery`,
          cost_per_kg: ing.cost_per_kg * 2.2,
          supplier_suggestions: ["NanoTech Ingredients", "Advanced Delivery Systems", "Future Formulations Ltd"],
          sourcing_region: "Switzerland"
        })),
        innovation_highlights: "Liposomal encapsulation, nano-particle delivery, and patented bioenhancer technology",
        cost_analysis: {
          raw_material_cost: Math.round(budgetPerUnit * 0.85),
          processing_cost: Math.round(budgetPerUnit * 0.25),
          packaging_cost: Math.round(budgetPerUnit * 0.12),
          total_cogs: Math.round(budgetPerUnit * 1.22),
          suggested_retail: Math.round(budgetPerUnit * 4.5),
          margin_percentage: 73
        },
        scores: {
          nutrition: 92,
          sustainability: 75,
          cost_efficiency: 58,
          compliance: 89,
          market_appeal: 88,
          innovation_index: 98,
          scalability: 65
        }
      }
    ];

    const allRecipes = [baseRecipe, ...alternatives];

    return {
      recipes: allRecipes,
      allRecipes: allRecipes,
      sustainability_report: {
        carbon_footprint: `${goals?.sustainability >= 80 ? 'Very Low' : goals?.sustainability >= 60 ? 'Low' : 'Moderate'} - optimized sourcing strategy`,
        sustainable_sourcing: `${goals?.sustainability || 70}% sustainably sourced ingredients`,
        packaging_recommendations: "Eco-friendly packaging aligned with sustainability goals",
        certifications: [
          ...(goals?.compliance?.organicCertified ? ["Organic"] : []),
          "Sustainable Sourcing",
          "Carbon Footprint Verified"
        ]
      },
      market_positioning: {
        unique_selling_points: [
          "AI-optimized formulation",
          ...(goals?.compliance?.veganFriendly ? ["100% Vegan"] : []),
          ...(goals?.compliance?.organicCertified ? ["Certified Organic"] : []),
          ...(goals?.compliance?.lowCalorie ? ["Low Calorie"] : []),
          "Science-backed efficacy"
        ],
        target_consumer: "Health-conscious professionals seeking premium formulations",
        price_positioning: budgetPerUnit > 100 ? "Premium" : budgetPerUnit > 50 ? "Mid-tier" : "Value",
        marketing_claims: [
          "Clinically-inspired formulation",
          `${goals?.nutritionScore || 80}% nutrition score target`,
          `${formatShelfLife(goals?.shelfLifeDays || 365)} shelf stability`
        ]
      }
    };
  }
}

module.exports = new ClaudeService();
