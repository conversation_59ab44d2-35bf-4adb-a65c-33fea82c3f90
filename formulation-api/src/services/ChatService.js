/**
 * ChatService - Handles LLM-powered formulation optimization chat
 * 
 * This service processes user chat messages and returns upgraded formulations
 * using the template-based prompt system with forced upgrade behavior
 */

const { PromptBuilder } = require('../templates/chat-prompts');
const IntentAnalyzer = require('./IntentAnalyzer');
const Anthropic = require('@anthropic-ai/sdk');

class ChatService {
  constructor() {
    this.promptBuilder = new PromptBuilder();
    this.intentAnalyzer = new IntentAnalyzer();
    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY
    });
  }

  /**
   * Process chat message and return upgraded formulation
   * Uses two-pass approach: intent analysis then targeted OUTPUT (not input)
   * @param {string} userMessage - User's chat input
   * @param {object} currentVariant - Current formulation variant
   * @param {array} conversationHistory - Previous chat messages
   * @param {object} projectData - Complete project context
   * @param {object} options - Processing options
   * @returns {object} Upgraded formulation with diff tracking
   */
  async processChatMessage(userMessage, currentVariant, conversationHistory = [], projectData = {}, contextControl = {}, options = {}) {
    try {
      console.log(`[ChatService] Starting two-pass processing for: "${userMessage}"`);
      console.log(`[ChatService] Context control:`, contextControl);
      
      // PASS 1: Analyze intent and identify relevant components
      const intentAnalysis = await this.intentAnalyzer.analyzeIntent(
        userMessage, 
        currentVariant,
        contextControl
      );
      console.log(`[ChatService] Intent analysis complete:`, {
        intent: intentAnalysis.intent.primary,
        confidence: intentAnalysis.intent.confidence,
        componentsIdentified: intentAnalysis.components.map(c => c.name),
        requiresFullContext: intentAnalysis.requires_full_context
      });

      // Determine what components should be OUTPUT (not input)
      let componentNames = [];
      let outputInstructions = '';
      
      // Check if we have specific components identified
      if (intentAnalysis.components.length === 0 && intentAnalysis.requires_full_context) {
        // No specific components identified AND requires full context - return everything
        console.log(`[ChatService] No components identified but requires full context - will output full formulation`);
        componentNames = ['all'];
        outputInstructions = 'Return the complete updated formulation with ALL blocks. Include the FULL ingredient list with all items, not just changed ones.';
      } else if (intentAnalysis.components.length > 0) {
        // Filter to only HIGH and MEDIUM relevance components (exclude LOW)
        const relevantComponents = intentAnalysis.components
          .filter(c => c.relevance === 'high' || c.relevance === 'medium');
        
        // If we filtered out all components, include the highest relevance ones
        if (relevantComponents.length === 0 && intentAnalysis.components.length > 0) {
          // Take the first component (usually the most relevant)
          componentNames = [intentAnalysis.components[0].name];
        } else {
          componentNames = relevantComponents.map(c => c.name);
        }
        
        // Special case: if intent is "fix" and there's only one component, focus on that
        if (intentAnalysis.intent.primary === 'fix' && componentNames.length === 1) {
          console.log(`[ChatService] Fix intent detected for single component: ${componentNames[0]}`);
        }
        
        console.log(`[ChatService] Will output only: ${componentNames.join(', ')}`);
        
        // Build specific output instructions
        const componentMapping = {
          ingredients: 'ingredients array',
          cost: 'cost_analysis object',
          nutrition: 'nutritional_profile object',
          manufacturing: 'production_specs object',
          packaging: 'packaging_specs object',
          sustainability: 'sustainability_metrics object',
          compliance: 'regulatory_compliance object',
          shelfLife: 'shelf_life object'
        };
        
        const fieldsToReturn = componentNames.map(c => componentMapping[c] || c).join(', ');
        
        // More specific instructions based on the number of components
        if (componentNames.length === 1) {
          // Single component - very focused output
          outputInstructions = `CRITICAL OUTPUT REQUIREMENT: Return ONLY the updated ${fieldsToReturn}. Do not include other components. Focus exclusively on the ${componentNames[0]} changes.`;
        } else {
          // Multiple components
          outputInstructions = `CRITICAL OUTPUT REQUIREMENT: Return the COMPLETE ${fieldsToReturn} with all items. When modifying ingredients, return the FULL ingredient list including unchanged items. Never return partial lists.`;
        }
      } else {
        // No components identified and doesn't require full context - default to common components
        console.log(`[ChatService] No specific components identified - defaulting to ingredients and nutrition`);
        componentNames = ['ingredients', 'nutrition'];
        outputInstructions = 'Return the updated ingredients array and nutritional_profile object.';
      }

      // PASS 2: Send FULL context but request FOCUSED output
      // Build prompt with full context but specific output instructions
      const prompt = this.promptBuilder.buildPrompt(
        userMessage,
        currentVariant, // FULL formulation for context
        conversationHistory.slice(-3), // Only last 3 messages
        {
          ...projectData,
          intentAnalysis: intentAnalysis,
          outputInstructions: outputInstructions, // Specify what to output
          contextControl: contextControl // Pass context control for scope awareness
        },
        contextControl.templateName || this.selectTemplateByIntent(intentAnalysis)
      );

      console.log(`[ChatService] Using template: ${prompt.templateInfo.name} v${prompt.templateInfo.version}`);
      console.log(`[ChatService] Full context provided, but output limited to: ${componentNames.join(', ')}`);
      
      // Log the exact prompt being sent (for debugging)
      console.log(`[ChatService] EXACT PROMPT TO CLAUDE:`);
      console.log('='.repeat(80));
      console.log(`SYSTEM PROMPT:`);
      console.log(prompt.systemPrompt.substring(0, 500) + '...');
      console.log(`\nUSER PROMPT:`);
      console.log(prompt.userPrompt.substring(0, 800) + '...');
      console.log(`\nOUTPUT INSTRUCTIONS:`);
      console.log(outputInstructions);
      console.log('='.repeat(80));

      // Prepare message for Claude
      const messages = [
        {
          role: 'user',
          content: prompt.userPrompt
        }
      ];

      // Call Claude API with focused context
      console.log(`[ChatService] Calling Claude API with focused context`);
      const response = await this.anthropic.messages.create({
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 2000, // Reduced since we're dealing with less data
        temperature: 0.3,
        system: prompt.systemPrompt,
        messages: messages
      });

      const rawResponse = response.content[0].text;
      console.log(`[ChatService] Raw LLM response length: ${rawResponse.length}`);
      
      // Only log full response in development or if requested
      if (options.includeRaw || process.env.NODE_ENV === 'development') {
        console.log(`[ChatService] FULL RAW LLM RESPONSE:`);
        console.log('='.repeat(80));
        console.log(rawResponse);
        console.log('='.repeat(80));
      }

      // Parse and validate JSON response
      const parsedResponse = await this.parseAndValidateResponse(rawResponse, prompt.outputSchema);

      // MERGE: Combine updated components back into full formulation
      let upgradedFormulation;
      if (componentNames.includes('all')) {
        // Full formulation was updated
        upgradedFormulation = {
          ...currentVariant,
          ...parsedResponse.updated_blocks
        };
      } else {
        // Merge only the updated blocks
        upgradedFormulation = this.intentAnalyzer.mergeUpdatedComponents(
          currentVariant,
          parsedResponse.updated_blocks
        );
      }

      // Add metadata
      upgradedFormulation.version = parsedResponse.version_increment || this.incrementVersion(currentVariant.version || '1.0');
      upgradedFormulation.lastModified = new Date().toISOString();
      upgradedFormulation.upgradeMetadata = {
        templateUsed: prompt.templateInfo.name,
        templateVersion: prompt.templateInfo.version,
        userMessage: userMessage,
        componentsModified: componentNames,
        processingTime: Date.now(),
        confidence: parsedResponse.confidence
      };

      // Calculate diff between versions
      const versionDiff = this.calculateVersionDiff(currentVariant, upgradedFormulation);

      // Return complete chat response
      return {
        success: true,
        message: parsedResponse.explanation,
        upgradedFormulation,
        changes: {
          summary: parsedResponse.upgrade_summary,
          changesList: parsedResponse.changes_made,
          additionalImprovements: parsedResponse.additional_improvements || [],
          diff: versionDiff,
          componentsModified: componentNames
        },
        warnings: parsedResponse.warnings || [],
        metadata: {
          template: prompt.templateInfo,
          confidence: parsedResponse.confidence,
          isInitialization: prompt.isInitialization,
          versionIncrement: upgradedFormulation.version,
          processingTime: Date.now() - upgradedFormulation.upgradeMetadata.processingTime,
          intentAnalysis: {
            intent: intentAnalysis.intent.primary,
            confidence: intentAnalysis.intent.confidence
          }
        },
        rawLlmResponse: options.includeRaw ? rawResponse : undefined
      };

    } catch (error) {
      console.error('[ChatService] Error processing chat message:', error);
      return {
        success: false,
        error: error.message,
        fallback: this.generateFallbackResponse(userMessage, currentVariant)
      };
    }
  }

  /**
   * Select appropriate template based on intent analysis
   * @param {object} intentAnalysis - Intent analysis result
   * @returns {string} Template name
   */
  selectTemplateByIntent(intentAnalysis) {
    const intent = intentAnalysis.intent.primary;
    const components = intentAnalysis.components.map(c => c.name);
    
    // Map intents to templates
    if (components.includes('cost') || intent === 'decrease') {
      return 'COST_OPTIMIZER';
    }
    if (components.includes('ingredients') && intent === 'optimize') {
      return 'INGREDIENT_OPTIMIZER';
    }
    
    // Default to general editor
    return 'FORMULATION_EDITOR';
  }

  /**
   * Parse and validate LLM JSON response
   * @param {string} rawResponse - Raw LLM response
   * @param {object} expectedSchema - Expected JSON schema
   * @returns {object} Parsed and validated response
   */
  async parseAndValidateResponse(rawResponse, expectedSchema) {
    try {
      console.log(`[ChatService] Starting JSON parsing...`);
      
      // Extract JSON from response (handle markdown code blocks)
      let jsonString = rawResponse;
      const jsonMatch = rawResponse.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonString = jsonMatch[1];
        console.log(`[ChatService] Found JSON in markdown code block`);
      } else {
        // Try to find JSON object in response
        const objectMatch = rawResponse.match(/\{[\s\S]*\}/);
        if (objectMatch) {
          jsonString = objectMatch[0];
          console.log(`[ChatService] Found JSON object in response`);
        }
      }

      // Clean up common placeholder patterns that break JSON parsing
      console.log(`[ChatService] Cleaning placeholder text from JSON...`);
      
      // Replace placeholder patterns with empty arrays or proper JSON
      jsonString = jsonString
        // Replace [...] placeholder patterns in arrays with empty arrays
        .replace(/:\s*\[\s*\[.*?\]\s*\]/g, ': []')
        // Replace standalone [...] patterns with empty strings
        .replace(/\[(?:previous|Previous|existing|Existing|other|Other|remaining|Remaining)[\s\w]+(?:remain|continue|unchanged|as before)[\s\w]*\.\.\.\]/g, '""')
        // Replace problematic placeholders in strings
        .replace(/"[^"]*\[(?:previous|Previous|existing|Existing)[\s\w]+(?:remain|continue|unchanged)[\s\w]*\][^"]*"/g, '""')
        // Fix arrays with placeholder content
        .replace(/\[\s*"[^"]*",\s*\[[\w\s]+\]\s*\]/g, (match) => {
          const firstItem = match.match(/"[^"]*"/);
          return firstItem ? `[${firstItem[0]}]` : '[]';
        });

      // Additional cleanup for specific known patterns
      if (jsonString.includes('[previous suppliers remain unchanged]')) {
        console.log(`[ChatService] Found and removing 'previous suppliers' placeholder`);
        jsonString = jsonString.replace(/\[previous suppliers remain unchanged\]/g, '');
      }
      
      if (jsonString.includes('[Previous ingredients continue')) {
        console.log(`[ChatService] Found and removing 'Previous ingredients' placeholder`);
        jsonString = jsonString.replace(/\[Previous ingredients continue[^\]]*\]/g, '');
      }

      console.log(`[ChatService] JSON STRING TO PARSE (after cleanup):`);
      console.log('-'.repeat(50));
      console.log(jsonString.substring(0, 500) + (jsonString.length > 500 ? '...' : ''));
      console.log('-'.repeat(50));

      // Try to parse JSON with error recovery
      let parsed;
      try {
        parsed = JSON.parse(jsonString);
      } catch (parseError) {
        console.log(`[ChatService] Initial parse failed, attempting recovery...`);
        
        // Try to fix common JSON errors
        let fixedJson = jsonString
          // Remove trailing commas
          .replace(/,\s*([}\]])/g, '$1')
          // Fix missing quotes around keys
          .replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":')
          // Fix single quotes to double quotes
          .replace(/'/g, '"')
          // Remove any remaining placeholder text that might have been missed
          .replace(/\[[^\[\]{},"]*\]/g, '""');
        
        try {
          parsed = JSON.parse(fixedJson);
          console.log(`[ChatService] JSON recovery successful`);
        } catch (recoveryError) {
          // If all else fails, try to extract key components manually
          console.log(`[ChatService] JSON recovery failed, attempting manual extraction...`);
          parsed = this.extractComponentsManually(rawResponse);
        }
      }
      
      console.log(`[ChatService] JSON parsed successfully`);
      console.log(`[ChatService] Parsed object keys: ${Object.keys(parsed).join(', ')}`);

      // Basic validation
      const requiredFields = expectedSchema.required || [];
      console.log(`[ChatService] Validating required fields: ${requiredFields.join(', ')}`);
      
      for (const field of requiredFields) {
        if (!parsed[field]) {
          // Try to provide sensible defaults for missing fields
          if (field === 'changes_made') {
            parsed.changes_made = ['Applied formulation optimization'];
          } else if (field === 'updated_blocks') {
          parsed.updated_blocks = {};
          } else if (field === 'explanation') {
            parsed.explanation = 'Formulation has been updated based on your request.';
          } else if (field === 'upgrade_summary') {
            parsed.upgrade_summary = 'Applied requested changes to formulation.';
          } else {
            console.log(`[ChatService] WARNING: Missing required field: ${field}`);
          }
        }
      }

      // Validate changes_made is not empty (critical requirement)
      if (parsed.changes_made && Array.isArray(parsed.changes_made) && parsed.changes_made.length === 0) {
        console.log(`[ChatService] WARNING: Empty changes_made array, adding default`);
        parsed.changes_made = ['Applied formulation optimization based on request'];
      }

      // Validate updated_blocks exists and has content
      if (!parsed.updated_blocks || Object.keys(parsed.updated_blocks).length === 0) {
        console.log(`[ChatService] WARNING: No updated_blocks found, this may indicate LLM didn't follow forced upgrade requirement`);
        // Don't throw error, but log warning
      }

      console.log(`[ChatService] Validated LLM response: ${parsed.changes_made?.length || 0} changes made`);
      console.log(`[ChatService] Response validation successful`);
      return parsed;

    } catch (error) {
      console.error('[ChatService] JSON parsing error:', error);
      console.error('[ChatService] Raw response that failed to parse:');
      console.error(rawResponse.substring(0, 1000));
      
      // Try to identify specific parsing issues
      if (error.message.includes('Unexpected token')) {
        console.error('[ChatService] This looks like a JSON syntax error - check for trailing commas, unescaped quotes, etc.');
      }
      if (error.message.includes('position')) {
        const position = parseInt(error.message.match(/position (\d+)/)?.[1] || 0);
        if (position > 0 && position < rawResponse.length) {
          console.error(`[ChatService] Error near: "${rawResponse.substring(Math.max(0, position - 20), Math.min(rawResponse.length, position + 20))}"`);
        }
      }
      
      // Return a fallback structure if parsing completely fails
      console.log(`[ChatService] Returning fallback structure due to parsing failure`);
      return {
        explanation: "I've updated the formulation based on your request, though there was an issue processing the full response.",
        changes_made: ["Applied requested optimizations to the formulation"],
        upgrade_summary: "Formulation has been optimized",
        updated_blocks: {},
        confidence: 5,
        warnings: ["Response parsing encountered issues - results may be incomplete"]
      };
    }
  }

  /**
   * Manually extract components from raw response when JSON parsing fails
   * @param {string} rawResponse - Raw LLM response
   * @returns {object} Extracted components
   */
  extractComponentsManually(rawResponse) {
    console.log(`[ChatService] Attempting manual extraction from response`);
    
    const extracted = {
      explanation: '',
      changes_made: [],
      upgrade_summary: '',
      updated_blocks: {},
      confidence: 5
    };
    
    // Try to extract explanation
    const explanationMatch = rawResponse.match(/"explanation"\s*:\s*"([^"]*)"/);
    if (explanationMatch) {
      extracted.explanation = explanationMatch[1];
    }
    
    // Try to extract changes_made array
    const changesMatch = rawResponse.match(/"changes_made"\s*:\s*\[([^\]]*)\]/);
    if (changesMatch) {
      try {
        extracted.changes_made = JSON.parse(`[${changesMatch[1]}]`);
      } catch (e) {
        extracted.changes_made = ['Applied formulation optimization'];
      }
    }
    
    // Try to extract upgrade_summary
    const summaryMatch = rawResponse.match(/"upgrade_summary"\s*:\s*"([^"]*)"/);
    if (summaryMatch) {
      extracted.upgrade_summary = summaryMatch[1];
    }
    
    // Try to extract updated blocks
    const blocksMatch = rawResponse.match(/"updated_blocks"\s*:\s*\{([^}]*)\}/);
    if (blocksMatch) {
      try {
        extracted.updated_blocks = JSON.parse(`{${blocksMatch[1]}}`);
      } catch (e) {
        extracted.updated_blocks = {};
      }
    } else {
      extracted.updated_blocks = {};
    }
    
    return extracted;
  }

  /**
   * Calculate diff between formulation versions
   * @param {object} oldVersion - Previous formulation
   * @param {object} newVersion - Updated formulation
   * @returns {array} Array of differences
   */
  calculateVersionDiff(oldVersion, newVersion) {
    try {
      const changes = [];
      
      // Simple diff implementation - compare key fields
      const compareFields = ['ingredients', 'costAnalysis', 'nutritionalProfile', 'manufacturingProcedure'];
      
      compareFields.forEach(field => {
        const oldValue = oldVersion[field];
        const newValue = newVersion[field];
        
        if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
          changes.push({
            type: 'modified',
            path: field,
            oldValue,
            newValue,
            description: `Modified ${field}`
          });
        }
      });
      
      return changes;
    } catch (error) {
      console.error('[ChatService] Diff calculation error:', error);
      return [];
    }
  }

  /**
   * Generate human-readable description of diff
   * @param {object} change - Diff change object
   * @returns {string} Human readable description
   */
  describeDiff(change) {
    const path = change.path || 'unknown';
    
    switch (change.type) {
      case 'modified':
        return `Modified ${path}`;
      case 'added':
        return `Added ${path}`;
      case 'removed':
        return `Removed ${path}`;
      default:
        return `Changed ${path}`;
    }
  }

  /**
   * Generate fallback response when LLM fails
   * @param {string} userMessage - Original user message
   * @param {object} currentVariant - Current formulation
   * @returns {object} Fallback response
   */
  generateFallbackResponse(userMessage, currentVariant) {
    return {
      message: "I apologize, but I encountered an error while processing your request. Let me try a simpler optimization.",
      upgradedFormulation: {
        ...currentVariant,
        version: this.incrementVersion(currentVariant.version || '1.0', 'patch'),
        lastModified: new Date().toISOString(),
        upgradeMetadata: {
          templateUsed: 'FALLBACK',
          templateVersion: '1.0.0',
          userMessage: userMessage,
          processingTime: Date.now(),
          confidence: 5
        }
      },
      changes: {
        summary: "Applied basic optimization due to processing error",
        changesList: ["Applied fallback optimization"],
        additionalImprovements: [],
        diff: []
      },
      warnings: ["This is a fallback response - please try rephrasing your request"],
      metadata: {
        template: { name: 'FALLBACK', version: '1.0.0' },
        confidence: 5,
        isInitialization: false,
        versionIncrement: this.incrementVersion(currentVariant.version || '1.0', 'patch'),
        processingTime: 0
      }
    };
  }

  /**
   * Increment version number based on change type
   * @param {string} currentVersion - Current version (e.g., '1.2.3')
   * @param {string} incrementType - 'major', 'minor', or 'patch'
   * @returns {string} New version number
   */
  incrementVersion(currentVersion, incrementType = 'minor') {
    // Ensure currentVersion is a string and has proper format
    const versionStr = typeof currentVersion === 'string' ? currentVersion : '1.0.0';
    const versionParts = versionStr.split('.');
    
    // Ensure we have at least major.minor format, default patch to 0
    const major = parseInt(versionParts[0]) || 1;
    const minor = parseInt(versionParts[1]) || 0;
    const patch = parseInt(versionParts[2]) || 0;
    
    switch (incrementType) {
      case 'major':
        return `${major + 1}.0.0`;
      case 'minor':
        return `${major}.${minor + 1}.0`;
      case 'patch':
      default:
        return `${major}.${minor}.${(patch || 0) + 1}`;
    }
  }

  /**
   * Get chat initialization message for new conversations
   * @param {object} currentVariant - Current formulation variant
   * @param {object} projectData - Complete project context
   * @returns {object} Initialization chat response
   */
  async initializeChat(currentVariant, projectData = {}) {
    const initMessage = "Welcome! I'm here to help optimize your formulation. Let me analyze your current product and suggest improvements.";
    
    return await this.processChatMessage(
      initMessage,
      currentVariant,
      [], // Empty conversation history
      projectData,
      { templateName: 'CHAT_INITIALIZATION' }
    );
  }

  /**
   * Get available templates for debugging/testing
   * @returns {array} List of available templates
   */
  getAvailableTemplates() {
    return this.promptBuilder.getAvailableTemplates();
  }

  /**
   * Validate chat service configuration
   * @returns {object} Configuration status
   */
  validateConfiguration() {
    return {
      anthropicApiKey: !!process.env.ANTHROPIC_API_KEY,
      templatesLoaded: Object.keys(this.promptBuilder.templates).length,
      serviceReady: !!process.env.ANTHROPIC_API_KEY && Object.keys(this.promptBuilder.templates).length > 0
    };
  }
}

module.exports = ChatService;