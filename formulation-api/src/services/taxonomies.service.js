/**
 * Taxonomies Service
 * Handles fetching and filtering of taxonomy data from MongoDB
 */

const { validateTaxonomy } = require('../models/schemas');

class TaxonomiesService {
  constructor(db) {
    this.db = db;
    this.collection = db.collection('taxonomies');
  }

  /**
   * List taxonomies by level with optional parent filtering
   * @param {string} level - 'industry', 'category', or 'sub_category'
   * @param {string} parentId - Optional parent ID for filtering
   * @returns {Array} Array of taxonomy objects
   */
  async listByLevel(level, parentId = null, status = null) {
    try {
      console.log(`[TaxonomiesService] Fetching taxonomies - level: ${level}, parent: ${parentId}`);

      const query = { level };
      
      // Add parent filter if provided
      if (parentId) {
        query.parent_id = parentId;
      }
      if (status) {
        query.status = status;
      }
      
      console.log('Query:', query);
      const taxonomies = await this.collection
        .find(query)
        .project({
          _id: 1,
          slug: 1,
          name: 1,
          level: 1,
          parent_id: 1,
          status: 1,
          metadata: 1
        })
        .sort({ order: 1, name: 1 })
        .toArray();

      console.log(`[TaxonomiesService] Found ${taxonomies.length} taxonomies`);
      console.log('Taxonomies from DB:', taxonomies);

      // Validate each taxonomy
      const validatedTaxonomies = [];
      for (const taxonomy of taxonomies) {
        const validation = validateTaxonomy(taxonomy);
        if (validation.valid) {
          validatedTaxonomies.push(taxonomy);
        } else {
          console.warn(`[TaxonomiesService] Invalid taxonomy ${taxonomy.slug}:`, validation.errors);
        }
      }

      return validatedTaxonomies;
    } catch (error) {
      console.error('[TaxonomiesService] Error fetching taxonomies:', error);
      throw error;
    }
  }

  /**
   * Get a single taxonomy by slug
   * @param {string} slug - Taxonomy slug
   * @returns {Object|null} Taxonomy object or null
   */
  async getBySlug(slug) {
    try {
      console.log(`[TaxonomiesService] Fetching taxonomy by slug: ${slug}`);

      const taxonomy = await this.collection.findOne({ slug });

      if (!taxonomy) {
        console.log(`[TaxonomiesService] Taxonomy not found: ${slug}`);
        return null;
      }

      const validation = validateTaxonomy(taxonomy);
      if (!validation.valid) {
        console.warn(`[TaxonomiesService] Invalid taxonomy ${slug}:`, validation.errors);
        return null;
      }

      return taxonomy;
    } catch (error) {
      console.error('[TaxonomiesService] Error fetching taxonomy:', error);
      throw error;
    }
  }

  /**
   * Get taxonomy hierarchy (industry > category > sub_category)
   * @param {string} subCategorySlug - Sub-category slug
   * @returns {Object} Hierarchy object with industry, category, and sub_category
   */
  async getHierarchy(subCategorySlug) {
    try {
      console.log(`[TaxonomiesService] Building hierarchy for: ${subCategorySlug}`);

      const subCategory = await this.getBySlug(subCategorySlug);
      if (!subCategory) {
        throw new Error(`Sub-category not found: ${subCategorySlug}`);
      }

      const category = await this.collection.findOne({ _id: subCategory.parent_id });
      if (!category) {
        throw new Error(`Category not found for sub-category: ${subCategorySlug}`);
      }

      const industry = await this.collection.findOne({ _id: category.parent_id });
      if (!industry) {
        throw new Error(`Industry not found for category: ${category.slug}`);
      }

      return {
        industry: {
          slug: industry.slug,
          name: industry.name
        },
        category: {
          slug: category.slug,
          name: category.name
        },
        sub_category: {
          slug: subCategory.slug,
          name: subCategory.name
        }
      };
    } catch (error) {
      console.error('[TaxonomiesService] Error building hierarchy:', error);
      throw error;
    }
  }

  /**
   * Search taxonomies by name or slug
   * @param {string} query - Search query
   * @param {string} level - Optional level filter
   * @returns {Array} Array of matching taxonomies
   */
  async search(query, level = null) {
    try {
      console.log(`[TaxonomiesService] Searching taxonomies: query="${query}", level="${level}"`);

      const searchQuery = {
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { slug: { $regex: query, $options: 'i' } }
        ]
      };

      if (level) {
        searchQuery.level = level;
      }

      const results = await this.collection
        .find(searchQuery)
        .limit(20)
        .toArray();

      console.log(`[TaxonomiesService] Found ${results.length} search results`);
      return results;
    } catch (error) {
      console.error('[TaxonomiesService] Error searching taxonomies:', error);
      throw error;
    }
  }
}

module.exports = TaxonomiesService;
