/**
 * ChatSessionManager - Manages persistent chat sessions with context caching
 * 
 * This service maintains formulation context in memory to avoid resending
 * large JSON payloads with every message. Uses <PERSON>'s ability to maintain
 * context across messages in a conversation.
 */

const Anthropic = require('@anthropic-ai/sdk');
const crypto = require('crypto');

class ChatSessionManager {
  constructor() {
    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY
    });
    
    // Store active sessions in memory
    // In production, this could be Redis or another cache
    this.sessions = new Map();
    
    // Session timeout (30 minutes)
    this.SESSION_TIMEOUT = 30 * 60 * 1000;
    
    // Cleanup old sessions every 5 minutes
    setInterval(() => this.cleanupSessions(), 5 * 60 * 1000);
  }

  /**
   * Create or get a chat session
   * @param {string} projectId - Project ID for the session
   * @param {string} userId - User ID
   * @param {object} currentFormulation - Current formulation data
   * @param {array} revisionHistory - Optional revision history
   * @returns {object} Session info
   */
  async getOrCreateSession(projectId, userId, currentFormulation, revisionHistory = []) {
    const sessionKey = `${userId}_${projectId}`;
    
    let session = this.sessions.get(sessionKey);
    
    if (!session || this.isSessionExpired(session)) {
      // Create new session with formulation context and revision history
      session = await this.createSession(sessionKey, currentFormulation, revisionHistory);
    } else if (this.hasFormulationChanged(session.formulationHash, currentFormulation)) {
      // Formulation has changed, update context
      session = await this.updateSessionContext(sessionKey, currentFormulation);
      // Also update revision history if provided
      if (revisionHistory && revisionHistory.length > 0) {
        session.revisionHistory = revisionHistory;
        console.log(`[SessionManager] Updated session with ${revisionHistory.length} revisions`);
      }
    } else {
      // Session is valid, just update last access
      session.lastAccess = Date.now();
      // Update revision history if newer revisions available
      if (revisionHistory && revisionHistory.length > session.revisionHistory.length) {
        session.revisionHistory = revisionHistory;
        console.log(`[SessionManager] Added ${revisionHistory.length - session.revisionHistory.length} new revisions to session`);
      }
    }
    
    this.sessions.set(sessionKey, session);
    return session;
  }

  /**
   * Create a new chat session with initial context
   * @param {string} sessionKey - Unique session identifier
   * @param {object} formulation - Current formulation
   * @param {array} revisionHistory - Previous revisions if available
   * @returns {object} New session
   */
  async createSession(sessionKey, formulation, revisionHistory = []) {
    const formulationHash = this.hashFormulation(formulation);
    
    // Build revision history context if available
    let revisionContext = '';
    if (revisionHistory && revisionHistory.length > 0) {
      revisionContext = `
REVISION HISTORY (How we got here):
${revisionHistory.map((rev, index) => `
Revision ${index + 1}: ${rev.timestamp}
User Request: "${rev.userRequest}"
Components Modified: ${rev.componentsModified?.join(', ') || 'unknown'}
Changes: ${rev.changes?.join(', ') || rev.description}
`).join('\n')}

This history shows the evolution of changes. The current formulation below is the result of all these modifications.
`;
    }
    
    // Build initial system context with full formulation and history
    const systemContext = `
You are an expert formulation scientist assistant. I'm providing you with the complete formulation context and its revision history.

ORIGINAL FORMULATION (Starting point - Version 1.0):
${JSON.stringify(formulation, null, 2)}
${revisionContext}
IMPORTANT INSTRUCTIONS:
1. The above shows the original formulation and how it evolved through user requests
2. I will track each change you make to build a complete revision history
3. Always consider the revision history when making new changes
4. When returning updates, only return the modified components unless specifically asked for the full formulation
5. Each change should build upon the previous revisions logically

CRITICAL REQUIREMENT:
You MUST always return an upgraded/modified version based on user input. Never just provide advice - ALWAYS make actual changes to improve the product.

OUTPUT FORMAT:
Return a JSON object with:
- The modified components only (not the full formulation)
- "changes_made": Array describing what was changed
- "explanation": Why these changes were made
- "componentsModified": Array of component names that were changed`;

    // Initialize conversation with context
    const messages = [
      {
        role: 'system',
        content: systemContext
      },
      {
        role: 'assistant',
        content: `I have loaded your formulation context${revisionHistory.length > 0 ? ` along with ${revisionHistory.length} previous revisions` : ''}. I understand the evolution of this product and will make informed modifications based on this history. How can I help optimize your formulation today?`
      }
    ];
    
    return {
      sessionKey,
      created: Date.now(),
      lastAccess: Date.now(),
      formulationHash,
      messages,
      systemContext,
      contextSize: JSON.stringify(formulation).length,
      messageCount: 1,
      revisionHistory: revisionHistory || [],
      baseFormulation: JSON.parse(JSON.stringify(formulation)) // Keep original for reference
    };
  }

  /**
   * Update session with new formulation context
   * @param {string} sessionKey - Session identifier
   * @param {object} formulation - New formulation data
   * @returns {object} Updated session
   */
  async updateSessionContext(sessionKey, formulation) {
    const existingSession = this.sessions.get(sessionKey);
    const formulationHash = this.hashFormulation(formulation);
    
    // Notify about context update
    const updateMessage = {
      role: 'system',
      content: `CONTEXT UPDATE: The formulation has been modified. Here's the updated version to remember:
${JSON.stringify(formulation, null, 2)}

Continue to use this updated context for all future modifications.`
    };
    
    const messages = existingSession ? existingSession.messages : [];
    messages.push(updateMessage);
    
    return {
      ...existingSession,
      lastAccess: Date.now(),
      formulationHash,
      messages,
      contextSize: JSON.stringify(formulation).length,
      contextUpdated: true
    };
  }

  /**
   * Send a message to an existing session
   * @param {string} sessionKey - Session identifier
   * @param {string} userMessage - User's message
   * @param {object} outputInstructions - Instructions for what to output
   * @param {object} changeMetadata - Metadata about changes made (for revision tracking)
   * @returns {object} Response from Claude
   */
  async sendMessage(sessionKey, userMessage, outputInstructions = {}, changeMetadata = null) {
    const session = this.sessions.get(sessionKey);
    
    if (!session) {
      throw new Error('Session not found. Please create a session first.');
    }
    
    // Add user message to conversation
    session.messages.push({
      role: 'user',
      content: userMessage + (outputInstructions.text ? '\n\n' + outputInstructions.text : '')
    });
    
    try {
      // Send to Claude with accumulated context
      console.log(`[SessionManager] Sending message to Claude with ${session.messages.length} messages in context`);
      console.log(`[SessionManager] Revision history contains ${session.revisionHistory.length} revisions`);
      
      const response = await this.anthropic.messages.create({
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 2000,
        temperature: 0.3,
        messages: session.messages
      });
      
      const assistantResponse = response.content[0].text;
      
      // Add assistant response to conversation history
      session.messages.push({
        role: 'assistant',
        content: assistantResponse
      });
      
      // Track revision if changes were made
      if (changeMetadata && changeMetadata.componentsModified && changeMetadata.componentsModified.length > 0) {
        const revision = {
          timestamp: new Date().toISOString(),
          userRequest: userMessage,
          componentsModified: changeMetadata.componentsModified,
          changes: changeMetadata.changes || [],
          description: changeMetadata.description || 'Formulation updated based on user request'
        };
        
        session.revisionHistory.push(revision);
        console.log(`[SessionManager] Added revision ${session.revisionHistory.length} with components: ${revision.componentsModified.join(', ')}`);
      }
      
      // Update session
      session.lastAccess = Date.now();
      session.messageCount++;
      this.sessions.set(sessionKey, session);
      
      return {
        response: assistantResponse,
        sessionInfo: {
          messageCount: session.messageCount,
          contextSize: session.contextSize,
          sessionAge: Date.now() - session.created,
          revisionCount: session.revisionHistory.length
        }
      };
      
    } catch (error) {
      console.error('[SessionManager] Error sending message:', error);
      throw error;
    }
  }

  /**
   * Check if session has expired
   * @param {object} session - Session object
   * @returns {boolean} True if expired
   */
  isSessionExpired(session) {
    return Date.now() - session.lastAccess > this.SESSION_TIMEOUT;
  }

  /**
   * Check if formulation has changed
   * @param {string} oldHash - Previous formulation hash
   * @param {object} newFormulation - New formulation
   * @returns {boolean} True if changed
   */
  hasFormulationChanged(oldHash, newFormulation) {
    return oldHash !== this.hashFormulation(newFormulation);
  }

  /**
   * Create hash of formulation for change detection
   * @param {object} formulation - Formulation object
   * @returns {string} Hash string
   */
  hashFormulation(formulation) {
    const jsonString = JSON.stringify(formulation);
    return crypto.createHash('md5').update(jsonString).digest('hex');
  }

  /**
   * Clean up expired sessions
   */
  cleanupSessions() {
    const now = Date.now();
    for (const [key, session] of this.sessions.entries()) {
      if (now - session.lastAccess > this.SESSION_TIMEOUT) {
        console.log(`[SessionManager] Cleaning up expired session: ${key}`);
        this.sessions.delete(key);
      }
    }
  }

  /**
   * Get session statistics
   * @param {string} sessionKey - Session identifier
   * @returns {object} Session stats
   */
  getSessionStats(sessionKey) {
    const session = this.sessions.get(sessionKey);
    
    if (!session) {
      return null;
    }
    
    return {
      created: new Date(session.created).toISOString(),
      lastAccess: new Date(session.lastAccess).toISOString(),
      messageCount: session.messageCount,
      contextSize: session.contextSize,
      ageMinutes: Math.floor((Date.now() - session.created) / 60000),
      remainingMinutes: Math.floor((this.SESSION_TIMEOUT - (Date.now() - session.lastAccess)) / 60000)
    };
  }

  /**
   * Clear all sessions (for testing or admin purposes)
   */
  clearAllSessions() {
    const count = this.sessions.size;
    this.sessions.clear();
    console.log(`[SessionManager] Cleared ${count} sessions`);
    return count;
  }

  /**
   * Build revision history from conversation history
   * @param {array} conversationHistory - Array of conversation messages
   * @returns {array} Array of revision objects
   */
  buildRevisionHistory(conversationHistory) {
    const revisions = [];
    
    if (!conversationHistory || conversationHistory.length === 0) {
      return revisions;
    }
    
    // Process conversation history to extract revisions
    for (let i = 0; i < conversationHistory.length; i++) {
      const message = conversationHistory[i];
      
      // Look for assistant responses with changes
      if (message.role === 'assistant' && message.metadata) {
        const { componentsModified, changesSummary, timestamp } = message.metadata;
        
        if (componentsModified && componentsModified.length > 0) {
          // Find the corresponding user message
          let userRequest = 'User request';
          if (i > 0 && conversationHistory[i - 1].role === 'user') {
            userRequest = conversationHistory[i - 1].message;
          }
          
          revisions.push({
            timestamp: timestamp || new Date(message.timestamp).toISOString(),
            userRequest: userRequest,
            componentsModified: componentsModified,
            changes: changesSummary || [],
            description: message.message || 'Formulation updated'
          });
        }
      }
    }
    
    console.log(`[SessionManager] Built ${revisions.length} revisions from conversation history`);
    return revisions;
  }

  /**
   * Get revision history for a session
   * @param {string} sessionKey - Session identifier
   * @returns {array} Revision history
   */
  getRevisionHistory(sessionKey) {
    const session = this.sessions.get(sessionKey);
    return session ? session.revisionHistory : [];
  }
}

module.exports = ChatSessionManager;