/**
 * Parameters Service
 * Handles parameter definitions, bindings, and answers from MongoDB
 */

const { validateParameter, validateParamBinding, validateParamAnswer } = require('../models/schemas');

class ParametersService {
  constructor(db) {
    this.db = db;
    this.parametersCollection = db.collection('parameters');
    this.bindingsCollection = db.collection('param_bindings');
    this.answersCollection = db.collection('param_answers');
  }

  /**
   * Get parameter bindings for a specific sub-category
   * Joins bindings with parameter definitions
   * @param {string} level - Target level (usually 'sub_category')
   * @param {string} slug - Target slug (e.g., 'face-serum')
   * @returns {Array} Array of merged parameter+binding objects
   */
  async getBindingsForTarget(level, slug) {
    try {
      console.log(`[ParametersService] Fetching bindings for ${level}:${slug}`);

      // Fetch all bindings for this target
      const bindings = await this.bindingsCollection
        .find({
          'target.level': level,
          'target.slug': slug,
          status: { $ne: 'inactive' }
        })
        .sort({ 'display.order': 1 })
        .toArray();

      console.log(`[ParametersService] Found ${bindings.length} bindings`);

      // Fetch all parameters that are referenced
      const parameterKeys = bindings.map(b => b.parameter_key);
      const parameters = await this.parametersCollection
        .find({ key: { $in: parameterKeys } })
        .toArray();

      // Create a map for quick lookup
      const parameterMap = new Map();
      parameters.forEach(p => {
        const validation = validateParameter(p);
        if (validation.valid) {
          parameterMap.set(p.key, p);
        } else {
          console.warn(`[ParametersService] Invalid parameter ${p.key}:`, validation.errors);
        }
      });

      // Merge bindings with parameters
      const mergedBindings = [];
      for (const binding of bindings) {
        const validation = validateParamBinding(binding);
        if (!validation.valid) {
          console.warn(`[ParametersService] Invalid binding:`, validation.errors);
          continue;
        }

        const parameter = parameterMap.get(binding.parameter_key);
        if (!parameter) {
          console.warn(`[ParametersService] Parameter not found for binding: ${binding.parameter_key}`);
          continue;
        }

        // Merge parameter and binding data
        const merged = {
          parameter_key: binding.parameter_key,
          type: parameter.type,
          name: parameter.name,
          description: parameter.description,
          panel: (binding.panel !== undefined ? binding.panel : parameter.panel),
          answers_key: parameter.answers_key || binding.answers_key,
          widget: binding.widget || {
            type: this.inferWidgetType(parameter.type)
          },
          guidance: binding.guidance || parameter.guidance || {},
          validators: binding.validators || parameter.validators || {},
          display: binding.display || {
            section: 'general',
            order: 999
          },
          required: binding.required !== undefined ? binding.required : parameter.required,
          default_value: binding.default_value || parameter.default_value
        };

        mergedBindings.push(merged);
      }

      console.log(`[ParametersService] Returning ${mergedBindings.length} merged bindings`);
      return mergedBindings;
    } catch (error) {
      console.error('[ParametersService] Error fetching bindings:', error);
      throw error;
    }
  }

  /**
   * Get answer options for enum parameters
   * @param {string} answersKey - The answers key to fetch
   * @returns {Object} Answer set with options
   */
  async getAnswerSet(answersKey) {
    try {
      console.log(`[ParametersService] Fetching answer set: ${answersKey}`);

      const answerSet = await this.answersCollection.findOne({ key: answersKey });

      if (!answerSet) {
        console.log(`[ParametersService] Answer set not found: ${answersKey}`);
        return null;
      }

      const validation = validateParamAnswer(answerSet);
      if (!validation.valid) {
        console.warn(`[ParametersService] Invalid answer set ${answersKey}:`, validation.errors);
        return null;
      }

      return {
        key: answerSet.key,
        name: answerSet.name,
        options: answerSet.options || []
      };
    } catch (error) {
      console.error('[ParametersService] Error fetching answer set:', error);
      throw error;
    }
  }

  /**
   * Get all answer sets for multiple keys
   * @param {Array} answersKeys - Array of answer keys
   * @returns {Object} Map of key to answer set
   */
  async getMultipleAnswerSets(answersKeys) {
    try {
      console.log(`[ParametersService] Fetching ${answersKeys.length} answer sets`);

      const answerSets = await this.answersCollection
        .find({ key: { $in: answersKeys } })
        .toArray();

      const answerMap = {};
      for (const answerSet of answerSets) {
        const validation = validateParamAnswer(answerSet);
        if (validation.valid) {
          answerMap[answerSet.key] = {
            key: answerSet.key,
            name: answerSet.name,
            options: answerSet.options || []
          };
        }
      }

      console.log(`[ParametersService] Returning ${Object.keys(answerMap).length} answer sets`);
      return answerMap;
    } catch (error) {
      console.error('[ParametersService] Error fetching answer sets:', error);
      throw error;
    }
  }

  /**
   * Backward-compatible alias used by routes
   * @param {Array<string>} keys
   * @returns {Object} Map of key -> answer set
   */
  async getAnswerSets(keys) {
    return this.getMultipleAnswerSets(keys);
  }

  /**
   * Get a single parameter definition by key
   * @param {string} key - Parameter key
   * @returns {Object|null} Parameter object or null
   */
  async getParameter(key) {
    try {
      console.log(`[ParametersService] Fetching parameter: ${key}`);

      const parameter = await this.parametersCollection.findOne({ key });

      if (!parameter) {
        console.log(`[ParametersService] Parameter not found: ${key}`);
        return null;
      }

      const validation = validateParameter(parameter);
      if (!validation.valid) {
        console.warn(`[ParametersService] Invalid parameter ${key}:`, validation.errors);
        return null;
      }

      return parameter;
    } catch (error) {
      console.error('[ParametersService] Error fetching parameter:', error);
      throw error;
    }
  }

  /**
   * Infer widget type from parameter type
   * @param {string} paramType - Parameter type
   * @returns {string} Widget type
   */
  inferWidgetType(paramType) {
    switch (paramType) {
      case 'enum':
        return 'select';
      case 'number':
        return 'slider';
      case 'boolean':
        return 'checkbox';
      case 'table':
        return 'table';
      case 'array':
        return 'multiselect';
      default:
        return 'text';
    }
  }

  /**
   * Validate parameter values against their definitions
   * @param {Object} values - Key-value pairs of parameters
   * @param {Array} parameterDefs - Parameter definitions with validators
   * @returns {Object} Validation result with errors
   */
  validateParameterValues(values, parameterDefs) {
    const errors = [];

    for (const def of parameterDefs) {
      const value = values[def.parameter_key];

      // Check required fields
      if (def.required && (value === undefined || value === null || value === '')) {
        errors.push({
          field: def.parameter_key,
          message: `${def.name} is required`
        });
        continue;
      }

      // Skip validation if not required and no value
      if (!def.required && (value === undefined || value === null)) {
        continue;
      }

      // Type validation
      if (def.type === 'number' && typeof value !== 'number') {
        errors.push({
          field: def.parameter_key,
          message: `${def.name} must be a number`
        });
      }

      // Range validation for numbers
      if (def.type === 'number' && def.validators) {
        if (def.validators.min !== undefined && value < def.validators.min) {
          errors.push({
            field: def.parameter_key,
            message: `${def.name} must be at least ${def.validators.min}`
          });
        }
        if (def.validators.max !== undefined && value > def.validators.max) {
          errors.push({
            field: def.parameter_key,
            message: `${def.name} must be at most ${def.validators.max}`
          });
        }
      }

      // Enum validation
      if (def.type === 'enum' && def.answers_key) {
        // This would need to fetch valid options
        // For now, we'll skip deep validation
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

module.exports = ParametersService;
