/**
 * IntentAnalyzer - Identifies user intent and relevant components from chat messages
 * 
 * This service performs the first pass of chat processing to determine:
 * 1. What the user wants to do (intent)
 * 2. Which components need to be modified
 * 3. Specific parameters or constraints mentioned
 */

const Anthropic = require('@anthropic-ai/sdk');

class IntentAnalyzer {
  constructor() {
    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY
    });
    
    // Component mapping for formulation structure
    this.componentMap = {
      ingredients: ['ingredients', 'ingredient list', 'components', 'recipe', 'formula'],
      cost: ['cost', 'price', 'pricing', 'budget', 'expense', 'cost analysis'],
      nutrition: ['nutrition', 'nutritional', 'protein', 'calories', 'vitamins', 'nutrients', 'nutritional profile'],
      manufacturing: ['manufacturing', 'production', 'process', 'procedure', 'specs', 'instructions'],
      packaging: ['packaging', 'package', 'container', 'bottle', 'pack'],
      sustainability: ['sustainability', 'sustainable', 'eco', 'environmental', 'green'],
      compliance: ['compliance', 'regulatory', 'certification', 'standards', 'legal'],
      shelfLife: ['shelf life', 'stability', 'expiry', 'preservation', 'storage']
    };
    
    // Intent categories
    this.intentCategories = {
      modify: ['change', 'modify', 'update', 'adjust', 'alter', 'edit'],
      increase: ['increase', 'add', 'more', 'boost', 'enhance', 'raise', 'higher'],
      decrease: ['decrease', 'reduce', 'less', 'lower', 'minimize', 'cut'],
      replace: ['replace', 'substitute', 'swap', 'switch', 'alternative', 'instead'],
      optimize: ['optimize', 'improve', 'better', 'enhance', 'refine'],
      fix: ['fix', 'correct', 'repair', 'resolve', 'solve'],
      analyze: ['analyze', 'explain', 'why', 'how', 'what if', 'compare']
    };
  }

  /**
   * Analyze user message to identify intent and relevant components
   * @param {string} userMessage - The user's chat message
   * @param {object} currentFormulation - Current formulation for context
   * @param {object} contextControl - Context control settings from toolbar
   * @returns {object} Analysis result with intent and components
   */
  async analyzeIntent(userMessage, currentFormulation = {}, contextControl = {}) {
    try {
      console.log(`[IntentAnalyzer] Analyzing message: "${userMessage}"`);
      console.log(`[IntentAnalyzer] Context control:`, contextControl);
      
      // Build a lightweight prompt for intent analysis
      const analysisPrompt = `
You are analyzing a user's message about modifying a product formulation.
Identify the user's intent and which components of the formulation they want to modify.

User Message: "${userMessage}"

Available formulation blocks:
- ingredients: The ingredient list and quantities
- cost: Cost analysis and pricing
- nutrition: Nutritional profile and values
- manufacturing: Production process and specifications
- packaging: Packaging specifications
- sustainability: Environmental impact metrics
- compliance: Regulatory and certification requirements
- shelfLife: Stability and storage requirements

Analyze the message and respond with this JSON structure:
{
  "intent": {
    "primary": "modify|increase|decrease|replace|optimize|fix|analyze",
    "specific": "Brief description of what user wants",
    "confidence": 1-10
  },
  "components": [
    {
      "name": "component name from list above",
      "relevance": "high|medium|low",
      "specific_aspect": "specific part mentioned (optional)"
    }
  ],
  "parameters": {
    "target_values": [],
    "constraints": [],
    "specific_items": []
  },
  "requires_full_context": false
}

IMPORTANT RULES:
1. Mark relevance as "high" ONLY for components directly mentioned or clearly targeted
2. Mark relevance as "medium" for components that might be affected
3. Mark relevance as "low" for components unlikely to change
4. For "fix" intents targeting a specific component, return ONLY that component with "high" relevance

Examples:
- "Increase protein content" → intent: increase, components: [{name: "nutrition", relevance: "high"}], parameters: {specific_items: ["protein"]}
- "Fix the nutritional profile" → intent: fix, components: [{name: "nutrition", relevance: "high"}], requires_full_context: false
- "Replace turmeric with something similar" → intent: replace, components: [{name: "ingredients", relevance: "high"}], parameters: {specific_items: ["turmeric"]}
- "Make it cheaper" → intent: decrease, components: [{name: "cost", relevance: "high"}, {name: "ingredients", relevance: "medium"}]
- "Fix validation errors" → intent: fix, components: [], requires_full_context: true
- "Improve everything" → intent: optimize, components: [], requires_full_context: true

Note: Set requires_full_context to true ONLY when you need to analyze the entire formulation to make a decision. 
For targeted fixes like "fix nutritional profile" or "fix cost analysis", set it to false.

Respond with JSON only.`;

      // Call Claude for intent analysis
      console.log(`[IntentAnalyzer] Calling Claude for intent analysis`);
      const response = await this.anthropic.messages.create({
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 1000,
        temperature: 0.2, // Lower temperature for more consistent analysis
        messages: [
          {
            role: 'user',
            content: analysisPrompt
          }
        ]
      });

      const content = response.content[0].text;
      console.log(`[IntentAnalyzer] Raw analysis response:`, content);
      
      // Parse the analysis
      let analysis;
      try {
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          analysis = JSON.parse(jsonMatch[0]);
        } else {
          analysis = JSON.parse(content);
        }
      } catch (parseError) {
        console.error('[IntentAnalyzer] Failed to parse analysis:', parseError);
        // Fallback to basic pattern matching
        analysis = this.fallbackAnalysis(userMessage);
      }
      
      // Validate and enhance analysis
      analysis = this.validateAnalysis(analysis, userMessage, contextControl);
      
      console.log(`[IntentAnalyzer] Final analysis:`, JSON.stringify(analysis, null, 2));
      return analysis;
      
    } catch (error) {
      console.error('[IntentAnalyzer] Error analyzing intent:', error);
      return this.fallbackAnalysis(userMessage);
    }
  }

  /**
   * Fallback analysis using pattern matching
   * @param {string} userMessage - The user's message
   * @returns {object} Basic analysis result
   */
  fallbackAnalysis(userMessage) {
    const lowerMessage = userMessage.toLowerCase();
    
    // Detect primary intent
    let primaryIntent = 'modify';
    let confidence = 5;
    
    for (const [intent, keywords] of Object.entries(this.intentCategories)) {
      if (keywords.some(keyword => lowerMessage.includes(keyword))) {
        primaryIntent = intent;
        confidence = 7;
        break;
      }
    }
    
    // Detect relevant components
    const components = [];
    for (const [component, keywords] of Object.entries(this.componentMap)) {
      if (keywords.some(keyword => lowerMessage.includes(keyword))) {
        components.push({
          name: component,
          relevance: 'high',
          specific_aspect: null
        });
      }
    }
    
    // If no specific components detected, include main ones
    if (components.length === 0) {
      components.push(
        { name: 'ingredients', relevance: 'medium' },
        { name: 'cost', relevance: 'low' },
        { name: 'nutrition', relevance: 'low' }
      );
    }
    
    return {
      intent: {
        primary: primaryIntent,
        specific: userMessage.substring(0, 100),
        confidence
      },
      components,
      parameters: {
        target_values: [],
        constraints: [],
        specific_items: []
      },
      requires_full_context: false
    };
  }

  /**
   * Validate and enhance the analysis
   * @param {object} analysis - The parsed analysis
   * @param {string} userMessage - Original message for validation
   * @param {object} contextControl - Context control settings
   * @returns {object} Validated analysis
   */
  validateAnalysis(analysis, userMessage, contextControl = {}) {
    // Ensure required fields exist
    if (!analysis.intent) {
      analysis.intent = {
        primary: 'modify',
        specific: userMessage,
        confidence: 5
      };
    }
    
    if (!analysis.components || !Array.isArray(analysis.components)) {
      analysis.components = [];
    }
    
    if (!analysis.parameters) {
      analysis.parameters = {
        target_values: [],
        constraints: [],
        specific_items: []
      };
    }
    
    // Special cases that always need full context
    const fullContextKeywords = ['everything', 'all', 'overall', 'complete', 'total', 'general'];
    if (fullContextKeywords.some(keyword => userMessage.toLowerCase().includes(keyword))) {
      analysis.requires_full_context = true;
    }
    
    // If confidence is low, include more components
    if (analysis.intent.confidence < 6 && analysis.components.length < 2) {
      analysis.components.push(
        { name: 'ingredients', relevance: 'low' },
        { name: 'cost', relevance: 'low' }
      );
    }
    
    // Apply componentScope filtering if specified
    if (contextControl && contextControl.componentScope && contextControl.componentScope !== 'all') {
      const scopeComponent = contextControl.componentScope;
      console.log(`[IntentAnalyzer] Applying component scope filter: ${scopeComponent}`);
      
      // If a specific component scope is set, filter to only that component
      if (scopeComponent !== 'all') {
        // Check if the scoped component exists in the analysis
        const hasScopedComponent = analysis.components.some(c => c.name === scopeComponent);
        
        if (hasScopedComponent) {
          // Keep only the scoped component
          analysis.components = analysis.components.filter(c => c.name === scopeComponent);
        } else {
          // Add the scoped component if not present
          analysis.components = [{
            name: scopeComponent,
            relevance: 'high',
            specific_aspect: 'User selected scope'
          }];
        }
        
        analysis.scope_applied = true;
        analysis.scope_component = scopeComponent;
      }
    }
    
    return analysis;
  }

  /**
   * Extract only the relevant components from the full formulation
   * @param {object} fullFormulation - Complete formulation object
   * @param {array} componentNames - Names of components to extract
   * @returns {object} Filtered formulation with only relevant components
   */
  extractRelevantComponents(fullFormulation, componentNames) {
    const relevantData = {
      version: fullFormulation.version,
      name: fullFormulation.name
    };
    
    // Map component names to actual formulation fields
    const fieldMapping = {
      ingredients: 'ingredients',
      cost: 'cost_analysis',
      nutrition: 'nutritional_profile',
      manufacturing: 'production_specs',
      packaging: 'packaging_specs',
      sustainability: 'sustainability_metrics',
      compliance: 'regulatory_compliance',
      shelfLife: 'shelf_life'
    };
    
    // Extract only requested components
    componentNames.forEach(componentName => {
      const fieldName = fieldMapping[componentName];
      if (fieldName && fullFormulation[fieldName]) {
        relevantData[fieldName] = fullFormulation[fieldName];
      }
    });
    
    // Always include basic metadata
    if (fullFormulation.quality_score) {
      relevantData.quality_score = fullFormulation.quality_score;
    }
    
    console.log(`[IntentAnalyzer] Extracted components:`, Object.keys(relevantData));
    return relevantData;
  }

  /**
   * Merge updated components back into the full formulation
   * @param {object} fullFormulation - Original complete formulation
   * @param {object} updatedComponents - Updated component values
   * @returns {object} Complete formulation with updates merged
   */
  mergeUpdatedComponents(fullFormulation, updatedComponents) {
    // Create a deep copy to avoid mutations
    const mergedFormulation = JSON.parse(JSON.stringify(fullFormulation));
    
    // Merge each updated field
    Object.keys(updatedComponents).forEach(key => {
      if (key !== 'version' && key !== 'name') {
        // Special handling for ingredients array - need to handle replacements properly
        if (key === 'ingredients' && Array.isArray(updatedComponents[key]) && Array.isArray(mergedFormulation[key])) {
          console.log(`[IntentAnalyzer] Processing ingredient updates`);
          console.log(`[IntentAnalyzer] Original ingredients: ${mergedFormulation[key].map(i => i.name).join(', ')}`);
          console.log(`[IntentAnalyzer] Updated ingredients from AI: ${updatedComponents[key].map(i => i.name).join(', ')}`);
          
          // IMPORTANT: If AI returns a partial ingredient list, it means those are the ONLY changes
          // We should identify what was replaced and merge properly
          
          // Check if this is a complete replacement (AI returned all ingredients)
          const originalCount = mergedFormulation[key].length;
          const updateCount = updatedComponents[key].length;
          
          if (updateCount >= originalCount - 2) {
            // AI returned most/all ingredients - use them directly
            console.log(`[IntentAnalyzer] Full ingredient replacement (${updateCount} ingredients)`);
            mergedFormulation[key] = updatedComponents[key];
          } else {
            // Partial update - AI only returned changed ingredients
            console.log(`[IntentAnalyzer] Partial ingredient update detected`);
            
            // For now, just replace the whole array
            // TODO: Implement smart merging based on ingredient names
            mergedFormulation[key] = updatedComponents[key];
            
            console.log(`[IntentAnalyzer] WARNING: Partial ingredient update resulted in fewer ingredients`);
          }
        } else {
          // For non-ingredient fields, do direct replacement
          mergedFormulation[key] = updatedComponents[key];
        }
      }
    });
    
    // Update version
    if (updatedComponents.version) {
      mergedFormulation.version = updatedComponents.version;
    }
    
    console.log(`[IntentAnalyzer] Merged updates for fields:`, Object.keys(updatedComponents));
    return mergedFormulation;
  }
}

module.exports = IntentAnalyzer;