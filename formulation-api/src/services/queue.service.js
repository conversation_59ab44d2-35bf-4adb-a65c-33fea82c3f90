/**
 * Lightweight MongoDB-backed job queue
 * Persistent, retryable, single-collection design
 */

const { mongoose } = require('../config/mongodb');

class QueueService {
  constructor(db) {
    this.db = db || mongoose.connection.db;
    this.col = this.db.collection('jobs');
  }

  async ensureIndexes() {
    await this.col.createIndex({ status: 1, availableAt: 1, priority: -1 });
    await this.col.createIndex({ projectId: 1 });
    await this.col.createIndex({ type: 1, status: 1 });
  }

  async enqueue({ type, payload, projectId = null, priority = 0, maxAttempts = 3, delayMs = 0 }) {
    const now = new Date();
    const job = {
      type,
      status: 'queued',
      projectId,
      payload,
      priority,
      attempts: 0,
      maxAttempts,
      availableAt: new Date(now.getTime() + delayMs),
      createdAt: now,
      updatedAt: now
    };
    const { insertedId } = await this.col.insertOne(job);
    return insertedId;
  }

  async claimNext(workerId) {
    const now = new Date();
    
    const result = await this.col.findOneAndUpdate(
      { status: 'queued', availableAt: { $lte: now } },
      { $set: { status: 'processing', lockedBy: workerId, lockedAt: now, processingStartedAt: now, updatedAt: now } },
      { sort: { priority: -1, createdAt: 1 }, returnDocument: 'after' }
    );
    
    // Handle different MongoDB driver versions
    const job = result?.value || result;
    return job || null;
  }

  async complete(jobId, result = {}) {
    const now = new Date();
    await this.col.updateOne(
      { _id: jobId },
      { $set: { status: 'completed', result, completedAt: now, updatedAt: now } }
    );
  }

  backoffMs(attempts) {
    // Exponential backoff: 30s, 60s, 120s...
    return Math.min(5 * 60 * 1000, Math.pow(2, attempts - 1) * 30000);
  }

  async fail(jobId, err, attempts, maxAttempts) {
    const now = new Date();
    if (attempts + 1 < maxAttempts) {
      const delay = this.backoffMs(attempts + 1);
      await this.col.updateOne(
        { _id: jobId },
        { $set: { status: 'queued', lastError: err?.message || String(err), availableAt: new Date(now.getTime() + delay), updatedAt: now }, $inc: { attempts: 1 } }
      );
    } else {
      await this.col.updateOne(
        { _id: jobId },
        { $set: { status: 'failed', lastError: err?.message || String(err), updatedAt: now }, $inc: { attempts: 1 } }
      );
    }
  }

  async get(jobId) {
    return this.col.findOne({ _id: jobId });
  }

  async removeByProjectId(projectId) {
    // Handle both string and ObjectId inputs
    const projectObjectId = typeof projectId === 'string' ? new mongoose.Types.ObjectId(projectId) : projectId;
    const result = await this.col.deleteMany({ 
      projectId: projectObjectId,
      status: { $in: ['queued', 'processing'] }
    });
    return result.deletedCount;
  }
}

module.exports = QueueService;

