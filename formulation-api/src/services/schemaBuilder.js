const FormulationBlock = require('../models/mongo/FormulationBlock');
const FormulationBinding = require('../models/mongo/FormulationBinding');
const baseSchema = require('../schemas/formulationSchema.json');

class SchemaBuilder {
  constructor() {
    this.baseSchema = JSON.parse(JSON.stringify(baseSchema)); // Deep clone without lodash
  }

  /**
   * Get the base schema
   * @returns {Object} - Base schema
   */
  getBaseSchema() {
    return JSON.parse(JSON.stringify(this.baseSchema));
  }

  /**
   * Build dynamic schema based on taxonomy and formulation blocks
   * @param {string} level - taxonomy level (industry, category, sub_category)
   * @param {string} slug - taxonomy slug
   * @returns {Object} - Generated schema
   */
  async buildSchema(level, slug) {
    try {
      // Get blocks bound to this taxonomy
      const boundBlocks = await FormulationBinding.getBlocksForTaxonomy(level, slug);
      
      if (!boundBlocks || boundBlocks.length === 0) {
        console.log(`No blocks found for ${level}:${slug}, using base schema`);
        return this.baseSchema;
      }

      // Start with base schema
      let dynamicSchema = JSON.parse(JSON.stringify(this.baseSchema));
      
      // Process each bound block
      for (const binding of boundBlocks) {
        const block = binding.block;
        const schemaPath = block.schema_path;
        
        // Apply block to schema
        await this.applyBlockToSchema(dynamicSchema, block, binding);
      }
      
      // Remove duplicates from required arrays
      this.removeDuplicatesFromSchema(dynamicSchema);
      
      // Validate and clean up schema
      this.validateSchema(dynamicSchema);
      
      console.log(`Built dynamic schema for ${level}:${slug} with ${boundBlocks.length} blocks`);
      return dynamicSchema;
      
    } catch (error) {
      console.error('Error building dynamic schema:', error);
      // Fallback to base schema
      return this.baseSchema;
    }
  }

  /**
   * Apply a formulation block to the schema
   * @param {Object} schema - Schema to modify
   * @param {Object} block - FormulationBlock
   * @param {Object} binding - FormulationBinding
   */
  async applyBlockToSchema(schema, block, binding) {
    const schemaPath = block.schema_path;
    const isRequired = binding.is_required || block.is_required;
    
    try {
      // Handle different schema path patterns
      if (schemaPath.startsWith('properties.recipes.items.properties.')) {
        // Recipe-level block
        this.applyToRecipeSchema(schema, block, binding, schemaPath);
      } else if (schemaPath.startsWith('properties.')) {
        // Root-level block
        this.applyToRootSchema(schema, block, binding, schemaPath);
      } else {
        console.warn(`Unknown schema path pattern: ${schemaPath}`);
      }
      
    } catch (error) {
      console.error(`Error applying block ${block.key}:`, error);
    }
  }

  /**
   * Apply block to recipe-level schema
   */
  applyToRecipeSchema(schema, block, binding, schemaPath) {
    // Extract the property path after 'properties.recipes.items.properties.'
    const propertyPath = schemaPath.replace('properties.recipes.items.properties.', '');
    
    // Navigate to recipe items properties
    if (!schema.properties.recipes.items.properties) {
      schema.properties.recipes.items.properties = {};
    }
    
    const recipeProps = schema.properties.recipes.items.properties;
    
    // Apply block schema
    if (binding.override_schema) {
      // Use custom schema from binding
      try {
        const customSchema = JSON.parse(binding.override_schema);
        this.setNestedProperty(recipeProps, propertyPath, customSchema);
      } catch (e) {
        console.error(`Invalid override_schema for ${block.key}:`, e);
      }
    } else {
      // Use default block schema based on category
      const blockSchema = this.getDefaultBlockSchema(block);
      this.setNestedProperty(recipeProps, propertyPath, blockSchema);
    }
    
    // Handle required fields
    if (binding.is_required || block.is_required) {
      if (!schema.properties.recipes.items.required) {
        schema.properties.recipes.items.required = [];
      }
      
      const topLevelProp = propertyPath.split('.')[0];
      if (!schema.properties.recipes.items.required.includes(topLevelProp)) {
        schema.properties.recipes.items.required.push(topLevelProp);
      }
    }
  }

  /**
   * Apply block to root-level schema
   */
  applyToRootSchema(schema, block, binding, schemaPath) {
    // Extract the property path after 'properties.'
    const propertyPath = schemaPath.replace('properties.', '');
    
    // Apply block schema
    if (binding.override_schema) {
      try {
        const customSchema = JSON.parse(binding.override_schema);
        this.setNestedProperty(schema.properties, propertyPath, customSchema);
      } catch (e) {
        console.error(`Invalid override_schema for ${block.key}:`, e);
      }
    } else {
      const blockSchema = this.getDefaultBlockSchema(block);
      this.setNestedProperty(schema.properties, propertyPath, blockSchema);
    }
    
    // Handle required fields
    if (binding.is_required || block.is_required) {
      if (!schema.required) {
        schema.required = [];
      }
      
      const topLevelProp = propertyPath.split('.')[0];
      if (!schema.required.includes(topLevelProp)) {
        schema.required.push(topLevelProp);
      }
    }
  }

  /**
   * Get default schema for a block based on its category
   */
  getDefaultBlockSchema(block) {
    const { category, key, name, description } = block;
    
    switch (category) {
      case 'core':
        return {
          type: 'object',
          description: description || `Core block: ${name}`,
          required: ['value'],
          properties: {
            value: { type: 'string', minLength: 1 },
            details: { type: 'string' }
          }
        };
        
      case 'analysis':
        return {
          type: 'object',
          description: description || `Analysis block: ${name}`,
          properties: {
            methodology: { type: 'string' },
            results: { type: 'array', items: { type: 'object' } },
            conclusions: { type: 'string' }
          }
        };
        
      case 'compliance':
        return {
          type: 'object',
          description: description || `Compliance block: ${name}`,
          properties: {
            status: { type: 'string', enum: ['compliant', 'non-compliant', 'pending'] },
            certifications: { type: 'array', items: { type: 'string' } },
            requirements: { type: 'string' }
          }
        };
        
      case 'optional':
      default:
        return {
          type: 'object',
          description: description || `Optional block: ${name}`,
          properties: {
            included: { type: 'boolean', default: false },
            details: { type: 'string' }
          }
        };
    }
  }

  /**
   * Validate the generated schema
   */
  validateSchema(schema) {
    // Basic validation
    if (!schema.type) {
      schema.type = 'object';
    }
    
    if (!schema.properties) {
      schema.properties = {};
    }
    
    // Ensure recipes array exists
    if (!schema.properties.recipes) {
      schema.properties.recipes = this.baseSchema.properties.recipes;
    }
    
    return true;
  }

  /**
   * Remove duplicates from required arrays in schema recursively
   * @param {Object} obj - Object to process
   */
  removeDuplicatesFromSchema(obj) {
    if (!obj || typeof obj !== 'object') return;

    // If this object has a required array, remove duplicates
    if (obj.required && Array.isArray(obj.required)) {
      obj.required = [...new Set(obj.required)];
    }

    // Recursively process all properties
    for (const key in obj) {
      if (obj.hasOwnProperty(key) && typeof obj[key] === 'object') {
        if (Array.isArray(obj[key])) {
          // Process each item in array
          obj[key].forEach(item => this.removeDuplicatesFromSchema(item));
        } else {
          // Process nested object
          this.removeDuplicatesFromSchema(obj[key]);
        }
      }
    }
  }

  /**
   * Get schema for a specific taxonomy
   * @param {string} industry - Industry slug
   * @param {string} category - Category slug (optional)
   * @param {string} subCategory - Sub-category slug (optional)
   * @returns {Object} - Generated schema
   */
  async getSchemaForTaxonomy(industry, category = null, subCategory = null) {
    let schema = JSON.parse(JSON.stringify(this.baseSchema));
    
    try {
      // Apply industry-level components
      const industrySchema = await this.buildSchema('industry', industry);
      schema = this.mergeSchemas(schema, industrySchema);
      
      // Apply category-level components if provided
      if (category) {
        const categorySchema = await this.buildSchema('category', category);
        schema = this.mergeSchemas(schema, categorySchema);
      }
      
      // Apply sub-category-level components if provided
      if (subCategory) {
        const subCategorySchema = await this.buildSchema('sub_category', subCategory);
        schema = this.mergeSchemas(schema, subCategorySchema);
      }
      
      // Remove duplicates from the final merged schema
      this.removeDuplicatesFromSchema(schema);
      
      return schema;
      
    } catch (error) {
      console.error('Error getting schema for taxonomy:', error);
      return this.baseSchema;
    }
  }

  /**
   * Merge two schemas together
   */
  mergeSchemas(baseSchema, additionalSchema) {
    return this.deepMerge(baseSchema, additionalSchema);
  }

  /**
   * Deep merge two objects
   */
  deepMerge(target, source) {
    const result = JSON.parse(JSON.stringify(target));
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (Array.isArray(source[key])) {
          // Special handling for 'required' and 'enum' arrays to avoid duplicates
          if (key === 'required' || key === 'enum') {
            const existingItems = result[key] || [];
            const newItems = source[key] || [];
            result[key] = [...new Set([...existingItems, ...newItems])];
          } else {
            result[key] = [...(result[key] || []), ...source[key]];
          }
        } else if (typeof source[key] === 'object' && source[key] !== null) {
          result[key] = this.deepMerge(result[key] || {}, source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }
    
    return result;
  }

  /**
   * Set nested property using dot notation
   */
  setNestedProperty(obj, path, value) {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  /**
   * Legacy merge method - keeping for compatibility
   */
  legacyMergeSchemas(baseSchema, additionalSchema) {
    return this.deepMerge(baseSchema, additionalSchema);
  }
}

module.exports = SchemaBuilder;