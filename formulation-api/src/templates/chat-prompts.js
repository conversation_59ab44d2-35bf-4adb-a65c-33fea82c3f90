/**
 * Chat Prompt Templates for Formulation Playground
 * 
 * Template-based system for easy iteration and improvement of LLM prompts
 * Each template can be versioned and A/B tested
 */

const FORMULATION_CHAT_TEMPLATES = {
  // Main template for formulation editing
  FORMULATION_EDITOR: {
    version: '1.0.0',
    name: 'Formulation Editor',
    description: 'Primary template for editing formulation blocks via chat',
    
    systemPrompt: `You are an expert formulation scientist and product development assistant. Your role is to ACTIVELY MODIFY and UPGRADE product formulations based on user input.

CRITICAL REQUIREMENT: 
You MUST always return an upgraded/modified version of the formulation. Never just provide advice - ALWAYS make actual changes to improve the product.

CORE RESPONSIBILITIES:
- MANDATORY: Make concrete modifications to the formulation based on user input
- Upgrade ingredients, optimize costs, enhance nutrition, or improve manufacturing
- Maintain formulation integrity and compliance while implementing improvements
- Provide clear explanations for the specific changes made
- Proactively suggest and implement additional optimizations

RESPONSE FORMAT:
You must respond with a valid JSON object containing:
1. "changes_made": Array of specific changes applied (REQUIRED - never empty)
2. "explanation": Clear explanation of what was changed and why  
3. "updated_blocks": COMPLETE updated formulation blocks (not just modified parts)
4. "upgrade_summary": Brief summary of how this version is better than the previous
5. "additional_improvements": Optional further optimizations implemented
6. "warnings": Any concerns or recommendations (optional)
7. "confidence": Confidence level (1-10) in the changes made
8. "version_increment": How this should be versioned (e.g., "1.1", "2.0")

MANDATORY BEHAVIOR:
- ALWAYS make at least one concrete improvement to the formulation
- If user request is vague, interpret it as an optimization opportunity
- NEVER respond with "no changes needed" - always find improvements
- Return COMPLETE updated blocks, not just diffs
- Ensure all percentages, costs, and specs are recalculated
- Increment version number based on significance of changes

CONSTRAINTS:
- Ensure ingredient percentages always sum to 100%
- Maintain cost calculations accuracy
- Preserve regulatory compliance
- Flag any potentially unsafe changes but still provide the upgrade`,

    userPrompt: `{productJourneyContext}

CONVERSATION HISTORY:
{conversationHistory}

CURRENT FORMULATION (VERSION TO UPGRADE):
{currentVariant}

USER REQUEST:
"{userMessage}"

MANDATORY TASK: 
You must create an upgraded version of this formulation. Even if the user just asks a question, interpret it as a request for optimization and improvement. 

UPGRADE REQUIREMENTS:
1. Make concrete changes to improve the formulation
2. Return the complete updated formulation with all blocks
3. Recalculate all costs, percentages, and nutritional values
4. Provide the new version number
5. Explain exactly what improvements were made

If the user request is unclear, implement the most logical optimization (cost reduction, nutritional improvement, ingredient upgrade, etc.).

Return your upgraded formulation in the exact JSON format specified.`,

    outputSchema: {
      type: 'object',
      required: ['changes_made', 'explanation', 'updated_blocks', 'upgrade_summary', 'confidence', 'version_increment'],
      properties: {
        changes_made: {
          type: 'array',
          items: { type: 'string' },
          minItems: 1,
          description: "Must contain at least one change - never empty"
        },
        explanation: { type: 'string' },
        updated_blocks: { 
          type: 'object',
          description: "Complete updated formulation with all blocks"
        },
        upgrade_summary: {
          type: 'string',
          description: "Brief summary of how this version improves the previous"
        },
        additional_improvements: {
          type: 'array',
          items: { type: 'string' },
          description: "Optional additional optimizations implemented"
        },
        warnings: {
          type: 'array',
          items: { type: 'string' }
        },
        confidence: {
          type: 'number',
          minimum: 1,
          maximum: 10
        },
        version_increment: {
          type: 'string',
          description: "Version number for this upgrade (e.g. '1.1', '2.0')"
        }
      }
    }
  },

  // Template for ingredient-specific modifications
  INGREDIENT_OPTIMIZER: {
    version: '1.0.0',
    name: 'Ingredient Optimizer',
    description: 'Specialized template for ingredient-focused changes',
    
    systemPrompt: `You are a specialized ingredient optimization expert. You MUST create an upgraded ingredient formulation based on user input.

CRITICAL REQUIREMENT:
You MUST always return a concrete upgrade to the ingredient formulation - never just advice.

EXPERTISE AREAS:
- Ingredient functionality and interactions
- Substitution recommendations
- Percentage optimization
- Cost-effective alternatives
- Nutritional impact analysis
- Regulatory compliance for ingredients

MANDATORY RESPONSE REQUIREMENTS:
- ALWAYS implement actual ingredient changes
- Recalculate all dependent values (cost, nutrition, percentages)
- Return complete updated ingredient list
- Provide version increment
- Explain functional impact of upgrades
- Suggest and implement complementary optimizations`,

    userPrompt: `CURRENT INGREDIENTS (TO BE UPGRADED):
{ingredients}

COST CONSTRAINTS:
{costConstraints}

USER REQUEST:
"{userMessage}"

MANDATORY TASK: Create an upgraded ingredient formulation. Make actual changes to improve functionality, cost-effectiveness, or nutritional value. Return the complete updated formulation with all recalculated values.`
  },

  // Template for cost optimization
  COST_OPTIMIZER: {
    version: '1.0.0',
    name: 'Cost Optimizer',
    description: 'Template for cost-focused modifications and analysis',
    
    systemPrompt: `You are a cost optimization specialist for product formulations. You MUST deliver a cost-optimized upgrade to the formulation.

CRITICAL REQUIREMENT:
You MUST always return an actual cost-optimized version of the formulation - never just analysis.

EXPERTISE AREAS:
- Raw material cost reduction
- Supplier optimization and substitutions
- Margin improvement strategies
- Price-performance optimization
- Market positioning through cost efficiency

MANDATORY RESPONSE REQUIREMENTS:
- ALWAYS implement actual cost optimizations
- Modify ingredients to reduce costs while maintaining quality
- Recalculate all cost structures and margins
- Return complete updated formulation
- Provide new version number with cost improvements
- Maintain or improve product quality and compliance`,

    userPrompt: `CURRENT COST ANALYSIS (TO BE OPTIMIZED):
{costAnalysis}

MARKET CONSTRAINTS:
Target Retail: {targetRetail}
Min Margin: {minMargin}%

USER REQUEST:
"{userMessage}"

MANDATORY TASK: Create a cost-optimized upgrade to this formulation. Make actual changes to reduce costs, improve margins, or enhance value. Return the complete updated formulation with recalculated costs.`
  },

  // Template for chat initialization - provides comprehensive context
  CHAT_INITIALIZATION: {
    version: '1.0.0',
    name: 'Chat Initialization',
    description: 'Comprehensive introduction template for first chat interaction',
    
    systemPrompt: `You are an expert formulation scientist and product development assistant. This is the beginning of a collaborative formulation optimization session.

ROLE:
- Expert formulation scientist with deep knowledge across industries
- Product development consultant 
- Cost optimization specialist
- Regulatory compliance advisor
- Manufacturing process expert

APPROACH:
- Provide comprehensive analysis of the current formulation state
- Identify optimization opportunities
- Offer actionable recommendations
- Maintain scientific rigor and regulatory awareness
- Focus on practical, implementable solutions

RESPONSE STYLE:
- Professional yet approachable
- Data-driven insights
- Clear explanations of technical concepts
- Proactive suggestions for improvement
- Acknowledge current achievements and potential enhancements`,

    userPrompt: `PRODUCT DEVELOPMENT JOURNEY OVERVIEW:
{productJourneyContext}

CURRENT FORMULATION STATE:
{currentVariant}

FORMULATION ANALYSIS:
{formulationAnalysis}

INITIALIZATION TASK:
Provide a comprehensive welcome message as a formulation optimization expert ready to actively upgrade this product. Your message should:

1. Acknowledge the current product and development stage (be encouraging!)
2. Summarize the key formulation characteristics and achievements
3. Identify 3-4 concrete optimization opportunities you can implement
4. Explain how you will actively upgrade and optimize this formulation (not just advise)
5. Ask a strategic question to understand their priorities for the first upgrade

IMPORTANT: Make it clear that every interaction will result in an actual improved version of their formulation, not just recommendations. Position yourself as ready to make immediate upgrades.`
  }
};

/**
 * Template Context Builders
 * Functions to prepare context data for each template
 */
const CONTEXT_BUILDERS = {
  conversationHistory: (messages) => {
    if (!messages || messages.length === 0) {
      return "No previous conversation - this is the first interaction.";
    }
    return messages.map((msg, index) => 
      `${index + 1}. ${msg.isUser ? 'User' : 'Assistant'}: ${msg.content}`
    ).join('\n');
  },

  currentVariant: (variant) => {
    return JSON.stringify({
      name: variant.name,
      ingredients: variant.ingredients,
      cost_analysis: variant.costAnalysis,
      nutritional_profile: variant.nutritionalProfile,
      manufacturing_procedure: variant.manufacturingProcedure
    }, null, 2);
  },

  ingredients: (variant) => {
    return JSON.stringify(variant.ingredients || [], null, 2);
  },

  costAnalysis: (variant) => {
    return JSON.stringify(variant.costAnalysis || {}, null, 2);
  },

  costConstraints: (constraints = {}) => {
    return JSON.stringify({
      maxRawMaterialCost: constraints.maxCost,
      targetMargin: constraints.targetMargin,
      competitorPrice: constraints.competitorPrice
    }, null, 2);
  },

  // Rich context for chat initialization
  productJourneyContext: (projectData) => {
    const journey = projectData.journey || {};
    const user = projectData.user || {};
    
    return `PRODUCT DEVELOPMENT CONTEXT:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📊 PROJECT OVERVIEW:
• Product Name: ${projectData.name || 'Unnamed Product'}
• Industry: ${journey.industry || 'Not specified'}
• Product Category: ${journey.productType || 'Not specified'}
• Development Stage: Formulation Optimization (Playground)
• Created: ${projectData.created_at ? new Date(projectData.created_at).toLocaleDateString() : 'Recently'}
• Version: ${projectData.currentFormulation?.version || 1}

👤 DEVELOPER PROFILE:
• Company: ${user.company || 'Independent Developer'}
• Experience Level: ${user.role || 'Product Developer'}
• Previous Projects: ${projectData.userProjectCount || 'First project'}

🎯 ORIGINAL GOALS & CONSTRAINTS:
• Target Market: ${journey.targetMarket || 'Consumer wellness'}
• Budget Range: ${journey.budgetRange || 'Mid-range'}
• Key Requirements: ${journey.keyRequirements || 'Quality and compliance focus'}
• Regulatory Needs: ${journey.regulatoryRequirements || 'Standard compliance'}
• Sustainability Focus: ${journey.sustainabilityLevel || 'Moderate'}

🔄 FORMULATION JOURNEY:
• Generation Method: ${projectData.generationMethod || 'AI-assisted'}
• Iterations Completed: ${projectData.iterationCount || 1}
• Previous Optimizations: ${projectData.optimizationHistory || 'None yet'}
• Quality Assessments: ${projectData.qualityHistory || 'Initial assessment completed'}

📈 CURRENT POSITION:
• Development Phase: Active formulation refinement
• Next Milestone: Commercial optimization
• Key Focus Areas: Cost efficiency, ingredient optimization, compliance validation
• Decision Points: Finalization for production planning

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`;
  },

  // Comprehensive formulation analysis for initialization
  formulationAnalysis: (variant, variations = []) => {
    const ingredients = variant.ingredients || [];
    const costAnalysis = variant.costAnalysis || {};
    const nutrition = variant.nutritionalProfile || {};
    
    // Calculate key metrics
    const totalIngredients = ingredients.length;
    const averagePercentage = totalIngredients > 0 ? 
      ingredients.reduce((sum, ing) => sum + (ing.percentage || 0), 0) / totalIngredients : 0;
    const costPerUnit = costAnalysis.raw_material_cost || 0;
    const retailPrice = costAnalysis.suggested_retail || 0;
    const margin = costAnalysis.margin_percentage || 0;
    const variationCount = variations.length;

    return `FORMULATION ANALYSIS SUMMARY:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🧪 COMPOSITION ANALYSIS:
• Total Ingredients: ${totalIngredients}
• Formulation Complexity: ${totalIngredients < 5 ? 'Simple' : totalIngredients < 10 ? 'Moderate' : 'Complex'}
• Primary Components: ${ingredients.slice(0, 3).map(ing => ing.name).join(', ')}
• Formulation Balance: ${averagePercentage > 95 ? 'Well-balanced' : 'Needs adjustment'}

💰 COST STRUCTURE:
• Raw Material Cost: ₹${costPerUnit.toLocaleString()}
• Suggested Retail: ₹${retailPrice.toLocaleString()}  
• Profit Margin: ${margin}%
• Cost Competitiveness: ${margin > 40 ? 'Excellent' : margin > 25 ? 'Good' : 'Needs improvement'}

🏭 PRODUCTION READINESS:
• Manufacturing Complexity: ${variant.manufacturingProcedure ? 'Defined' : 'Needs specification'}
• Scalability: ${totalIngredients < 8 ? 'High' : 'Moderate'}
• Equipment Requirements: ${variant.manufacturingProcedure?.equipment_needed || 'Standard processing'}

📊 VARIATIONS AVAILABLE:
• Total Variations: ${variationCount}
• Geographic Adaptations: ${variations.filter(v => v.variation_type === 'geographic').length}
• Flavor Profiles: ${variations.filter(v => v.variation_type === 'flavor').length}
• Premium Options: ${variations.filter(v => v.variation_type === 'premium').length}

🎯 OPTIMIZATION OPPORTUNITIES:
• Cost Optimization Potential: ${margin < 30 ? 'High priority' : 'Standard review'}
• Ingredient Sourcing: ${totalIngredients > 10 ? 'Simplification possible' : 'Streamlined'}
• Nutritional Enhancement: ${Object.keys(nutrition).length < 5 ? 'Expansion recommended' : 'Comprehensive'}
• Market Positioning: ${retailPrice ? 'Price point established' : 'Pricing strategy needed'}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`;
  }
};

/**
 * Template Selection Logic
 * Determines which template to use based on user message content
 */
const TEMPLATE_SELECTOR = {
  selectTemplate: (userMessage, context = {}) => {
    const { isInitialization = false, conversationHistory = [] } = context;
    
    // First message - use initialization template
    if (isInitialization || conversationHistory.length === 0) {
      return 'CHAT_INITIALIZATION';
    }
    
    const message = userMessage.toLowerCase();
    
    // Ingredient-focused requests
    if (message.includes('ingredient') || 
        message.includes('substitute') || 
        message.includes('replace') ||
        message.includes('add ') ||
        message.includes('remove ')) {
      return 'INGREDIENT_OPTIMIZER';
    }
    
    // Cost-focused requests
    if (message.includes('cost') || 
        message.includes('price') || 
        message.includes('margin') ||
        message.includes('cheaper') ||
        message.includes('expensive')) {
      return 'COST_OPTIMIZER';
    }
    
    // Default to general formulation editor
    return 'FORMULATION_EDITOR';
  }
};

/**
 * Prompt Builder - Main interface for generating prompts
 */
class PromptBuilder {
  constructor() {
    this.templates = FORMULATION_CHAT_TEMPLATES;
    this.contextBuilders = CONTEXT_BUILDERS;
    this.selector = TEMPLATE_SELECTOR;
  }

  /**
   * Build complete prompt for LLM
   * @param {string} userMessage - User's chat message
   * @param {object} variant - Current formulation variant
   * @param {array} conversationHistory - Previous messages
   * @param {object} projectData - Complete project context for initialization
   * @param {string} templateName - Optional specific template to use
   * @returns {object} Complete prompt with system and user messages
   */
  buildPrompt(userMessage, variant, conversationHistory = [], projectData = {}, templateName = null) {
    // Determine context for template selection
    const selectionContext = {
      isInitialization: conversationHistory.length === 0,
      conversationHistory,
      variant
    };

    // Select appropriate template
    const selectedTemplate = templateName || this.selector.selectTemplate(userMessage, selectionContext);
    const template = this.templates[selectedTemplate];
    
    if (!template) {
      throw new Error(`Template '${selectedTemplate}' not found`);
    }

    // Extract output instructions if provided
    const outputInstructions = projectData.outputInstructions || '';

    // Build context variables
    const context = {
      conversationHistory: this.contextBuilders.conversationHistory(conversationHistory),
      currentVariant: this.contextBuilders.currentVariant(variant),
      userMessage: userMessage,
      ingredients: this.contextBuilders.ingredients(variant),
      costAnalysis: this.contextBuilders.costAnalysis(variant),
      costConstraints: this.contextBuilders.costConstraints(variant.constraints),
      targetRetail: variant.costAnalysis?.suggested_retail || 'Not specified',
      minMargin: variant.constraints?.minMargin || 20,
      // Rich context for initialization
      productJourneyContext: this.contextBuilders.productJourneyContext(projectData),
      formulationAnalysis: this.contextBuilders.formulationAnalysis(variant, projectData.variations),
      // Add output instructions
      outputInstructions: outputInstructions
    };

    // Modify system prompt to include output instructions
    let modifiedSystemPrompt = template.systemPrompt;
    if (outputInstructions) {
      modifiedSystemPrompt = template.systemPrompt + '\n\n' + outputInstructions;
    }

    // Replace template variables
    const systemPrompt = this.replaceVariables(modifiedSystemPrompt, context);
    const userPrompt = this.replaceVariables(template.userPrompt + 
      (outputInstructions ? '\n\n' + outputInstructions : ''), context);

    return {
      templateInfo: {
        name: template.name,
        version: template.version,
        description: template.description
      },
      systemPrompt,
      userPrompt,
      outputSchema: template.outputSchema,
      context,
      isInitialization: selectedTemplate === 'CHAT_INITIALIZATION'
    };
  }

  /**
   * Replace template variables with actual values
   * @param {string} template - Template string with {variable} placeholders
   * @param {object} context - Context values
   * @returns {string} Processed template
   */
  replaceVariables(template, context) {
    return template.replace(/\{(\w+)\}/g, (match, key) => {
      return context[key] || match;
    });
  }

  /**
   * Get available templates
   * @returns {array} List of available templates
   */
  getAvailableTemplates() {
    return Object.keys(this.templates).map(key => ({
      key,
      name: this.templates[key].name,
      version: this.templates[key].version,
      description: this.templates[key].description
    }));
  }

  /**
   * Add or update a template
   * @param {string} key - Template key
   * @param {object} template - Template definition
   */
  addTemplate(key, template) {
    this.templates[key] = {
      ...template,
      version: template.version || '1.0.0'
    };
  }

  /**
   * Get template analytics (for A/B testing)
   * @param {string} templateKey - Template to analyze
   * @returns {object} Usage statistics
   */
  getTemplateAnalytics(templateKey) {
    // This would connect to analytics service
    // For now, return placeholder
    return {
      templateKey,
      usageCount: 0,
      averageConfidence: 0,
      successRate: 0,
      lastUsed: null
    };
  }
}

module.exports = {
  FORMULATION_CHAT_TEMPLATES,
  CONTEXT_BUILDERS,
  TEMPLATE_SELECTOR,
  PromptBuilder
};