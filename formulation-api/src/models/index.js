// MongoDB Models Export
// This replaces the old Sequelize models index

const { mongoConnection } = require('../config/mongodb');

// Import MongoDB models
const User = require('./mongo/User');
const Project = require('./mongo/Project');
const Session = require('./mongo/Session');

// Export models and connection utilities
module.exports = {
  // Models
  User,
  Project,
  Session,
  
  // Connection utilities
  mongoConnection,
  
  // Helper functions
  async connect(environment = 'development') {
    return await mongoConnection.connect(environment);
  },
  
  async disconnect() {
    return await mongoConnection.disconnect();
  },
  
  async ping() {
    return await mongoConnection.ping();
  },
  
  isConnected() {
    return mongoConnection.isConnectionReady();
  },
  
  // Note: Formulations are now embedded in Projects as current_formulation
};