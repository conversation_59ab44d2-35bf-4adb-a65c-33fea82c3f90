/**
 * Validation Schemas for MongoDB Collections
 * Provides validation and type checking for all configuration data
 */

// Taxonomy Schema Validator
const validateTaxonomy = (data) => {
  const errors = [];
  
  if (!data.slug || typeof data.slug !== 'string') {
    errors.push('slug is required and must be a string');
  }
  
  if (!data.name || typeof data.name !== 'string') {
    errors.push('name is required and must be a string');
  }
  
  if (!data.level || !['industry', 'category', 'sub_category'].includes(data.level)) {
    errors.push('level must be one of: industry, category, sub_category');
  }
  
  if (data.parent_id !== undefined && data.parent_id !== null && typeof data.parent_id !== 'string') {
    errors.push('parent_id must be a string or null');
  }
  
  if (data.status && !['active', 'inactive'].includes(data.status)) {
    errors.push('status must be active or inactive');
  }
  
  return { valid: errors.length === 0, errors };
};

// Parameter Schema Validator
const validateParameter = (data) => {
  const errors = [];
  
  if (!data.key || typeof data.key !== 'string') {
    errors.push('key is required and must be a string');
  }
  
  if (!data.name || typeof data.name !== 'string') {
    errors.push('name is required and must be a string');
  }
  
  if (!data.type || !['string', 'number', 'enum', 'boolean', 'array', 'table'].includes(data.type)) {
    errors.push('type must be one of: string, number, enum, boolean, array, table');
  }
  
  if (data.answers_key && typeof data.answers_key !== 'string') {
    errors.push('answers_key must be a string');
  }

  // Optional UI grouping panel (for future UI use)
  if (data.panel !== undefined && data.panel !== null && typeof data.panel !== 'string') {
    errors.push('panel must be a string');
  }
  
  return { valid: errors.length === 0, errors };
};

// Parameter Binding Schema Validator
const validateParamBinding = (data) => {
  const errors = [];
  
  if (!data.parameter_key || typeof data.parameter_key !== 'string') {
    errors.push('parameter_key is required and must be a string');
  }
  
  if (!data.target || typeof data.target !== 'object') {
    errors.push('target is required and must be an object');
  } else {
    if (!data.target.level || !['industry', 'category', 'sub_category'].includes(data.target.level)) {
      errors.push('target.level must be one of: industry, category, sub_category');
    }
    if (!data.target.slug || typeof data.target.slug !== 'string') {
      errors.push('target.slug is required and must be a string');
    }
  }
  
  if (data.widget && typeof data.widget === 'object') {
    if (!data.widget.type || !['select', 'slider', 'table', 'text', 'checkbox'].includes(data.widget.type)) {
      errors.push('widget.type must be one of: select, slider, table, text, checkbox');
    }
  }

  // Optional UI grouping panel on binding (overrides parameter.panel when present)
  if (data.panel !== undefined && data.panel !== null && typeof data.panel !== 'string') {
    errors.push('panel must be a string');
  }
  
  return { valid: errors.length === 0, errors };
};

// Parameter Answer Schema Validator
const validateParamAnswer = (data) => {
  const errors = [];
  
  if (!data.key || typeof data.key !== 'string') {
    errors.push('key is required and must be a string');
  }
  
  if (!data.name || typeof data.name !== 'string') {
    errors.push('name is required and must be a string');
  }
  
  if (!data.options || !Array.isArray(data.options)) {
    errors.push('options is required and must be an array');
  } else {
    data.options.forEach((option, index) => {
      if (!option.value || typeof option.value !== 'string') {
        errors.push(`options[${index}].value is required and must be a string`);
      }
      if (!option.label || typeof option.label !== 'string') {
        errors.push(`options[${index}].label is required and must be a string`);
      }
    });
  }
  
  return { valid: errors.length === 0, errors };
};

// UI Flow Schema Validator
const validateUIFlow = (data) => {
  const errors = [];
  
  if (!data.slug || typeof data.slug !== 'string') {
    errors.push('slug is required and must be a string');
  }
  
  if (!data.name || typeof data.name !== 'string') {
    errors.push('name is required and must be a string');
  }
  
  if (!data.steps || !Array.isArray(data.steps)) {
    errors.push('steps is required and must be an array');
  } else {
    data.steps.forEach((step, index) => {
      if (!step.slug || typeof step.slug !== 'string') {
        errors.push(`steps[${index}].slug is required and must be a string`);
      }
      if (!step.title || typeof step.title !== 'string') {
        errors.push(`steps[${index}].title is required and must be a string`);
      }
    });
  }
  
  return { valid: errors.length === 0, errors };
};

// Guardrail Schema Validator
const validateGuardrail = (data) => {
  const errors = [];
  
  if (!data.name || typeof data.name !== 'string') {
    errors.push('name is required and must be a string');
  }
  
  if (!data.scope || !['industry', 'category', 'sub_category', 'functional-food', 'all'].includes(data.scope)) {
    errors.push('scope must be one of: industry, category, sub_category, functional-food, all');
  }
  
  if (!data.target || typeof data.target !== 'string') {
    errors.push('target is required and must be a string');
  }
  
  if (!data.type || !['expression', 'sum_fields'].includes(data.type)) {
    errors.push('type must be one of: expression, sum_fields');
  }
  
  if (!data.severity || !['error', 'warning', 'info'].includes(data.severity)) {
    errors.push('severity must be one of: error, warning, info');
  }
  
  if (!data.message || typeof data.message !== 'string') {
    errors.push('message is required and must be a string');
  }
  
  return { valid: errors.length === 0, errors };
};

// Component Schema Validator (for reference)
const validateComponent = (data) => {
  const errors = [];
  
  if (!data.inci || typeof data.inci !== 'string') {
    errors.push('inci is required and must be a string');
  }
  
  if (!data.name || typeof data.name !== 'string') {
    errors.push('name is required and must be a string');
  }
  
  if (!data.category || typeof data.category !== 'string') {
    errors.push('category is required and must be a string');
  }
  
  return { valid: errors.length === 0, errors };
};

// Guardrail Evaluation Request Validator
const validateGuardrailRequest = (data) => {
  const errors = [];
  
  if (!data.industry || typeof data.industry !== 'string') {
    errors.push('industry is required and must be a string');
  }
  
  if (!data.category || typeof data.category !== 'string') {
    errors.push('category is required and must be a string');
  }
  
  if (!data.sub_category || typeof data.sub_category !== 'string') {
    errors.push('sub_category is required and must be a string');
  }
  
  if (!data.answers || typeof data.answers !== 'object') {
    errors.push('answers is required and must be an object');
  }
  
  if (data.components && typeof data.components !== 'object') {
    errors.push('components must be an object');
  }
  
  return { valid: errors.length === 0, errors };
};

module.exports = {
  validateTaxonomy,
  validateParameter,
  validateParamBinding,
  validateParamAnswer,
  validateUIFlow,
  validateGuardrail,
  validateComponent,
  validateGuardrailRequest
};
