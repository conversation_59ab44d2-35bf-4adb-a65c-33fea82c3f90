const mongoose = require('mongoose');

const formulationBlockSchema = new mongoose.Schema({
  key: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^[a-z_]+$/, 'Key must contain only lowercase letters and underscores']
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 500
  },
  prompt_instructions: {
    type: String,
    required: false,
    trim: true,
    maxlength: 2000,
    default: '',
    description: 'Instructions for LLM on how to generate this block'
  },
  output_format: {
    type: String,
    required: false,
    trim: true,
    maxlength: 1000,
    default: '',
    description: 'Expected output format for this block (e.g., table, list, paragraph)'
  },
  is_required: {
    type: Boolean,
    default: false,
    description: 'Whether this block is required for formulation generation'
  },
  display_order: {
    type: Number,
    default: 0,
    description: 'Order in which blocks should appear in formulation'
  },
  is_active: {
    type: Boolean,
    default: true
  },
  block_type: {
    type: String,
    required: true,
    enum: ['ingredients', 'analysis', 'compliance', 'manufacturing', 'sustainability', 'packaging', 'other'],
    default: 'other',
    description: 'Type of formulation block for categorization'
  },
  dependencies: [{
    type: String,
    description: 'Keys of other blocks this block depends on'
  }],
  validation_rules: {
    type: mongoose.Schema.Types.Mixed,
    default: {},
    description: 'JSON schema or rules for validating generated content'
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  },
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
formulationBlockSchema.index({ key: 1 }, { unique: true });
formulationBlockSchema.index({ is_active: 1 });
formulationBlockSchema.index({ block_type: 1 });
formulationBlockSchema.index({ display_order: 1 });

// Static methods
formulationBlockSchema.statics.findActive = function(options = {}) {
  return this.find({ is_active: true, ...options }).sort({ display_order: 1 });
};

formulationBlockSchema.statics.findByType = function(blockType) {
  return this.find({ block_type: blockType, is_active: true }).sort({ display_order: 1 });
};

formulationBlockSchema.statics.findByKey = function(key) {
  return this.findOne({ key: key });
};

formulationBlockSchema.statics.getOrderedBlocks = function(blockKeys = []) {
  const query = blockKeys.length > 0 ? { key: { $in: blockKeys }, is_active: true } : { is_active: true };
  return this.find(query).sort({ display_order: 1 });
};

// Instance methods
formulationBlockSchema.methods.activate = function() {
  this.is_active = true;
  return this.save();
};

formulationBlockSchema.methods.deactivate = function() {
  this.is_active = false;
  return this.save();
};

formulationBlockSchema.methods.updateOrder = function(newOrder) {
  this.display_order = newOrder;
  return this.save();
};

module.exports = mongoose.model('FormulationBlock', formulationBlockSchema);