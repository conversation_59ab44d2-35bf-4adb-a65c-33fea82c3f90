const mongoose = require('mongoose');

const formulationBindingSchema = new mongoose.Schema({
  level: {
    type: String,
    required: true,
    enum: ['industry', 'category', 'sub_category'],
    index: true
  },
  slug: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
    index: true
  },
  block_key: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
    ref: 'FormulationBlock'
  },
  is_included: {
    type: Boolean,
    default: true
  },
  is_required: {
    type: Boolean,
    default: false
  },
  display_order: {
    type: Number,
    default: 0
  },
  custom_rules: {
    type: String,
    trim: true,
    maxlength: 1000,
    default: ''
  },
  override_schema: {
    type: mongoose.Schema.Types.Mixed,
    default: null
  },
  is_active: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  },
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Compound indexes
formulationBindingSchema.index({ level: 1, slug: 1 });
formulationBindingSchema.index({ level: 1, slug: 1, block_key: 1 }, { unique: true });
formulationBindingSchema.index({ block_key: 1 });
formulationBindingSchema.index({ is_active: 1 });
formulationBindingSchema.index({ is_included: 1 });

// Static methods
formulationBindingSchema.statics.findByTaxonomy = function(level, slug, options = {}) {
  return this.find({ 
    level, 
    slug, 
    is_active: true,
    ...options 
  }).sort({ display_order: 1, block_key: 1 });
};

formulationBindingSchema.statics.findIncludedBlocks = function(level, slug) {
  return this.find({
    level: level,
    slug: slug,
    is_included: true,
    is_active: true
  }).sort({ display_order: 1 });
};

formulationBindingSchema.statics.findByBlock = function(blockKey) {
  return this.find({
    block_key: blockKey,
    is_active: true
  });
};

formulationBindingSchema.statics.getBlocksForTaxonomy = async function(level, slug) {
  return await this.aggregate([
    {
      $match: {
        level,
        slug,
        is_active: true,
        is_included: true
      }
    },
    {
      $lookup: {
        from: 'formulationblocks',
        localField: 'block_key',
        foreignField: 'key',
        as: 'block'
      }
    },
    {
      $unwind: '$block'
    },
    {
      $match: {
        'block.is_active': true
      }
    },
    {
      $sort: {
        display_order: 1,
        'block.display_order': 1,
        'block.name': 1
      }
    },
    {
      $project: {
        block_key: 1,
        is_required: 1,
        custom_rules: 1,
        override_schema: 1,
        display_order: 1,
        block: 1
      }
    }
  ]);
};

// Instance methods
formulationBindingSchema.methods.include = function() {
  this.is_included = true;
  return this.save();
};

formulationBindingSchema.methods.exclude = function() {
  this.is_included = false;
  return this.save();
};

formulationBindingSchema.methods.makeRequired = function() {
  this.is_required = true;
  return this.save();
};

formulationBindingSchema.methods.makeOptional = function() {
  this.is_required = false;
  return this.save();
};

module.exports = mongoose.model('FormulationBinding', formulationBindingSchema);