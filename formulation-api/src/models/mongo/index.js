const { mongoConnection } = require('../../config/mongodb');

// Import models
const User = require('./User');
const Project = require('./Project');
const Session = require('./Session');
const FormulationBlock = require('./FormulationBlock');
const FormulationBinding = require('./FormulationBinding');

// Set up relationships
Project.schema.add({
  user: {
    type: require('mongoose').Schema.Types.ObjectId,
    ref: 'User'
  }
});

// Export models and connection
module.exports = {
  mongoConnection,
  User,
  Project,
  Session,
  FormulationBlock,
  FormulationBinding,
  
  // Helper functions
  async connect(environment = 'development') {
    return await mongoConnection.connect(environment);
  },
  
  async disconnect() {
    return await mongoConnection.disconnect();
  },
  
  async ping() {
    return await mongoConnection.ping();
  },
  
  isConnected() {
    return mongoConnection.isConnectionReady();
  }
};