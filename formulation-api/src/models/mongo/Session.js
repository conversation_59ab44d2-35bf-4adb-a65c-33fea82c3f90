const mongoose = require('mongoose');

const sessionSchema = new mongoose.Schema({
  session_id: {
    type: String,
    required: true,
    unique: true
  },
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  data: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  expires_at: {
    type: Date,
    required: true,
    index: { expireAfterSeconds: 0 } // TTL index for automatic cleanup
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  },
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
// Note: session_id already has unique: true which creates an index automatically
sessionSchema.index({ user_id: 1 });
// Note: expires_at has TTL index in field definition, but we keep it for TTL functionality

// Static methods

sessionSchema.statics.findBySessionId = function(sessionId) {
  return this.findOne({ 
    session_id: sessionId,
    expires_at: { $gt: new Date() }
  }).populate('user_id', '-password_hash -email_verification_token -password_reset_token');
};

sessionSchema.statics.updateSession = async function(sessionId, data, expiresIn = 24 * 60 * 60 * 1000) {
  const expiresAt = new Date(Date.now() + expiresIn);
  
  return await this.findOneAndUpdate(
    { session_id: sessionId },
    { 
      data,
      expires_at: expiresAt,
      updated_at: new Date()
    },
    { new: true }
  );
};

sessionSchema.statics.deleteSession = function(sessionId) {
  return this.deleteOne({ session_id: sessionId });
};

sessionSchema.statics.deleteUserSessions = function(userId) {
  return this.deleteMany({ user_id: new mongoose.Types.ObjectId(userId) });
};

sessionSchema.statics.createSession = async function(userId, ipAddress, userAgent) {
  const crypto = require('crypto');
  
  const session = new this({
    session_id: crypto.randomUUID(),
    user_id: userId,
    data: {
      ip_address: ipAddress,
      user_agent: userAgent,
      created_at: new Date()
    },
    expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
  });
  
  return await session.save();
};

sessionSchema.statics.cleanupExpiredSessions = function() {
  return this.deleteMany({ expires_at: { $lte: new Date() } });
};

// Instance methods
sessionSchema.methods.extend = function(expiresIn = 24 * 60 * 60 * 1000) {
  this.expires_at = new Date(Date.now() + expiresIn);
  return this.save();
};

sessionSchema.methods.updateData = function(newData) {
  this.data = { ...this.data, ...newData };
  return this.save();
};

sessionSchema.methods.isExpired = function() {
  return this.expires_at <= new Date();
};

module.exports = mongoose.model('Session', sessionSchema);