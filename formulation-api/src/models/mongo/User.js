const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^[^\s@]+@[^\s@]+\.[^\s@]+$/, 'Please enter a valid email address']
  },
  password_hash: {
    type: String,
    required: true,
    minlength: 60 // bcrypt hash length
  },
  first_name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  last_name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  company: {
    type: String,
    trim: true,
    maxlength: 100
  },
  role: {
    type: String,
    enum: ['user', 'admin', 'formulator', 'researcher'],
    default: 'user'
  },
  is_active: {
    type: Boolean,
    default: true
  },
  email_verified: {
    type: Boolean,
    default: false
  },
  email_verification_token: {
    type: String
  },
  email_verification_expires: {
    type: Date
  },
  password_reset_token: {
    type: String
  },
  password_reset_expires: {
    type: Date
  },
  last_login: {
    type: Date
  },
  preferences: {
    default_industry: {
      type: String,
      enum: ['beverages', 'nutraceuticals', 'cosmetics']
    },
    notification_settings: {
      email_notifications: {
        type: Boolean,
        default: true
      },
      formulation_updates: {
        type: Boolean,
        default: true
      },
      project_reminders: {
        type: Boolean,
        default: false
      }
    },
    ui_preferences: {
      theme: {
        type: String,
        enum: ['light', 'dark'],
        default: 'light'
      },
      dashboard_layout: {
        type: String,
        enum: ['grid', 'list'],
        default: 'grid'
      }
    }
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  },
  toJSON: {
    transform: function(doc, ret) {
      delete ret.password_hash;
      delete ret.email_verification_token;
      delete ret.password_reset_token;
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
// Note: email already has unique: true which creates an index automatically
userSchema.index({ created_at: -1 });
userSchema.index({ last_login: -1 });
userSchema.index({ email_verification_token: 1 }, { sparse: true });
userSchema.index({ password_reset_token: 1 }, { sparse: true });

// Virtual for full name
userSchema.virtual('full_name').get(function() {
  return `${this.first_name} ${this.last_name}`.trim();
});

// Instance methods
userSchema.methods.comparePassword = async function(candidatePassword) {
  if (!candidatePassword || !this.password_hash) {
    return false;
  }
  return await bcrypt.compare(candidatePassword, this.password_hash);
};

userSchema.methods.updateLastLogin = async function() {
  this.last_login = new Date();
  return await this.save();
};

userSchema.methods.generatePasswordResetToken = function() {
  const crypto = require('crypto');
  const token = crypto.randomBytes(32).toString('hex');
  this.password_reset_token = token;
  this.password_reset_expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
  return token;
};

userSchema.methods.generateEmailVerificationToken = function() {
  const crypto = require('crypto');
  const token = crypto.randomBytes(32).toString('hex');
  this.email_verification_token = token;
  this.email_verification_expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
  return token;
};

userSchema.methods.toSafeObject = function() {
  const userObject = this.toObject();
  delete userObject.password_hash;
  delete userObject.email_verification_token;
  delete userObject.password_reset_token;
  return userObject;
};

// Static methods
userSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase() });
};

userSchema.statics.findActiveUsers = function(options = {}) {
  const { limit = 20, skip = 0, sortBy = 'created_at', sortOrder = -1 } = options;
  
  return this.find({ is_active: true })
    .select('-password_hash -email_verification_token -password_reset_token')
    .limit(limit)
    .skip(skip)
    .sort({ [sortBy]: sortOrder });
};

userSchema.statics.createUser = async function(userData) {
  const {
    email,
    password,
    first_name,
    last_name,
    company,
    role = 'user'
  } = userData;

  // Check if user already exists
  const existingUser = await this.findByEmail(email);
  if (existingUser) {
    throw new Error('User with this email already exists');
  }

  // Hash password
  const saltRounds = 12;
  const password_hash = await bcrypt.hash(password, saltRounds);

  // Create user
  const user = new this({
    email: email.toLowerCase(),
    password_hash,
    first_name,
    last_name,
    company,
    role
  });

  return await user.save();
};

// Pre-save middleware
userSchema.pre('save', function(next) {
  if (this.isModified('email')) {
    this.email = this.email.toLowerCase();
  }
  next();
});

// Pre-remove middleware (cleanup related data)
userSchema.pre('deleteOne', { document: true, query: false }, async function() {
  const Project = require('./Project');
  
  // Delete all user's projects
  await Project.deleteMany({ user_id: this._id });
});

// Static methods
userSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase() });
};

userSchema.statics.findByEmailWithAuth = function(email) {
  return this.findOne({ 
    email: email.toLowerCase(),
    is_active: true 
  });
};

// Instance methods
userSchema.methods.validatePassword = async function(password) {
  const bcrypt = require('bcrypt');
  return await bcrypt.compare(password, this.password_hash);
};

userSchema.methods.updateLastLogin = async function() {
  this.last_login = new Date();
  this.updated_at = new Date();
  return await this.save();
};

module.exports = mongoose.model('User', userSchema);