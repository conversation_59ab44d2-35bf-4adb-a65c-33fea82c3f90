const mongoose = require('mongoose');

// Sub-schemas for complex nested objects
const ingredientSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  percentage: {
    type: Number,
    required: true,
    min: 0.1,
    max: 95
  },
  function: {
    type: String,
    trim: true
  },
  cost_per_kg: {
    type: Number,
    min: 1,
    max: 50000
  },
  supplier_suggestions: [{
    name: String,
    location: String,
    quality_grade: {
      type: String,
      enum: ['Food Grade', 'Pharma Grade', 'Cosmetic Grade', 'Organic Certified']
    },
    certification: String,
    lead_time_days: {
      type: Number,
      min: 1,
      max: 90
    }
  }],
  sourcing_region: String,
  alternative_names: [String],
  cas_number: String,
  nutritional_contribution: String
}, { _id: false });

const nutritionalProfileSchema = new mongoose.Schema({
  macronutrients: {
    protein_g: { type: Number, min: 0 },
    carbohydrates_g: { type: Number, min: 0 },
    fat_g: { type: Number, min: 0 },
    fiber_g: { type: Number, min: 0 },
    calories_per_serving: { type: Number, min: 0 }
  },
  micronutrients: {
    vitamin_c_mg: { type: Number, min: 0 },
    vitamin_d_iu: { type: Number, min: 0 },
    calcium_mg: { type: Number, min: 0 },
    iron_mg: { type: Number, min: 0 },
    magnesium_mg: { type: Number, min: 0 }
  },
  bioactive_compounds: {
    antioxidants_orac: { type: Number, min: 0 },
    polyphenols_mg: { type: Number, min: 0 },
    omega3_mg: { type: Number, min: 0 }
  },
  daily_value_percentages: {
    vitamin_c: { type: Number, min: 0, max: 1000 },
    vitamin_d: { type: Number, min: 0, max: 1000 },
    calcium: { type: Number, min: 0, max: 1000 },
    iron: { type: Number, min: 0, max: 1000 }
  }
}, { _id: false });

const costAnalysisSchema = new mongoose.Schema({
  raw_material_cost: { type: Number, min: 10 },
  processing_cost: { type: Number, min: 5 },
  packaging_cost: { type: Number, min: 2 },
  total_cogs: { type: Number, min: 20 },
  suggested_retail: { type: Number, min: 50 },
  margin_percentage: { type: Number, min: 20, max: 90 },
  break_even_volume: { type: Number, min: 1000 },
  cost_breakdown: {
    active_ingredients: { type: Number, min: 0, max: 100 },
    excipients: { type: Number, min: 0, max: 100 },
    processing: { type: Number, min: 0, max: 100 },
    packaging: { type: Number, min: 0, max: 100 }
  }
}, { _id: false });

const scoresSchema = new mongoose.Schema({
  nutrition: { type: Number, min: 0, max: 100 },
  sustainability: { type: Number, min: 0, max: 100 },
  cost_efficiency: { type: Number, min: 0, max: 100 },
  compliance: { type: Number, min: 0, max: 100 },
  market_appeal: { type: Number, min: 0, max: 100 },
  innovation_index: { type: Number, min: 0, max: 100 },
  scalability: { type: Number, min: 0, max: 100 }
}, { _id: false });

const recipeSchema = new mongoose.Schema({
  id: String,
  name: {
    type: String,
    required: true,
    minlength: 5,
    maxlength: 100
  },
  variation: {
    type: String,
    default: 'standard'
  },
  description: {
    type: String,
    minlength: 50,
    maxlength: 500
  },
  target_market: String,
  ingredients: [ingredientSchema],
  nutritional_profile: nutritionalProfileSchema,
  production_specs: {
    batch_size: String,
    shelf_life: String,
    storage_conditions: String,
    manufacturing_process: String,
    quality_control: String,
    packaging_requirements: String
  },
  compliance_details: {
    regulatory_status: {
      fda: String,
      fssai: String,
      eu: String,
      organic: String
    },
    certifications_needed: [String],
    labeling_requirements: String,
    health_claims: String,
    allergen_warnings: String
  },
  cost_analysis: costAnalysisSchema,
  scores: scoresSchema,
  sustainability_metrics: {
    carbon_footprint_kg: { type: Number, min: 0 },
    water_usage_liters: { type: Number, min: 0 },
    renewable_energy_percentage: { type: Number, min: 0, max: 100 },
    sustainable_sourcing_percentage: { type: Number, min: 0, max: 100 },
    packaging_recyclability: { type: Number, min: 0, max: 100 },
    biodegradability_score: { type: Number, min: 0, max: 100 }
  }
}, { _id: false });

const variationSchema = new mongoose.Schema({
  id: String,
  name: {
    type: String,
    required: true,
    minlength: 5,
    maxlength: 100
  },
  variation_type: String,
  target_geography: String,
  key_differences: {
    type: String,
    minlength: 20
  },
  ingredients: [ingredientSchema],
  traditional_benefits: String,
  innovation_highlights: String,
  cost_analysis: {
    raw_material_cost: { type: Number, min: 10 },
    processing_cost: { type: Number, min: 5 },
    packaging_cost: { type: Number, min: 2 },
    total_cogs: { type: Number, min: 20 },
    suggested_retail: { type: Number, min: 50 },
    margin_percentage: { type: Number, min: 20, max: 90 }
  },
  scores: scoresSchema
}, { _id: false });

// Main Project Schema
const projectSchema = new mongoose.Schema({
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 255
  },
  description: {
    type: String,
    trim: true
  },
  industry: {
    type: String,
    required: true,
    trim: true
  },
  product_type: {
    type: String,
    trim: true
  },
  custom_product_type: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['queued', 'generating', 'ready', 'failed', 'closed', 'archived'],
    default: 'queued'
  },
  generation_job_id: { type: mongoose.Schema.Types.ObjectId, index: true },
  generation_started_at: Date,
  generation_completed_at: Date,
  generation_failed_at: Date,
  failure_reason: String,
  
  // Wizard Configuration Data
  goals: {
    budget: Number,
    nutrition_targets: mongoose.Schema.Types.Mixed,
    sustainability: String,
    compliance: String
  },
  constraints: {
    allergies: [String],
    certifications_needed: [String],
    regulatory_requirements: mongoose.Schema.Types.Mixed
  },
  branding: {
    colors: mongoose.Schema.Types.Mixed,
    personas: [mongoose.Schema.Types.Mixed],
    values: [String]
  },
  gtm_strategy: {
    channels: [String],
    marketing_focus: [String],
    launch_timeline: mongoose.Schema.Types.Mixed
  },
  
  // Current Formulation (embedded for performance)
  current_formulation: {
    version: {
      type: String,
      default: '1.0'
    },
    active_version: {
      type: String,
      default: '1.0'
    },
    generated_at: {
      type: Date,
      default: Date.now
    },
    quality_score: {
      type: Number,
      min: 0,
      max: 100
    },
    quality_level: {
      type: String,
      enum: ['excellent', 'good', 'acceptable', 'needs_optimization']
    },
    ready_for_results: {
      type: Boolean,
      default: false
    },
    
    // All recipes (consolidated from old recipes + variations)
    recipes: {
      type: [recipeSchema],
      validate: {
        validator: function(v) {
          // Allow up to 10 recipes
          return v.length <= 10;
        },
        message: 'Maximum 10 recipes allowed'
      }
    },
    
    // Legacy field - kept for backward compatibility during migration
    variations: {
      type: [variationSchema],
      default: undefined
    },
    
    // Market Analysis
    market_analysis: {
      target_segments: [{
        segment: String,
        size_percentage: { type: Number, min: 1, max: 100 },
        willingness_to_pay: String,
        key_motivators: [String]
      }],
      competitive_landscape: [{
        competitor: String,
        price_point: String,
        key_differentiator: String
      }],
      market_opportunity: {
        tam_size_crores: { type: Number, min: 100 },
        growth_rate_cagr: { type: Number, min: 0, max: 50 },
        entry_barriers: String
      }
    },
    
    // Regulatory Pathway
    regulatory_pathway: {
      approval_timeline: String,
      required_studies: [String],
      regulatory_costs: String,
      key_milestones: [String]
    },
    
    // Manufacturing Recommendations
    manufacturing_recommendations: {
      preferred_locations: [String],
      equipment_requirements: String,
      quality_standards: String,
      capacity_planning: String
    },
    
    // Validation Errors
    validation_errors: [{
      field: String,
      message: String
    }],
    
    // Conversation History (playground chat)
    conversation_history: [{
      role: {
        type: String,
        enum: ['user', 'assistant'],
        required: true
      },
      message: {
        type: String,
        required: true
      },
      action: String, // modify, fix, rethink, etc.
      timestamp: {
        type: Date,
        default: Date.now
      },
      changes: [String], // List of changes made
      version: String // Which formulation version this applies to
    }]
  },
  
  // Version History
  formulation_versions: [{
    version: {
      type: String,
      required: true
    },
    formulation_data: mongoose.Schema.Types.Mixed, // Snapshot of formulation
    created_at: {
      type: Date,
      default: Date.now
    },
    changes_summary: String,
    parent_version: String,
    diff: [{
      type: {
        type: String
      },
      path: String,
      oldValue: mongoose.Schema.Types.Mixed,
      newValue: mongoose.Schema.Types.Mixed,
      description: String
    }],
    template_used: String,
    confidence_score: {
      type: Number,
      min: 0,
      max: 10
    }
  }],
  
  // Metadata
  is_active: {
    type: Boolean,
    default: true
  },
  last_accessed: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  },
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
projectSchema.index({ user_id: 1, created_at: -1 });
projectSchema.index({ status: 1 });
projectSchema.index({ industry: 1 });
projectSchema.index({ 'current_formulation.quality_score': -1 });
projectSchema.index({ 'current_formulation.ready_for_results': 1 });
projectSchema.index({ last_accessed: -1 });

// Virtual for formulation count
projectSchema.virtual('formulation_count').get(function() {
  return this.formulation_versions.length;
});

// Instance methods
projectSchema.methods.updateLastAccessed = function() {
  this.last_accessed = new Date();
  return this.save();
};

projectSchema.methods.addConversationMessage = function(role, message, action = null, changes = []) {
  const conversationEntry = {
    role,
    message,
    action,
    changes,
    version: this.current_formulation.version || 1,
    timestamp: new Date()
  };
  
  this.conversation_history.push(conversationEntry);
  return this.save();
};

projectSchema.methods.createFormulationVersion = function(formulationData, changesSummary = '') {
  const version = this.formulation_versions.length + 1;
  
  const versionEntry = {
    version,
    formulation_data: JSON.parse(JSON.stringify(formulationData)),
    changes_summary: changesSummary,
    created_at: new Date()
  };
  
  this.formulation_versions.push(versionEntry);
  
  // Update current formulation
  this.current_formulation = {
    ...formulationData,
    version,
    generated_at: new Date()
  };
  
  return this.save();
};

projectSchema.methods.getFormulationVersion = function(version) {
  return this.formulation_versions.find(v => v.version === version);
};

projectSchema.methods.rollbackToVersion = function(version) {
  const targetVersion = this.getFormulationVersion(version);
  if (!targetVersion) {
    throw new Error(`Version ${version} not found`);
  }
  
  // Create new version from rolled back data
  const newVersion = this.formulation_versions.length + 1;
  this.current_formulation = {
    ...targetVersion.formulation_data,
    version: newVersion,
    generated_at: new Date()
  };
  
  // Add version entry
  this.formulation_versions.push({
    version: newVersion,
    formulation_data: JSON.parse(JSON.stringify(targetVersion.formulation_data)),
    changes_summary: `Rolled back from version ${version}`,
    created_at: new Date()
  });
  
  return this.save();
};

// Static methods
projectSchema.statics.findUserProjects = function(userId, options = {}) {
  const {
    status,
    industry,
    limit = 20,
    skip = 0,
    sortBy = 'updated_at',
    sortOrder = -1,
    includeArchived = false
  } = options;

  const query = { 
    user_id: new mongoose.Types.ObjectId(userId),
    is_active: includeArchived ? undefined : true
  };
  
  if (status) query.status = status;
  if (industry) query.industry = industry;

  return this.find(query)
    .populate('user_id', 'first_name last_name email')
    .limit(limit)
    .skip(skip)
    .sort({ [sortBy]: sortOrder });
};

projectSchema.statics.createProject = async function(userId, projectData) {
  const {
    name,
    description,
    industry,
    product_type,
    custom_product_type,
    goals,
    constraints,
    branding,
    gtm_strategy
  } = projectData;

  const project = new this({
    user_id: new mongoose.Types.ObjectId(userId),
    name,
    description,
    industry,
    product_type,
    custom_product_type,
    goals,
    constraints,
    branding,
    gtm_strategy,
    status: 'draft'
  });

  return await project.save();
};

projectSchema.statics.findWithFormulations = function(userId, options = {}) {
  const query = { 
    user_id: new mongoose.Types.ObjectId(userId),
    is_active: true,
    'current_formulation.recipes': { $exists: true, $ne: [] }
  };
  
  return this.find(query)
    .select('name description industry status current_formulation.quality_score current_formulation.ready_for_results formulation_versions')
    .sort({ updated_at: -1 })
    .limit(options.limit || 10);
};

// Pre-save middleware
projectSchema.pre('save', function(next) {
  if (this.isModified() && !this.isModified('last_accessed')) {
    this.updated_at = new Date();
  }
  next();
});

module.exports = mongoose.model('Project', projectSchema);
