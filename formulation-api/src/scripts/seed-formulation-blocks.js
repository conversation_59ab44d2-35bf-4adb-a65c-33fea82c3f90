const { connect } = require('../models');
const FormulationBlock = require('../models/mongo/FormulationBlock');
const FormulationBinding = require('../models/mongo/FormulationBinding');
const logger = require('../utils/logger');

// Default formulation blocks based on existing schema
const defaultBlocks = [
  {
    key: 'ingredients',
    name: 'Ingredients',
    description: 'Complete ingredient composition including base ingredients, actives, preservatives, and additives',
    rules: 'Required: true, Validation: ingredient_safety, regulatory_compliance, allergen_declaration',
    schema_path: '/formulation/ingredients',
    is_required: true,
    category: 'core',
    display_order: 1
  },
  {
    key: 'cost_analysis',
    name: 'Cost Analysis',
    description: 'Comprehensive cost breakdown including raw materials, manufacturing, packaging, and overhead costs',
    rules: 'Required: true, Validation: cost_accuracy, margin_targets, competitive_pricing',
    schema_path: '/formulation/cost_analysis',
    is_required: true,
    category: 'core',
    display_order: 2
  },
  {
    key: 'nutritional_profile',
    name: 'Nutritional Profile',
    description: 'Complete nutritional information including macronutrients, micronutrients, and health claims',
    rules: 'Required: true, Validation: nutritional_accuracy, health_claims_substantiation, labeling_compliance',
    schema_path: '/formulation/nutritional_profile',
    is_required: true,
    category: 'core',
    display_order: 3
  },
  {
    key: 'compliance',
    name: 'Compliance',
    description: 'Regulatory compliance including safety assessments, certifications, and legal requirements',
    rules: 'Required: true, Validation: regulatory_approval, safety_documentation, certification_status',
    schema_path: '/formulation/compliance',
    is_required: true,
    category: 'core',
    display_order: 4
  },
  {
    key: 'manufacturing',
    name: 'Manufacturing',
    description: 'Manufacturing specifications including process parameters, equipment requirements, and quality control',
    rules: 'Required: true, Validation: process_feasibility, equipment_compatibility, quality_standards',
    schema_path: '/formulation/manufacturing',
    is_required: true,
    category: 'core',
    display_order: 5
  },
  {
    key: 'sustainability',
    name: 'Sustainability',
    description: 'Environmental impact assessment including carbon footprint, packaging sustainability, and sourcing ethics',
    rules: 'Required: false, Validation: environmental_impact, sustainable_sourcing, lifecycle_assessment',
    schema_path: '/formulation/sustainability',
    is_required: false,
    category: 'optional',
    display_order: 6
  },
  {
    key: 'packaging',
    name: 'Packaging',
    description: 'Packaging specifications including materials, design, labeling, and shelf-life considerations',
    rules: 'Required: true, Validation: packaging_compatibility, shelf_life_testing, labeling_accuracy',
    schema_path: '/formulation/packaging',
    is_required: true,
    category: 'core',
    display_order: 7
  }
];

// Default bindings for food industry
const defaultBindings = [
  {
    industry: 'food',
    category: 'beverages',
    sub_category: 'functional_drinks',
    blockKeys: ['ingredients', 'cost_analysis', 'nutritional_profile', 'compliance', 'manufacturing', 'packaging']
  },
  {
    industry: 'food',
    category: 'dairy',
    sub_category: 'yogurt',
    blockKeys: ['ingredients', 'cost_analysis', 'nutritional_profile', 'compliance', 'manufacturing', 'packaging']
  },
  {
    industry: 'food',
    category: 'snacks',
    sub_category: 'protein_bars',
    blockKeys: ['ingredients', 'cost_analysis', 'nutritional_profile', 'compliance', 'manufacturing', 'packaging']
  },
  {
    industry: 'food',
    category: 'supplements',
    sub_category: 'powder',
    blockKeys: ['ingredients', 'cost_analysis', 'nutritional_profile', 'compliance', 'manufacturing', 'packaging']
  },
  {
    industry: 'food',
    category: 'bakery',
    sub_category: 'bread',
    blockKeys: ['ingredients', 'cost_analysis', 'nutritional_profile', 'compliance', 'manufacturing', 'packaging']
  },
  {
    industry: 'cosmetics',
    category: 'skincare',
    sub_category: 'moisturizer',
    blockKeys: ['ingredients', 'cost_analysis', 'compliance', 'manufacturing', 'sustainability', 'packaging']
  },
  {
    industry: 'nutraceuticals',
    category: 'supplements',
    sub_category: 'capsules',
    blockKeys: ['ingredients', 'cost_analysis', 'nutritional_profile', 'compliance', 'manufacturing', 'packaging']
  }
];

async function seedFormulationBlocks() {
  try {
    logger.info('Starting formulation blocks seeding...');
    
    // Connect to database
    await connect('development');
    logger.info('Connected to database');
    
    // Clear existing data
    await FormulationBlock.deleteMany({});
    await FormulationBinding.deleteMany({});
    logger.info('Cleared existing formulation blocks and bindings');
    
    // Insert blocks
    const insertedBlocks = await FormulationBlock.insertMany(defaultBlocks);
    logger.info(`Inserted ${insertedBlocks.length} formulation blocks`);
    
    // Create block key to ID mapping
    const blockMap = {};
    insertedBlocks.forEach(block => {
      blockMap[block.key] = block._id;
    });
    
    // Create individual binding records for each block-taxonomy combination
     const bindingsToInsert = [];
     defaultBindings.forEach(binding => {
       binding.blockKeys.forEach((blockKey, index) => {
         if (blockMap[blockKey]) {
           bindingsToInsert.push({
             level: 'sub_category',
             slug: `${binding.industry}_${binding.category}_${binding.sub_category}`,
             block_key: blockKey,
             is_included: true,
             is_required: ['base_ingredients', 'active_compounds', 'preservatives'].includes(blockKey),
             display_order: index + 1
           });
         }
       });
     });
    
    const insertedBindings = await FormulationBinding.insertMany(bindingsToInsert);
    logger.info(`Inserted ${insertedBindings.length} formulation bindings`);
    
    logger.info('✅ Formulation blocks seeding completed successfully!');
    
    // Display summary
    console.log('\n=== SEEDING SUMMARY ===');
    console.log(`Blocks created: ${insertedBlocks.length}`);
    console.log(`Bindings created: ${insertedBindings.length}`);
    console.log('\nBlocks:');
    insertedBlocks.forEach(block => {
      console.log(`  - ${block.name}: ${block.description}`);
    });
    console.log('\nBindings:');
    const bindingsBySlug = {};
    insertedBindings.forEach(binding => {
      if (!bindingsBySlug[binding.slug]) {
        bindingsBySlug[binding.slug] = [];
      }
      bindingsBySlug[binding.slug].push(binding.block_key);
    });
    Object.entries(bindingsBySlug).forEach(([slug, blockKeys]) => {
      console.log(`  - ${slug}: ${blockKeys.length} blocks (${blockKeys.join(', ')})`);
    });
    
    process.exit(0);
    
  } catch (error) {
    logger.error('Error seeding formulation blocks:', error);
    console.error('❌ Seeding failed:', error.message);
    process.exit(1);
  }
}

// Run seeding if called directly
if (require.main === module) {
  seedFormulationBlocks();
}

module.exports = { seedFormulationBlocks, defaultBlocks, defaultBindings };