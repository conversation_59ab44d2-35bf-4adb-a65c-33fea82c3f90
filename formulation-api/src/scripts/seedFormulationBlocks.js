const mongoose = require('mongoose');
const { connect } = require('../models');
const FormulationBlock = require('../models/mongo/FormulationBlock');
const FormulationBinding = require('../models/mongo/FormulationBinding');
const logger = require('../utils/logger');

// Default formulation blocks based on existing schema
const defaultBlocks = [
  {
    key: 'nutritional_profile',
    name: 'Nutritional Profile',
    description: 'Complete nutritional breakdown including macronutrients, micronutrients, and bioactive compounds',
    rules: 'Must include calories, protein, carbs, fat, fiber, vitamins, and minerals per serving',
    schema_path: 'nutritional_profile',
    category: 'core',
    is_required: true,
    display_order: 1
  },
  {
    key: 'production_specs',
    name: 'Production Specifications',
    description: 'Manufacturing requirements including batch size, shelf life, storage conditions, and detailed procedures',
    rules: 'Must specify batch size, manufacturing process, equipment requirements, and quality control points',
    schema_path: 'production_specs',
    category: 'core',
    is_required: true,
    display_order: 2
  },
  {
    key: 'compliance_details',
    name: 'Compliance & Regulatory',
    description: 'Regulatory compliance information including FDA, FSSAI, EU approvals and certifications',
    rules: 'Must include regulatory status for target markets and required certifications',
    schema_path: 'compliance_details',
    category: 'compliance',
    is_required: true,
    display_order: 3
  },
  {
    key: 'cost_analysis',
    name: 'Cost Analysis',
    description: 'Comprehensive cost breakdown including raw materials, processing, packaging, and margins',
    rules: 'Must provide detailed COGS calculation and suggested retail pricing',
    schema_path: 'cost_analysis',
    category: 'analysis',
    is_required: true,
    display_order: 4
  },
  {
    key: 'scores',
    name: 'Performance Scores',
    description: 'Quantitative scoring for nutrition, sustainability, cost efficiency, compliance, and market appeal',
    rules: 'Scores should be 0-100 scale with clear justification for each metric',
    schema_path: 'scores',
    category: 'analysis',
    is_required: true,
    display_order: 5
  },
  {
    key: 'supplier_suggestions',
    name: 'Supplier Recommendations',
    description: 'Detailed supplier information for each ingredient including location, certifications, and lead times',
    rules: 'Provide at least 2 suppliers per ingredient with Indian suppliers preferred',
    schema_path: 'ingredients.supplier_suggestions',
    category: 'optional',
    is_required: false,
    display_order: 6
  },
  {
    key: 'bom_details',
    name: 'Bill of Materials',
    description: 'Detailed BOM with quantities, storage requirements, and quality grades for each ingredient',
    rules: 'Include quantity per batch, lead times, and storage specifications',
    schema_path: 'ingredients.bom_details',
    category: 'optional',
    is_required: false,
    display_order: 7
  },
  {
    key: 'bioactive_compounds',
    name: 'Bioactive Compounds',
    description: 'Analysis of functional compounds like antioxidants, polyphenols, and omega fatty acids',
    rules: 'Include ORAC values, polyphenol content, and other bioactive measurements where applicable',
    schema_path: 'nutritional_profile.bioactive_compounds',
    category: 'analysis',
    is_required: false,
    display_order: 8
  },
  {
    key: 'manufacturing_procedure',
    name: 'Detailed Manufacturing Procedure',
    description: 'Step-by-step manufacturing process with timing, equipment, and quality control checkpoints',
    rules: 'Include pre-processing, mixing, quality control, and packaging steps with time estimates',
    schema_path: 'production_specs.detailed_manufacturing_procedure',
    category: 'core',
    is_required: false,
    display_order: 9
  },
  {
    key: 'daily_value_percentages',
    name: 'Daily Value Percentages',
    description: 'Percentage of daily recommended values for key nutrients per serving',
    rules: 'Calculate based on FDA/FSSAI daily value recommendations',
    schema_path: 'nutritional_profile.daily_value_percentages',
    category: 'compliance',
    is_required: false,
    display_order: 10
  }
];

// Default bindings for different taxonomy levels
const defaultBindings = [
  // Industry level bindings (apply to all)
  { level: 'industry', slug: 'beverages', block_key: 'nutritional_profile', is_included: true, is_required: true },
  { level: 'industry', slug: 'beverages', block_key: 'production_specs', is_included: true, is_required: true },
  { level: 'industry', slug: 'beverages', block_key: 'compliance_details', is_included: true, is_required: true },
  { level: 'industry', slug: 'beverages', block_key: 'cost_analysis', is_included: true, is_required: true },
  { level: 'industry', slug: 'beverages', block_key: 'scores', is_included: true, is_required: true },
  { level: 'industry', slug: 'beverages', block_key: 'bioactive_compounds', is_included: true, is_required: false },
  
  { level: 'industry', slug: 'nutraceuticals', block_key: 'nutritional_profile', is_included: true, is_required: true },
  { level: 'industry', slug: 'nutraceuticals', block_key: 'production_specs', is_included: true, is_required: true },
  { level: 'industry', slug: 'nutraceuticals', block_key: 'compliance_details', is_included: true, is_required: true },
  { level: 'industry', slug: 'nutraceuticals', block_key: 'cost_analysis', is_included: true, is_required: true },
  { level: 'industry', slug: 'nutraceuticals', block_key: 'scores', is_included: true, is_required: true },
  { level: 'industry', slug: 'nutraceuticals', block_key: 'bioactive_compounds', is_included: true, is_required: true },
  { level: 'industry', slug: 'nutraceuticals', block_key: 'daily_value_percentages', is_included: true, is_required: true },
  
  { level: 'industry', slug: 'cosmetics', block_key: 'production_specs', is_included: true, is_required: true },
  { level: 'industry', slug: 'cosmetics', block_key: 'compliance_details', is_included: true, is_required: true },
  { level: 'industry', slug: 'cosmetics', block_key: 'cost_analysis', is_included: true, is_required: true },
  { level: 'industry', slug: 'cosmetics', block_key: 'scores', is_included: true, is_required: true },
  
  // Category level bindings (more specific)
  { level: 'category', slug: 'functional-beverages', block_key: 'bioactive_compounds', is_included: true, is_required: true },
  { level: 'category', slug: 'functional-beverages', block_key: 'manufacturing_procedure', is_included: true, is_required: false },

  { level: 'category', slug: 'energy-drinks', block_key: 'bioactive_compounds', is_included: true, is_required: true },
  { level: 'category', slug: 'energy-drinks', block_key: 'daily_value_percentages', is_included: true, is_required: false },

  { level: 'category', slug: 'dietary-supplements', block_key: 'daily_value_percentages', is_included: true, is_required: true },
  { level: 'category', slug: 'dietary-supplements', block_key: 'manufacturing_procedure', is_included: true, is_required: true },

  { level: 'category', slug: 'skincare', block_key: 'manufacturing_procedure', is_included: true, is_required: true },
  { level: 'category', slug: 'skincare', block_key: 'supplier_suggestions', is_included: true, is_required: false }
];

async function seedFormulationBlocks() {
  try {
    logger.info('Starting formulation blocks seeding...');
    
    // Connect to database
    await connect('development');
    
    // Clear existing data
    await FormulationBlock.deleteMany({});
    await FormulationBinding.deleteMany({});
    logger.info('Cleared existing formulation blocks and bindings');
    
    // Insert blocks
    const insertedBlocks = await FormulationBlock.insertMany(defaultBlocks);
    logger.info(`Inserted ${insertedBlocks.length} formulation blocks`);
    
    // Insert bindings
    const insertedBindings = await FormulationBinding.insertMany(defaultBindings);
    logger.info(`Inserted ${insertedBindings.length} formulation bindings`);
    
    // Log summary
    const blocksByCategory = insertedBlocks.reduce((acc, block) => {
      acc[block.category] = (acc[block.category] || 0) + 1;
      return acc;
    }, {});
    
    const bindingsByLevel = insertedBindings.reduce((acc, binding) => {
      acc[binding.level] = (acc[binding.level] || 0) + 1;
      return acc;
    }, {});
    
    logger.info('Seeding completed successfully', {
      blocks: {
        total: insertedBlocks.length,
        byCategory: blocksByCategory
      },
      bindings: {
        total: insertedBindings.length,
        byLevel: bindingsByLevel
      }
    });
    
  } catch (error) {
    logger.error('Failed to seed formulation blocks', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  } finally {
    await mongoose.connection.close();
  }
}

// Run seeding if called directly
if (require.main === module) {
  seedFormulationBlocks()
    .then(() => {
      console.log('✅ Formulation blocks seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Formulation blocks seeding failed:', error.message);
      process.exit(1);
    });
}

module.exports = { seedFormulationBlocks, defaultBlocks, defaultBindings };