const logger = require('../utils/logger');

const errorHandler = (err, req, res, next) => {
  // Log the error
  logger.logError(err, {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Default error
  let error = { ...err };
  error.message = err.message;

  // MongoDB validation error
  if (err.name === 'ValidationError') {
    const messages = Object.values(err.errors).map(error => error.message);
    error = {
      statusCode: 400,
      message: `Validation Error: ${messages.join(', ')}`
    };
  }

  // MongoDB duplicate key error
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    const message = `Resource with that ${field} already exists`;
    error = {
      statusCode: 409,
      message
    };
  }

  // MongoDB cast error (invalid ObjectId)
  if (err.name === 'CastError') {
    const message = 'Invalid resource ID format';
    error = {
      statusCode: 400,
      message
    };
  }

  // MongoDB connection error
  if (err.name === 'MongoNetworkError' || err.name === 'MongoServerError') {
    const message = 'Database connection error';
    error = {
      statusCode: 500,
      message
    };
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'Invalid token';
    error = {
      statusCode: 401,
      message
    };
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Token expired';
    error = {
      statusCode: 401,
      message
    };
  }

  // Joi validation errors
  if (err.isJoi) {
    const message = err.details.map(detail => detail.message).join(', ');
    error = {
      statusCode: 400,
      message: `Validation Error: ${message}`
    };
  }

  // Bcrypt errors
  if (err.message && err.message.includes('bcrypt')) {
    error = {
      statusCode: 500,
      message: 'Authentication processing error'
    };
  }

  // Default to 500 server error
  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal Server Error';

  // Send error response
  res.status(statusCode).json({
    success: false,
    error: message,
    ...(process.env.NODE_ENV === 'development' && {
      stack: err.stack,
      details: err
    })
  });
};

module.exports = errorHandler;