const jwt = require('jsonwebtoken');
const { Session, User } = require('../models');
const logger = require('../utils/logger');

const JWT_SECRET = process.env.JWT_SECRET || 'agrizy-formulation-platform-secret-key-2025';

// Verify JWT token middleware
const verifyToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Access token required'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Verify JWT
    let decoded;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
      logger.debug('JWT decoded successfully', {
        userId: decoded.userId,
        sessionId: decoded.sessionId,
        email: decoded.email,
        iat: decoded.iat,
        exp: decoded.exp
      });
    } catch (jwtError) {
      logger.warn('Invalid JWT token', {
        token: token.substring(0, 10) + '...',
        error: jwtError.message,
        ip: req.ip
      });
      
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired token'
      });
    }

    // Find session in MongoDB by session ID from JWT
    logger.debug('Looking for session', { sessionId: decoded.sessionId });
    const session = await Session.findBySessionId(decoded.sessionId);
    logger.debug('Session lookup result', { 
      found: !!session, 
      sessionId: decoded.sessionId,
      expires: session?.expires_at 
    });
    
    if (!session) {
      logger.warn('Session not found or inactive', {
        userId: decoded.userId,
        token: token.substring(0, 10) + '...',
        ip: req.ip
      });
      
      return res.status(401).json({
        success: false,
        error: 'Session not found or expired'
      });
    }

    // Check if session is expired (MongoDB TTL will auto-remove expired sessions)
    if (session.expires_at <= new Date()) {
      logger.warn('Session expired', {
        userId: decoded.userId,
        sessionId: session._id,
        expiresAt: session.expires_at,
        ip: req.ip
      });
      
      // Remove expired session
      await Session.deleteOne({ _id: session._id });
      
      return res.status(401).json({
        success: false,
        error: 'Session expired'
      });
    }

    // Check if user is active (user is populated by findBySessionId)
    if (!session.user_id || !session.user_id.is_active) {
      logger.warn('User account inactive', {
        userId: decoded.userId,
        email: session.user_id?.email,
        ip: req.ip
      });
      
      return res.status(401).json({
        success: false,
        error: 'User account is inactive'
      });
    }

    // Update session last used
    await Session.updateOne(
      { _id: session._id },
      { 
        last_used: new Date(),
        updated_at: new Date()
      }
    );

    // Add user and session to request
    req.user = session.user_id;
    req.session = session;
    
    logger.debug('Authentication successful', {
      userId: session.user_id._id,
      email: session.user_id.email,
      sessionId: session._id,
      ip: req.ip
    });

    next();
  } catch (error) {
    logger.error('Authentication middleware error', {
      error: error.message,
      stack: error.stack,
      ip: req.ip
    });
    
    return res.status(500).json({
      success: false,
      error: 'Authentication error'
    });
  }
};

// Optional authentication middleware (for public endpoints that benefit from user context)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // No token provided, continue without authentication
      return next();
    }

    // Use verifyToken but don't return errors, just continue
    await new Promise((resolve) => {
      verifyToken(req, res, (err) => {
        // Ignore errors in optional auth
        resolve();
      });
    });
    
    next();
  } catch (error) {
    // Log error but continue
    logger.debug('Optional auth failed', {
      error: error.message,
      ip: req.ip
    });
    next();
  }
};

// Generate JWT token
const generateToken = (payload) => {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: '24h',
    issuer: 'agrizy-formulation-platform',
    audience: 'agrizy-users'
  });
};

// Validate email domain
const validateEmailDomain = (email) => {
  const allowedDomains = ['agrizy.in', 'agrizywellness.com', 'naturalzy.com'];
  const domain = email.split('@')[1]?.toLowerCase();
  return allowedDomains.includes(domain);
};

// Admin only middleware
const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Check if user has admin privileges (you can add admin field to User model)
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    
    if (!adminEmails.includes(req.user.email)) {
      logger.warn('Admin access denied', {
        userId: req.user.id,
        email: req.user.email,
        ip: req.ip
      });
      
      return res.status(403).json({
        success: false,
        error: 'Admin privileges required'
      });
    }

    next();
  } catch (error) {
    logger.error('Admin middleware error', {
      error: error.message,
      userId: req.user?.id,
      ip: req.ip
    });
    
    return res.status(500).json({
      success: false,
      error: 'Authorization error'
    });
  }
};

module.exports = {
  verifyToken,
  optionalAuth,
  generateToken,
  validateEmailDomain,
  requireAdmin,
  JWT_SECRET
};