const mongoose = require('mongoose');
const logger = require('../utils/logger');

// MongoDB connection configuration
const mongoConfig = {
  development: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/agrizy_formulation_dev',
    options: {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    }
  },
  test: {
    uri: process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/agrizy_formulation_test',
    options: {
      maxPoolSize: 5,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    }
  },
  production: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/agrizy_formulation_prod',
    options: {
      maxPoolSize: 20,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      retryWrites: true,
      w: 'majority',
    }
  }
};

class MongoConnection {
  constructor() {
    this.isConnected = false;
    this.connection = null;
  }

  async connect(environment = 'development') {
    try {
      const config = mongoConfig[environment];
      
      if (!config) {
        throw new Error(`Invalid environment: ${environment}`);
      }

      // Close existing connection if any
      if (this.connection) {
        await this.disconnect();
      }

      logger.info('🔌 Attempting to connect to MongoDB...', {
        service: 'agrizy-formulation-api',
        environment,
        uri: config.uri.replace(/\/\/.*:.*@/, '//***:***@') // Hide credentials in logs
      });

      // Connect to MongoDB
      this.connection = await mongoose.connect(config.uri, config.options);
      this.isConnected = true;

      logger.info('✅ MongoDB connected successfully', {
        service: 'agrizy-formulation-api',
        environment,
        database: this.connection.connection.name,
        host: this.connection.connection.host
      });

      // Handle connection events
      mongoose.connection.on('error', (error) => {
        logger.error('❌ MongoDB connection error:', error);
        this.isConnected = false;
      });

      mongoose.connection.on('disconnected', () => {
        logger.warn('⚠️ MongoDB disconnected');
        this.isConnected = false;
      });

      mongoose.connection.on('reconnected', () => {
        logger.info('🔄 MongoDB reconnected');
        this.isConnected = true;
      });

      return this.connection;

    } catch (error) {
      logger.error('❌ Failed to connect to MongoDB:', {
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  async disconnect() {
    try {
      if (this.connection) {
        await mongoose.disconnect();
        this.isConnected = false;
        this.connection = null;
        logger.info('✅ MongoDB disconnected successfully');
      }
    } catch (error) {
      logger.error('❌ Error disconnecting from MongoDB:', error);
      throw error;
    }
  }

  async ping() {
    try {
      if (!this.isConnected) {
        throw new Error('MongoDB not connected');
      }
      
      const adminDb = this.connection.connection.db.admin();
      const result = await adminDb.ping();
      return result.ok === 1;
    } catch (error) {
      logger.error('❌ MongoDB ping failed:', error);
      return false;
    }
  }

  getConnection() {
    return this.connection;
  }

  isConnectionReady() {
    return this.isConnected && mongoose.connection.readyState === 1;
  }
}

// Create singleton instance
const mongoConnection = new MongoConnection();

// Graceful shutdown
process.on('SIGINT', async () => {
  logger.info('🛑 Received SIGINT, closing MongoDB connection...');
  await mongoConnection.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('🛑 Received SIGTERM, closing MongoDB connection...');
  await mongoConnection.disconnect();
  process.exit(0);
});

module.exports = {
  mongoConnection,
  mongoose
};