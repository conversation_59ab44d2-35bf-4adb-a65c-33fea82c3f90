{"type": "object", "required": ["recipes"], "properties": {"recipes": {"type": "array", "minItems": 4, "maxItems": 10, "description": "Array of recipe formulations (main recipe plus alternative recipes)", "items": {"type": "object", "required": ["name", "ingredients"], "properties": {"id": {"type": "string"}, "name": {"type": "string", "minLength": 5, "maxLength": 100}, "recipe_type": {"type": "string", "enum": ["main", "alternative", "regional", "premium", "budget"], "description": "Type of recipe - main is the primary formulation, others are alternatives"}, "description": {"type": "string", "minLength": 50, "maxLength": 500}, "target_market": {"type": "string"}, "target_geography": {"type": "string"}, "key_differences": {"type": "string", "minLength": 20}, "ingredients": {"type": "array", "minItems": 1, "maxItems": 12, "items": {"type": "object", "required": ["name", "percentage"], "properties": {"name": {"type": "string", "minLength": 3}, "percentage": {"type": "number", "minimum": 0.1, "maximum": 95}, "function": {"type": "string", "minLength": 10}, "cost_per_kg": {"type": "number", "minimum": 1, "maximum": 50000}, "supplier_suggestions": {"type": "array", "minItems": 0, "maxItems": 4, "items": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}, "location": {"type": "string"}, "quality_grade": {"type": "string", "enum": ["Food Grade", "Pharma Grade", "Cosmetic Grade", "Organic Certified"]}, "certification": {"type": "string"}, "lead_time_days": {"type": "number", "minimum": 1, "maximum": 90}}}}, "sourcing_region": {"type": "string"}, "alternative_names": {"type": "array", "items": {"type": "string"}}, "cas_number": {"type": "string"}, "nutritional_contribution": {"type": "string"}}}}, "nutritional_profile": {"type": "object", "required": ["macronutrients", "micronutrients", "bioactive_compounds", "daily_value_percentages"], "properties": {"macronutrients": {"type": "object", "required": ["protein_g", "carbohydrates_g", "fat_g", "fiber_g", "calories_per_serving"], "properties": {"protein_g": {"type": "number", "minimum": 0}, "carbohydrates_g": {"type": "number", "minimum": 0}, "fat_g": {"type": "number", "minimum": 0}, "fiber_g": {"type": "number", "minimum": 0}, "calories_per_serving": {"type": "number", "minimum": 0}}}, "micronutrients": {"type": "object", "required": ["vitamin_c_mg", "calcium_mg", "iron_mg"], "properties": {"vitamin_c_mg": {"type": "number", "minimum": 0}, "vitamin_d_iu": {"type": "number", "minimum": 0}, "calcium_mg": {"type": "number", "minimum": 0}, "iron_mg": {"type": "number", "minimum": 0}, "magnesium_mg": {"type": "number", "minimum": 0}}}, "bioactive_compounds": {"type": "object", "properties": {"antioxidants_orac": {"type": "number", "minimum": 0}, "polyphenols_mg": {"type": "number", "minimum": 0}, "omega3_mg": {"type": "number", "minimum": 0}}}, "daily_value_percentages": {"type": "object", "required": ["vitamin_c"], "properties": {"vitamin_c": {"type": "number", "minimum": 0, "maximum": 1000}, "vitamin_d": {"type": "number", "minimum": 0, "maximum": 1000}, "calcium": {"type": "number", "minimum": 0, "maximum": 1000}, "iron": {"type": "number", "minimum": 0, "maximum": 1000}}}}}, "production_specs": {"type": "object", "required": ["batch_size", "shelf_life", "storage_conditions", "manufacturing_process"], "properties": {"batch_size": {"type": "string"}, "shelf_life": {"type": "string"}, "storage_conditions": {"type": "string"}, "manufacturing_process": {"type": "string"}, "quality_control": {"type": "string"}, "packaging_requirements": {"type": "string"}}}, "compliance_details": {"type": "object", "required": ["regulatory_status", "certifications_needed", "labeling_requirements"], "properties": {"regulatory_status": {"type": "object", "required": ["fssai"], "properties": {"fda": {"type": "string"}, "fssai": {"type": "string"}, "eu": {"type": "string"}, "organic": {"type": "string"}}}, "certifications_needed": {"type": "array", "items": {"type": "string"}}, "labeling_requirements": {"type": "string"}, "health_claims": {"type": "string"}, "allergen_warnings": {"type": "string"}}}, "cost_analysis": {"type": "object", "required": ["raw_material_cost", "processing_cost", "packaging_cost", "total_cogs", "suggested_retail", "margin_percentage"], "properties": {"raw_material_cost": {"type": "number", "minimum": 10}, "processing_cost": {"type": "number", "minimum": 5}, "packaging_cost": {"type": "number", "minimum": 2}, "total_cogs": {"type": "number", "minimum": 20}, "suggested_retail": {"type": "number", "minimum": 50}, "margin_percentage": {"type": "number", "minimum": 20, "maximum": 90}, "break_even_volume": {"type": "number", "minimum": 1000}, "cost_breakdown": {"type": "object", "properties": {"active_ingredients": {"type": "number", "minimum": 0, "maximum": 100}, "excipients": {"type": "number", "minimum": 0, "maximum": 100}, "processing": {"type": "number", "minimum": 0, "maximum": 100}, "packaging": {"type": "number", "minimum": 0, "maximum": 100}}}}}, "scores": {"type": "object", "required": ["nutrition", "sustainability", "cost_efficiency", "compliance", "market_appeal"], "properties": {"nutrition": {"type": "number", "minimum": 0, "maximum": 100}, "sustainability": {"type": "number", "minimum": 0, "maximum": 100}, "cost_efficiency": {"type": "number", "minimum": 0, "maximum": 100}, "compliance": {"type": "number", "minimum": 0, "maximum": 100}, "market_appeal": {"type": "number", "minimum": 0, "maximum": 100}, "innovation_index": {"type": "number", "minimum": 0, "maximum": 100}, "scalability": {"type": "number", "minimum": 0, "maximum": 100}}}, "sustainability_metrics": {"type": "object", "properties": {"carbon_footprint_kg": {"type": "number", "minimum": 0}, "water_usage_liters": {"type": "number", "minimum": 0}, "renewable_energy_percentage": {"type": "number", "minimum": 0, "maximum": 100}, "sustainable_sourcing_percentage": {"type": "number", "minimum": 0, "maximum": 100}, "packaging_recyclability": {"type": "number", "minimum": 0, "maximum": 100}, "biodegradability_score": {"type": "number", "minimum": 0, "maximum": 100}}}, "traditional_benefits": {"type": "string"}, "innovation_highlights": {"type": "string"}}}}, "market_analysis": {"type": "object", "required": ["target_segments", "competitive_landscape", "market_opportunity"], "properties": {"target_segments": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["segment", "size_percentage", "willingness_to_pay", "key_motivators"], "properties": {"segment": {"type": "string"}, "size_percentage": {"type": "number", "minimum": 1, "maximum": 100}, "willingness_to_pay": {"type": "string"}, "key_motivators": {"type": "array", "items": {"type": "string"}}}}}, "competitive_landscape": {"type": "array", "items": {"type": "object", "required": ["competitor", "price_point", "key_differentiator"], "properties": {"competitor": {"type": "string"}, "price_point": {"type": "string"}, "key_differentiator": {"type": "string"}}}}, "market_opportunity": {"type": "object", "required": ["tam_size_crores", "growth_rate_cagr", "entry_barriers"], "properties": {"tam_size_crores": {"type": "number", "minimum": 100}, "growth_rate_cagr": {"type": "number", "minimum": 0, "maximum": 50}, "entry_barriers": {"type": "string"}}}}}, "regulatory_pathway": {"type": "object", "required": ["approval_timeline", "required_studies", "regulatory_costs", "key_milestones"], "properties": {"approval_timeline": {"type": "string"}, "required_studies": {"type": "array", "items": {"type": "string"}}, "regulatory_costs": {"type": "string"}, "key_milestones": {"type": "array", "items": {"type": "string"}}}}, "manufacturing_recommendations": {"type": "object", "properties": {"preferred_locations": {"type": "array", "items": {"type": "string"}}, "equipment_requirements": {"type": "string"}, "quality_standards": {"type": "string"}, "capacity_planning": {"type": "string"}}}}}