/**
 * Migration script to rename variations to recipes throughout the database
 * Run this after updating the codebase to use 'recipes' terminology
 */

const mongoose = require('mongoose');
require('dotenv').config();

async function migrateToRecipes() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/agrizy_formulation_dev');
    console.log('✅ Connected to MongoDB');

    const Project = require('./src/models/mongo/Project');
    
    // Find all projects with variations
    const projects = await Project.find({
      $or: [
        { 'current_formulation.variations': { $exists: true } },
        { 'current_formulation.recipes': { $exists: true } }
      ]
    });

    console.log(`Found ${projects.length} projects to check`);

    let migratedCount = 0;
    let alreadyMigratedCount = 0;

    for (const project of projects) {
      if (!project.current_formulation) continue;

      const cf = project.current_formulation;
      let needsMigration = false;

      console.log(`\nProject: ${project.name}`);

      // Check if we have variations that need to be moved to recipes
      if (cf.variations && cf.variations.length > 0) {
        console.log(`  - Has ${cf.variations.length} variations to migrate`);
        
        // Move variations to recipes
        if (!cf.recipes || cf.recipes.length === 0) {
          cf.recipes = [...cf.variations];
        } else {
          // Merge if both exist (shouldn't happen but just in case)
          cf.recipes = [...cf.recipes, ...cf.variations];
        }
        
        // Clear variations field
        cf.variations = undefined;
        needsMigration = true;
        
        console.log(`  ✨ Migrated ${cf.recipes.length} items to recipes array`);
      } else if (cf.recipes && cf.recipes.length > 0) {
        console.log(`  ✓ Already using recipes array (${cf.recipes.length} recipes)`);
        
        // Make sure variations is cleared
        if (cf.variations !== undefined) {
          cf.variations = undefined;
          needsMigration = true;
        }
        alreadyMigratedCount++;
      } else {
        console.log('  ⚠️ No formulation data found');
      }

      // Also update formulation_versions if they exist
      if (project.formulation_versions && project.formulation_versions.length > 0) {
        for (let version of project.formulation_versions) {
          if (version.formulation_data) {
            const vfd = version.formulation_data;
            
            // Migrate variations to recipes in version history
            if (vfd.variations && vfd.variations.length > 0) {
              if (!vfd.recipes || vfd.recipes.length === 0) {
                vfd.recipes = [...vfd.variations];
              } else {
                vfd.recipes = [...vfd.recipes, ...vfd.variations];
              }
              vfd.variations = undefined;
              needsMigration = true;
            }
          }
        }
      }

      if (needsMigration) {
        // Update recipe types if not set
        if (cf.recipes && cf.recipes.length > 0) {
          cf.recipes.forEach((recipe, index) => {
            if (!recipe.recipe_type) {
              recipe.recipe_type = index === 0 ? 'main' : 'alternative';
            }
            
            // Ensure all required fields have valid values
            if (!recipe.description || recipe.description.length < 50) {
              recipe.description = `${recipe.description || 'AI-optimized recipe formulation'}. This recipe has been carefully designed to meet quality, cost, and regulatory requirements.`;
            }
            
            if (!recipe.name || recipe.name.length < 5) {
              recipe.name = recipe.name || `Recipe ${index + 1}`;
            }
            
            if (recipe.key_differences !== undefined && recipe.key_differences.length < 20) {
              recipe.key_differences = `${recipe.key_differences || 'Optimized formulation'}. Enhanced for better performance and market appeal.`;
            }
          });
        }
        
        // Save the migrated project
        project.current_formulation = cf;
        await project.save({ validateBeforeSave: false });
        migratedCount++;
        console.log('  ✅ Project migrated successfully');
      }
    }

    console.log('\n🎉 Migration completed successfully!');
    console.log(`  - ${migratedCount} projects migrated`);
    console.log(`  - ${alreadyMigratedCount} projects already using recipes`);
    console.log('All formulations now use the "recipes" terminology.');
    
    // Verify the migration
    const verifyProject = await Project.findOne({
      'current_formulation.variations': { $exists: true, $ne: undefined }
    });
    
    if (verifyProject) {
      console.log('⚠️  Warning: Some projects still have variations field. Please check manually.');
    } else {
      console.log('✅ Verification passed: No projects have variations field anymore.');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

// Run migration
migrateToRecipes();