{"slug": "formulation_wizard_v2", "name": "Formulation Setup Wizard v2", "layout_locked": true, "steps": [{"key": "industry", "title": "Choose Industry", "source": "taxonomies", "filter": {"level": "industry", "status": "active"}, "selection_mode": "single"}, {"key": "category", "title": "Choose Category", "source": "taxonomies", "filter": {"level": "category", "by_parent": "industry"}, "selection_mode": "single"}, {"key": "sub_category", "title": "Choose Product Type", "source": "taxonomies", "filter": {"level": "sub_category", "by_parent": "category"}, "selection_mode": "single"}, {"key": "configure", "title": "Configure Goals & Parameters", "panels": [{"title": "Global Goals", "binding_group": "global", "fields": ["budget_per_unit", "shelf_life", "sustainability_priority", "compliance_tags"]}, {"title": "Product Parameters", "binding_group": "sub_category", "fields": "auto"}], "cta": {"label": "Generate Formulation", "action": "run_generation"}}], "bindings": {"global": [{"parameter_key": "budget_per_unit", "widget": {"type": "slider", "step": 1}, "guidance": {"min": 10, "max": 500, "recommended": 120, "unit": "INR"}}, {"parameter_key": "shelf_life", "widget": {"type": "slider", "step": 3}, "guidance": {"min": 6, "max": 36, "unit": "months"}}, {"parameter_key": "sustainability_priority", "widget": {"type": "select"}}, {"parameter_key": "compliance_tags", "widget": {"type": "chips"}}]}, "version": 2, "status": "active"}