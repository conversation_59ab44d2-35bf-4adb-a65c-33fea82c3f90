{"_id": "ca16fa3506fb4d9783f13461", "level": "industry", "slug": "herbal-cosmetics", "name": "Herbal Cosmetics", "parent_id": null, "status": "active", "order": 10, "metadata": {"icon": "sparkles", "description": "Natural and herbal cosmetic products"}}
{"_id": "26942e1888d1436aa4308a04", "level": "industry", "slug": "nutraceuticals", "name": "Nutraceuticals", "parent_id": null, "status": "active", "order": 20, "metadata": {"icon": "capsule", "description": "Nutritional supplements and functional products"}}
{"_id": "683d8012ec5a43548b842747", "level": "industry", "slug": "functional-food", "name": "Functional Food", "parent_id": null, "status": "active", "order": 30, "metadata": {"icon": "cup", "description": "Functional foods and beverages with health benefits"}}
{"_id": "b4f234a8b74a414eaf2cffaa", "level": "industry", "slug": "herbal-extracts", "name": "Herbal Extracts", "parent_id": null, "status": "active", "order": 40, "metadata": {"icon": "leaf", "description": "Standardized and full-spectrum botanical extracts"}}
{"_id": "f869221e3a3b48cdab2e7dab", "level": "category", "slug": "skin-care", "name": "Skin Care", "parent_id": "ca16fa3506fb4d9783f13461", "status": "active", "order": 11}
{"_id": "d229f460dc4541c495d6fb16", "level": "category", "slug": "hair-care", "name": "Hair Care", "parent_id": "ca16fa3506fb4d9783f13461", "status": "active", "order": 12}
{"_id": "aae093423c97470cab726178", "level": "category", "slug": "body-care", "name": "Body Care", "parent_id": "ca16fa3506fb4d9783f13461", "status": "active", "order": 13}
{"_id": "92aaaa51a50f4c9c96170f9c", "level": "category", "slug": "functional-color-cosmetics", "name": "Functional Color Cosmetics", "parent_id": "ca16fa3506fb4d9783f13461", "status": "active", "order": 14}
{"_id": "57840b6a4f6e4d0585702557", "level": "sub_category", "slug": "face-serum", "name": "Face Serum", "parent_id": "f869221e3a3b48cdab2e7dab", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "d4fe9c05d3c34212af6c9abd", "level": "sub_category", "slug": "herbal-cream", "name": "Herbal Cream", "parent_id": "f869221e3a3b48cdab2e7dab", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "1139f8e69efc472fa1447dc4", "level": "sub_category", "slug": "cleanser-gel", "name": "Gel Cleanser", "parent_id": "f869221e3a3b48cdab2e7dab", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "eba7a7e53a3d4cfc99cf8a64", "level": "sub_category", "slug": "cleanser-micellar", "name": "Micellar Water", "parent_id": "f869221e3a3b48cdab2e7dab", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "eeaf174bb2b24f60a456f68a", "level": "sub_category", "slug": "toner-hydrating", "name": "Hydrating Toner", "parent_id": "f869221e3a3b48cdab2e7dab", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "c4d5502fbf6d4afaae64f002", "level": "sub_category", "slug": "toner-exfoliating", "name": "AHA/BHA Toner", "parent_id": "f869221e3a3b48cdab2e7dab", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "5ccfa023cab948e48eb00816", "level": "sub_category", "slug": "mask-clay", "name": "Clay Mask", "parent_id": "f869221e3a3b48cdab2e7dab", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "e539d12cc8564f5890cbc674", "level": "sub_category", "slug": "sunscreen-mineral", "name": "Mineral Sunscreen", "parent_id": "f869221e3a3b48cdab2e7dab", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "0240e0b3a2c54d118ccd4bf4", "level": "sub_category", "slug": "eye-cream", "name": "Eye Cream", "parent_id": "f869221e3a3b48cdab2e7dab", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "f5ac1a100bcc40cca2c17cf5", "level": "sub_category", "slug": "lip-balm", "name": "Lip Balm", "parent_id": "f869221e3a3b48cdab2e7dab", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "986f81df4637454f9eca977f", "level": "sub_category", "slug": "shampoo", "name": "Shampoo", "parent_id": "d229f460dc4541c495d6fb16", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "02f8ec7f56314c9fb0a2f3da", "level": "sub_category", "slug": "conditioner", "name": "Conditioner", "parent_id": "d229f460dc4541c495d6fb16", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "9931e4debcc949de8b519395", "level": "sub_category", "slug": "hair-oil", "name": "Herbal Hair Oil", "parent_id": "d229f460dc4541c495d6fb16", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "61fcd01c92514d34a5b0a59e", "level": "sub_category", "slug": "hair-serum", "name": "Hair Serum", "parent_id": "d229f460dc4541c495d6fb16", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "ffa12280b4a240a994f36385", "level": "sub_category", "slug": "body-lotion", "name": "Body Lotion", "parent_id": "aae093423c97470cab726178", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "7b9ea1e906bc45719c20b1bc", "level": "sub_category", "slug": "body-wash", "name": "Body Wash", "parent_id": "aae093423c97470cab726178", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "be6ea8c4d23e4d31907f26eb", "level": "sub_category", "slug": "body-oil", "name": "Body Oil", "parent_id": "aae093423c97470cab726178", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "fa9de244ff9646f0ba255b41", "level": "sub_category", "slug": "tinted-lip-balm", "name": "Tinted Lip Balm", "parent_id": "92aaaa51a50f4c9c96170f9c", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "972be6eb0f33430b988837c1", "level": "sub_category", "slug": "bb-cream", "name": "BB Cream", "parent_id": "92aaaa51a50f4c9c96170f9c", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "a3907cde21d34509bb3d0a99", "level": "sub_category", "slug": "herbal-lipstick", "name": "Herbal Lipstick", "parent_id": "92aaaa51a50f4c9c96170f9c", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile"]}}
{"_id": "5b3ad715bd1846798a50948e", "level": "category", "slug": "capsules", "name": "Capsules", "parent_id": "26942e1888d1436aa4308a04", "status": "active", "order": 11}
{"_id": "14cafddc9e7d42a8953dee29", "level": "category", "slug": "tablets", "name": "Tablets", "parent_id": "26942e1888d1436aa4308a04", "status": "active", "order": 12}
{"_id": "ed35c28216d84227a526458f", "level": "category", "slug": "powders", "name": "Powders", "parent_id": "26942e1888d1436aa4308a04", "status": "active", "order": 13}
{"_id": "ce4ebddaf21c42ea8a41cceb", "level": "category", "slug": "gummies-chews", "name": "Gummies & Chews", "parent_id": "26942e1888d1436aa4308a04", "status": "active", "order": 14}
{"_id": "53d073d4461e4ce09e2e90c9", "level": "category", "slug": "liquids-sprays", "name": "Liquids & Sprays", "parent_id": "26942e1888d1436aa4308a04", "status": "active", "order": 15}
{"_id": "1a836bdcc14648ce84b65581", "level": "category", "slug": "others", "name": "Others", "parent_id": "26942e1888d1436aa4308a04", "status": "active", "order": 16}
{"_id": "df6016adc184462ca16a6945", "level": "sub_category", "slug": "herbal-capsule", "name": "Herbal Capsule", "parent_id": "5b3ad715bd1846798a50948e", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "68da185aee114a03a632e441", "level": "sub_category", "slug": "multivit-capsule", "name": "Multivitamin Capsule", "parent_id": "5b3ad715bd1846798a50948e", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "c5d6baa5fbd441178935b0f1", "level": "sub_category", "slug": "omega3-capsule", "name": "Omega-3 Capsule", "parent_id": "5b3ad715bd1846798a50948e", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "d7cf9fdeb16642f6afd30d58", "level": "sub_category", "slug": "standard-tablet", "name": "Standard Tablet", "parent_id": "14cafddc9e7d42a8953dee29", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "96de941723a644d7915e0537", "level": "sub_category", "slug": "chewable-tablet", "name": "Chewable Tablet", "parent_id": "14cafddc9e7d42a8953dee29", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "0982c9dc706e41acacadfcd5", "level": "sub_category", "slug": "effervescent-tablet", "name": "Effervescent Tablet", "parent_id": "14cafddc9e7d42a8953dee29", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "0464aac292d94a2ca2d011e8", "level": "sub_category", "slug": "protein-powder", "name": "Protein Powder", "parent_id": "ed35c28216d84227a526458f", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "c569510437a642cbb2b421d6", "level": "sub_category", "slug": "collagen-powder", "name": "Collagen Powder", "parent_id": "ed35c28216d84227a526458f", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "88c0812f536d43729fcc0a84", "level": "sub_category", "slug": "electrolyte-powder", "name": "Electrolyte Powder", "parent_id": "ed35c28216d84227a526458f", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "3e310d8dd61d40519d4d0498", "level": "sub_category", "slug": "vitamin-gummies", "name": "Vitamin Gummies", "parent_id": "ce4ebddaf21c42ea8a41cceb", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "e8a0fb6aa2614e3b8109f720", "level": "sub_category", "slug": "herbal-gummies", "name": "Herbal Gummies", "parent_id": "ce4ebddaf21c42ea8a41cceb", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "1ce918602803403d8b90ff66", "level": "sub_category", "slug": "tonic-syrup", "name": "Ayurvedic Tonic/Syrup", "parent_id": "53d073d4461e4ce09e2e90c9", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "ea898bcc4d7944b391b92528", "level": "sub_category", "slug": "oral-spray", "name": "Oral Spray", "parent_id": "53d073d4461e4ce09e2e90c9", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "d4ca1836b77d4e0db837be5a", "level": "sub_category", "slug": "lozenge", "name": "Lozenge", "parent_id": "1a836bdcc14648ce84b65581", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "8c66a2ada92e4ab5b611c3a3", "level": "sub_category", "slug": "sachet-probiotic", "name": "Probiotic Sachet", "parent_id": "1a836bdcc14648ce84b65581", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "d477f7f2c33d4096841d3063", "level": "category", "slug": "functional-beverages", "name": "Functional Beverages", "parent_id": "683d8012ec5a43548b842747", "status": "active", "order": 11}
{"_id": "087119ff34284025b6cf1616", "level": "category", "slug": "bars-snacks", "name": "Bars & Snacks", "parent_id": "683d8012ec5a43548b842747", "status": "active", "order": 12}
{"_id": "a967c4f305914dd89c88f0e8", "level": "category", "slug": "breakfast-cereals", "name": "Breakfast & Cereals", "parent_id": "683d8012ec5a43548b842747", "status": "active", "order": 13}
{"_id": "a2c57696129e47dd9f5d55ad", "level": "category", "slug": "fortified-staples", "name": "Fortified Staples", "parent_id": "683d8012ec5a43548b842747", "status": "active", "order": 14}
{"_id": "7b2a24e111db4d5aa35c17e0", "level": "sub_category", "slug": "energy-drink", "name": "Energy Drink", "parent_id": "d477f7f2c33d4096841d3063", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "10ffb29caec94695849a7612", "level": "sub_category", "slug": "wellness-shot", "name": "Wellness Shot", "parent_id": "d477f7f2c33d4096841d3063", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "1e13f2ab537c4b5b84d75a07", "level": "sub_category", "slug": "functional-water", "name": "Functional Water", "parent_id": "d477f7f2c33d4096841d3063", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "31b21b97a0a9489fbbd609f6", "level": "sub_category", "slug": "protein-shake-rtd", "name": "Protein Shake (RTD)", "parent_id": "d477f7f2c33d4096841d3063", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "2c38e91e229a414886899351", "level": "sub_category", "slug": "herbal-tea", "name": "Herbal Tea/Infusion", "parent_id": "d477f7f2c33d4096841d3063", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "7df1d93edc1e419b8088c6b6", "level": "sub_category", "slug": "protein-bar", "name": "Protein Bar", "parent_id": "087119ff34284025b6cf1616", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "7ff012e24bf94c599e4be4f3", "level": "sub_category", "slug": "granola-bar", "name": "Granola/Cereal Bar", "parent_id": "087119ff34284025b6cf1616", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "de08a210160349c992468667", "level": "sub_category", "slug": "fortified-biscuit", "name": "Fortified Biscuit", "parent_id": "087119ff34284025b6cf1616", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "baa0d93891d64071b6674f4d", "level": "sub_category", "slug": "fortified-cereal", "name": "Fortified Cereal", "parent_id": "a967c4f305914dd89c88f0e8", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "95ea66002dc94749a7ee22bb", "level": "sub_category", "slug": "high-protein-oats", "name": "High-protein Oats", "parent_id": "a967c4f305914dd89c88f0e8", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "5bc00b24b8d4478e9e596352", "level": "sub_category", "slug": "fortified-flour", "name": "Fortified Flour (Atta/Rice)", "parent_id": "a2c57696129e47dd9f5d55ad", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "886f1eba197a41bab1937ec5", "level": "sub_category", "slug": "fortified-dairy", "name": "Fortified Dairy (Milk/Yogurt)", "parent_id": "a2c57696129e47dd9f5d55ad", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "7ef8c42e26674bd4b70add61", "level": "sub_category", "slug": "enriched-oil", "name": "Enriched Oil", "parent_id": "a2c57696129e47dd9f5d55ad", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "nutritional_profile", "process_spec", "quality_safety", "compliance", "sustainability", "packaging", "cost_analysis", "sensory", "stability_plan", "scores"]}}
{"_id": "75824812e33541c2bc75ce78", "level": "category", "slug": "extract-formats", "name": "Extract Formats", "parent_id": "b4f234a8b74a414eaf2cffaa", "status": "active", "order": 11}
{"_id": "e16bdd229a244197b8a9024d", "level": "sub_category", "slug": "powdered-extract", "name": "Powdered Extract", "parent_id": "75824812e33541c2bc75ce78", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "compliance", "sustainability", "packaging", "cost_analysis", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile", "sensory", "quality_safety"]}}
{"_id": "a400f122e7724e29936ac86d", "level": "sub_category", "slug": "liquid-extract", "name": "Liquid Extract", "parent_id": "75824812e33541c2bc75ce78", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "compliance", "sustainability", "packaging", "cost_analysis", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile", "sensory", "quality_safety"]}}
{"_id": "806d14dfd98d4f589b528804", "level": "sub_category", "slug": "standardized-extract", "name": "Standardized Extract", "parent_id": "75824812e33541c2bc75ce78", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "compliance", "sustainability", "packaging", "cost_analysis", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile", "sensory", "quality_safety"]}}
{"_id": "2191a7ca238b4a1ca70b526a", "level": "sub_category", "slug": "full-spectrum-extract", "name": "Full Spectrum Extract", "parent_id": "75824812e33541c2bc75ce78", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "compliance", "sustainability", "packaging", "cost_analysis", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile", "sensory", "quality_safety"]}}
{"_id": "8f68647d48d144ef9ccc503c", "level": "sub_category", "slug": "isolate", "name": "Isolate / Pure Compound", "parent_id": "75824812e33541c2bc75ce78", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "compliance", "sustainability", "packaging", "cost_analysis", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile", "sensory", "quality_safety"]}}
{"_id": "b5d4d2807ac94bd2afb454b1", "level": "sub_category", "slug": "co2-extract", "name": "CO₂ / Oil Extract", "parent_id": "75824812e33541c2bc75ce78", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "compliance", "sustainability", "packaging", "cost_analysis", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile", "sensory", "quality_safety"]}}
{"_id": "41d2fba6abb04a739e06ff92", "level": "sub_category", "slug": "fermented-extract", "name": "Fermented / Enhanced Extract", "parent_id": "75824812e33541c2bc75ce78", "status": "active", "order": 99, "applicability": {"components": ["ingredients", "process_spec", "compliance", "sustainability", "packaging", "cost_analysis", "stability_plan", "scores"], "disallowed_components": ["nutritional_profile", "sensory", "quality_safety"]}}
