[{"slug": "formulation_wizard_v2", "name": "Formulation Wizard v2", "description": "Main wizard flow for creating formulations", "version": "2.0.0", "status": "active", "steps": [{"slug": "industry", "title": "Select Industry", "description": "Choose your industry vertical", "type": "selection", "data_source": {"type": "taxonomy", "level": "industry"}, "ui": {"layout": "cards", "columns": 3}, "validation": {"required": true}, "navigation": {"next": "category", "can_skip": false}}, {"slug": "category", "title": "Select Category", "description": "Choose a product category", "type": "selection", "data_source": {"type": "taxonomy", "level": "category", "filter_by": "parent_id"}, "ui": {"layout": "cards", "columns": 3}, "validation": {"required": true}, "navigation": {"next": "product_type", "prev": "industry", "can_skip": false}}, {"slug": "product_type", "title": "Select Product Type", "description": "Choose your specific product type", "type": "selection", "data_source": {"type": "taxonomy", "level": "sub_category", "filter_by": "parent_id"}, "ui": {"layout": "cards", "columns": 3, "allow_custom": true}, "validation": {"required": true}, "navigation": {"next": "configure", "prev": "category", "can_skip": false}}, {"slug": "configure", "title": "Configure Parameters", "description": "Set your formulation parameters", "type": "form", "data_source": {"type": "parameters", "filter_by": "sub_category"}, "ui": {"layout": "sections", "sections": [{"id": "formulation", "title": "Formulation Basics", "icon": "flask"}, {"id": "actives", "title": "Active Ingredients", "icon": "sparkles"}, {"id": "aesthetics", "title": "Aesthetics", "icon": "palette"}, {"id": "safety", "title": "Safety & Preservation", "icon": "shield"}, {"id": "packaging", "title": "Packaging", "icon": "package"}]}, "validation": {"use_guardrails": true}, "navigation": {"next": "generate", "prev": "product_type", "can_skip": false}}, {"slug": "generate", "title": "Generate Formulation", "description": "AI generates your formulation", "type": "generation", "ui": {"show_progress": true, "show_preview": true}, "navigation": {"next": "review", "prev": "configure", "can_skip": false}}, {"slug": "review", "title": "Review & Refine", "description": "Review and refine your formulation", "type": "editor", "ui": {"layout": "split", "enable_chat": true, "enable_playground": true}, "navigation": {"prev": "generate", "can_skip": false}}], "bindings": {"global": {"cta_labels": {"next": "Continue", "prev": "Back", "generate": "Generate Formulation", "save": "Save & Continue"}, "panels": [{"id": "progress", "type": "progress_bar", "position": "top"}, {"id": "help", "type": "help_tooltip", "position": "float"}]}}}]