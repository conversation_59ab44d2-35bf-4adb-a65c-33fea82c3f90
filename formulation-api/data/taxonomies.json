[{"_id": "ind_herbal_cosmetics", "slug": "herbal-cosmetics", "name": "Herbal Cosmetics", "level": "industry", "parent_id": null, "status": "active", "metadata": {"description": "Natural and herbal cosmetic products", "icon": "leaf"}}, {"_id": "ind_nutraceuticals", "slug": "nutraceuticals", "name": "Nutraceuticals", "level": "industry", "parent_id": null, "status": "active", "metadata": {"description": "Nutritional supplements and functional foods", "icon": "pill"}}, {"_id": "ind_functional_beverages", "slug": "functional-beverages", "name": "Functional Beverages", "level": "industry", "parent_id": null, "status": "active", "metadata": {"description": "Health-focused beverages with functional benefits", "icon": "cup"}}, {"_id": "cat_skin_care", "slug": "skin-care", "name": "Skin Care", "level": "category", "parent_id": "ind_herbal_cosmetics", "status": "active"}, {"_id": "cat_hair_care", "slug": "hair-care", "name": "Hair Care", "level": "category", "parent_id": "ind_herbal_cosmetics", "status": "active"}, {"_id": "cat_supplements", "slug": "supplements", "name": "Dietary Supplements", "level": "category", "parent_id": "ind_nutraceuticals", "status": "active"}, {"_id": "cat_energy_drinks", "slug": "energy-drinks", "name": "Energy Drinks", "level": "category", "parent_id": "ind_functional_beverages", "status": "active"}, {"_id": "sub_face_serum", "slug": "face-serum", "name": "Face Serum", "level": "sub_category", "parent_id": "cat_skin_care", "status": "active", "metadata": {"typical_volume": "30ml", "packaging": "dropper bottle"}}, {"_id": "sub_face_cream", "slug": "face-cream", "name": "Face Cream", "level": "sub_category", "parent_id": "cat_skin_care", "status": "active", "metadata": {"typical_volume": "50ml", "packaging": "jar"}}, {"_id": "sub_shampoo", "slug": "shampoo", "name": "Shampoo", "level": "sub_category", "parent_id": "cat_hair_care", "status": "active", "metadata": {"typical_volume": "250ml", "packaging": "bottle"}}, {"_id": "sub_protein_powder", "slug": "protein-powder", "name": "<PERSON><PERSON>", "level": "sub_category", "parent_id": "cat_supplements", "status": "active"}, {"_id": "sub_pre_workout", "slug": "pre-workout", "name": "Pre-Workout", "level": "sub_category", "parent_id": "cat_energy_drinks", "status": "active"}]