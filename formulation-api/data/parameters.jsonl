{"_id": "8d6a6dc9570c4d599a516b93", "key": "budget_per_unit", "name": "Budget per Unit", "type": "number", "unit": "INR", "validators": [{"type": "range", "min": 1, "max": 10000}]}
{"_id": "8b10ea040f834b368422a1e5", "key": "shelf_life", "name": "Shelf Life", "type": "number", "unit": "months", "validators": [{"type": "range", "min": 3, "max": 60}]}
{"_id": "4735423913e44c708a3d3cb0", "key": "sustainability_priority", "name": "Sustainability Priority", "type": "enum", "answers_key": "sustainability_priority"}
{"_id": "880df12e1ee44e26ba71b29e", "key": "compliance_tags", "name": "Compliance & Certifications", "type": "list", "schema": {"fields": [{"key": "tag", "type": "string"}]}}
{"_id": "7a3af22996e4487495167cb3", "key": "base_type", "name": "Base / Emulsion Type", "type": "enum", "answers_key": "base_type"}
{"_id": "2e40887936534e72905369e6", "key": "oil_phase_pct", "name": "Oil Phase %", "type": "number", "validators": [{"type": "range", "min": 0, "max": 100}]}
{"_id": "0952f82adc5e467483c09eac", "key": "water_phase_pct", "name": "Water Phase %", "type": "number", "validators": [{"type": "range", "min": 0, "max": 100}]}
{"_id": "a5161e5a39a84a51b8623273", "key": "humectant_system", "name": "Humectant System", "type": "list", "schema": {"fields": [{"key": "inci", "type": "string"}, {"key": "pct_w_w", "type": "number"}]}}
{"_id": "315529a6cf204ffb8f363b09", "key": "surfactant_system_hlb", "name": "Surfactant System HLB", "type": "number", "validators": [{"type": "range", "min": 4, "max": 16}]}
{"_id": "5397082b4aa44c06b0b071b6", "key": "emollient_profile", "name": "Emollient Profile", "type": "list", "schema": {"fields": [{"key": "inci", "type": "string"}, {"key": "pct_w_w", "type": "number"}]}}
{"_id": "714091683b964b039ee05a8b", "key": "rheology_modifier", "name": "Rheology Modifier", "type": "list", "schema": {"fields": [{"key": "inci", "type": "string"}, {"key": "pct_w_w", "type": "number"}]}}
{"_id": "efb566fb99b04b1b8d7938df", "key": "ph_target", "name": "Target pH", "type": "number", "validators": [{"type": "range", "min": 2.5, "max": 9.0}]}
{"_id": "517315c3cd7e4adb984b5fdd", "key": "viscosity_cps", "name": "Viscosity (cP)", "type": "number", "validators": [{"type": "range", "min": 10, "max": 200000}]}
{"_id": "330c643c4cb646138e892b5a", "key": "preservative_system", "name": "Preservative System", "type": "enum", "answers_key": "preservative_system"}
{"_id": "84f2cf2ea420475db22ef4dd", "key": "preservative_load_pct", "name": "Preservative Load %", "type": "number", "validators": [{"type": "range", "min": 0, "max": 1.5}]}
{"_id": "261117643d8b422dae33bf03", "key": "penetration_strategy", "name": "Penetration Strategy", "type": "enum", "answers_key": "penetration_strategy"}
{"_id": "d33199a91d374103ba482c23", "key": "fragrance_load_pct", "name": "Fragrance Load %", "type": "number", "validators": [{"type": "range", "min": 0, "max": 2.0}]}
{"_id": "cba4c7aea7bb4b90ae2b1eb5", "key": "comedogenicity_limit", "name": "Comedogenicity Limit", "type": "number", "validators": [{"type": "range", "min": 0, "max": 5}]}
{"_id": "68ed880cb5c1470dacc182e6", "key": "spf_target", "name": "SPF Target", "type": "number", "validators": [{"type": "range", "min": 10, "max": 100}]}
{"_id": "faca7cea2e8746d5ae430dd3", "key": "uva_pf_target", "name": "UVA PF Target", "type": "number", "validators": [{"type": "range", "min": 1, "max": 50}]}
{"_id": "32077358f55f489d9c6ad1c6", "key": "pigment_load_pct", "name": "Pigment Load %", "type": "number", "validators": [{"type": "range", "min": 0, "max": 30}]}
{"_id": "785868b8ceb946a0a78c946c", "key": "finish_texture", "name": "Finish / Texture", "type": "enum", "answers_key": "finish_texture"}
{"_id": "dd5cbc1cdbc0417f8469dbeb", "key": "pigment_type", "name": "Pigment Type", "type": "enum", "answers_key": "pigment_type"}
{"_id": "46a4112ee11c4713be7bf8d5", "key": "active_panel", "name": "Active Ingredient Panel", "type": "list", "schema": {"fields": [{"key": "inci", "type": "string", "required": true}, {"key": "pct_w_w", "type": "number", "unit": "%", "required": true, "min": 0, "max": 20}, {"key": "standardization", "type": "string"}, {"key": "role", "type": "string"}]}}
{"_id": "34fb5346007448ecbe7d2319", "key": "skin_type", "name": "Skin Type", "type": "enum", "answers_key": "skin_type"}
{"_id": "e0bfe7953ade46f7ae5ec2a7", "key": "claim_family", "name": "Claim Family", "type": "enum", "answers_key": "claim_family"}
{"_id": "00eab79f25df4878a8ad4c81", "key": "pack_format", "name": "Packaging Format", "type": "enum", "answers_key": "pack_format"}
{"_id": "01d82bdaf4b2446eb29316ff", "key": "pack_material", "name": "Packaging Material", "type": "enum", "answers_key": "pack_material"}
{"_id": "06735ba6f4a24c4db773f615", "key": "serving_size_g", "name": "Serving Size (g)", "type": "number", "validators": [{"type": "range", "min": 0.1, "max": 200}]}
{"_id": "8f08a22118124b12bfd7e16f", "key": "dosage_form", "name": "Dosage Form", "type": "enum", "answers_key": "dosage_form"}
{"_id": "591960029472429285fd9e22", "key": "capsule_size", "name": "Capsule Size", "type": "enum", "answers_key": "capsule_size"}
{"_id": "d17e43eb08734e808a0b5e0b", "key": "capsule_shell", "name": "Capsule Shell", "type": "enum", "answers_key": "capsule_shell"}
{"_id": "6dab7487064c47f99872b48a", "key": "actives_list", "name": "Actives List", "type": "list", "schema": {"fields": [{"key": "name", "type": "string"}, {"key": "mg_per_serving", "type": "number"}, {"key": "standardization", "type": "string"}, {"key": "rda_pct", "type": "number"}]}}
{"_id": "a8e7e0f5dda142dca821a50b", "key": "excipients", "name": "Excipients", "type": "list", "schema": {"fields": [{"key": "name", "type": "string"}, {"key": "pct_w_w", "type": "number"}, {"key": "function", "type": "string"}]}}
{"_id": "078f4a95d794499cb3d49761", "key": "disintegration_time_min", "name": "Disintegration Time (min)", "type": "number", "validators": [{"type": "range", "min": 1, "max": 60}]}
{"_id": "a03f3e3a6be347a3be481cd3", "key": "dissolution_profile", "name": "Dissolution Profile", "type": "string"}
{"_id": "aeb3be34b0bf4265b2ad5732", "key": "bioavailability_strategy", "name": "Bioavailability Strategy", "type": "enum", "answers_key": "bioavailability_strategy"}
{"_id": "bab1a533cc634406b6c61a03", "key": "cfu_per_serving", "name": "Probiotic CFU/Serving", "type": "number", "validators": [{"type": "range", "min": 1000000.0, "max": 1000000000000.0}]}
{"_id": "975405de9f184ce7bbe5e05d", "key": "moisture_max_pct", "name": "Moisture Max %", "type": "number", "validators": [{"type": "range", "min": 0.1, "max": 12}]}
{"_id": "16b79f3ee88d41a3a817708a", "key": "serving_size_ml", "name": "Serving Size (ml)", "type": "number", "validators": [{"type": "range", "min": 10, "max": 1000}]}
{"_id": "4ccc6648596d4811a6875f6b", "key": "brix", "name": "Brix (°Bx)", "type": "number", "validators": [{"type": "range", "min": 0, "max": 80}]}
{"_id": "ccd808786b714c87bdc3d17f", "key": "pH_food", "name": "pH (Food/Beverage)", "type": "number", "validators": [{"type": "range", "min": 2.0, "max": 8.0}]}
{"_id": "0e9052863e514082a28c9a94", "key": "water_activity_aw", "name": "Water Activity (aw)", "type": "number", "validators": [{"type": "range", "min": 0.1, "max": 1.0}]}
{"_id": "987324ace8664a4098cc1eaa", "key": "protein_pct", "name": "Protein % (w/w)", "type": "number", "validators": [{"type": "range", "min": 0, "max": 80}]}
{"_id": "dd9ac1d064ad42b4a6ea5487", "key": "sugar_pct", "name": "Sugar % (w/w)", "type": "number", "validators": [{"type": "range", "min": 0, "max": 60}]}
{"_id": "193e1adb97ad445282e433d7", "key": "fat_pct", "name": "Fat % (w/w)", "type": "number", "validators": [{"type": "range", "min": 0, "max": 60}]}
{"_id": "19a95546776e4bfe86124ab8", "key": "fiber_pct", "name": "Fiber % (w/w)", "type": "number", "validators": [{"type": "range", "min": 0, "max": 60}]}
{"_id": "acc3308d8d0947f8afca70d1", "key": "sodium_mg", "name": "Sodium (mg/serving)", "type": "number", "validators": [{"type": "range", "min": 0, "max": 2000}]}
{"_id": "661674c5cac94255aa36bf97", "key": "caffeine_mg", "name": "Caffeine (mg/serving)", "type": "number", "validators": [{"type": "range", "min": 0, "max": 400}]}
{"_id": "f0b750e9d1844861838e46d2", "key": "carbonation_vol", "name": "Carbonation (CO2 volumes)", "type": "number", "validators": [{"type": "range", "min": 0, "max": 4.5}]}
{"_id": "094bbdfa8e1c49dfbe3b6289", "key": "osmolality_mOsm", "name": "Osmolality (mOsm/kg)", "type": "number", "validators": [{"type": "range", "min": 50, "max": 1500}]}
{"_id": "a459813e536b4d21b630bb0a", "key": "process_method", "name": "Process Method", "type": "enum", "answers_key": "process_method"}
{"_id": "8e1309a068a946a18901ff33", "key": "sweetener_strategy", "name": "Sweetener Strategy", "type": "enum", "answers_key": "sweetener_strategy"}
{"_id": "fc4d775b304c4976b4e27eb1", "key": "flavor_system", "name": "Flavor System", "type": "enum", "answers_key": "flavor_system"}
{"_id": "7b1763946c144ee99255f7f4", "key": "protein_source", "name": "Protein Source", "type": "enum", "answers_key": "protein_source"}
{"_id": "9efd6c601fc44ba88df313ac", "key": "fat_source", "name": "Fat Source", "type": "enum", "answers_key": "fat_source"}
{"_id": "003f1b5c82e544dea0ccb051", "key": "extraction_method", "name": "Extraction Method", "type": "enum", "answers_key": "extraction_method"}
{"_id": "1b09c6fb414c45d6b36ea81a", "key": "standardization_pct", "name": "Standardization (%)", "type": "number", "validators": [{"type": "range", "min": 0, "max": 100}]}
{"_id": "b0aa00ae742d4efe9b92c466", "key": "solubility", "name": "Solubility", "type": "enum", "answers_key": "solubility"}
{"_id": "05997db7d4f14795aafc9358", "key": "botanical_part", "name": "Botanical Part", "type": "enum", "answers_key": "botanical_part"}
{"_id": "70d407f0c2534274a2d14ec9", "key": "residual_solvent_ppm", "name": "Residual Solvent (ppm)", "type": "number", "validators": [{"type": "range", "min": 0, "max": 5000}]}
{"_id": "2c30a3492c534c768ac01a85", "key": "heavy_metals_ppm", "name": "Heavy Metals (ppm total)", "type": "number", "validators": [{"type": "range", "min": 0, "max": 50}]}
{"_id": "dc8c9ff1f82e49d49369bac3", "key": "microbial_limits", "name": "Microbial Limits", "type": "object"}
{"_id": "c49266a93c81465da1ddb10d", "key": "lod_pct", "name": "Loss on Drying (%)", "type": "number", "validators": [{"type": "range", "min": 0, "max": 15}]}
{"_id": "185ccc978878459f9065a711", "key": "particle_size_mesh", "name": "Particle Size (mesh)", "type": "number", "validators": [{"type": "range", "min": 10, "max": 300}]}
{"_id": "79f566f783444e35ae48bd33", "key": "bulk_density_gL", "name": "Bulk Density (g/L)", "type": "number", "validators": [{"type": "range", "min": 50, "max": 1200}]}
{"_id": "51435c9621a0403cbff9a6c1", "key": "extraction_yield_pct", "name": "Extraction Yield (%)", "type": "number", "validators": [{"type": "range", "min": 1, "max": 60}]}
