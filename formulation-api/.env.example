# Environment Configuration
NODE_ENV=development

# Server Configuration
PORT=3001

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/agrizy_formulation_dev

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Claude AI Configuration
ANTHROPIC_API_KEY=your-claude-api-key-from-anthropic
CLAUDE_API_KEY=your-claude-api-key-from-anthropic

# CORS Configuration (Frontend URLs)
FRONTEND_URL=http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX_REQUESTS=5

# Logging
LOG_LEVEL=info
LOG_TO_FILE=true
LOG_FILE_PATH=../logs/backend.log
LOG_ERROR_FILE_PATH=../logs/backend-error.log
LOG_MAX_FILE_SIZE=10485760
LOG_MAX_FILES=5
FRONTEND_LOG_PATH=../logs/frontend.log

# Security
BCRYPT_SALT_ROUNDS=12