# MongoDB Migration Plan

## Current Pain Points with MySQL
1. **Complex JSON Storage**: Heavy reliance on JSON fields defeats relational benefits
2. **Deep Nesting**: Formulation data is naturally hierarchical (recipes → ingredients → suppliers)
3. **Schema Rigidity**: AI-generated formulations may have varying field structures
4. **Query Complexity**: Complex JSON queries are inefficient in MySQL
5. **Redis Dependency**: Currently using Redis for caching what should be database operations

## Proposed MongoDB Schema

### 1. Users Collection
```javascript
{
  _id: ObjectId,
  email: String,
  password_hash: String,
  first_name: String,
  last_name: String,
  company: String,
  role: String,
  is_active: Boolean,
  email_verified: Boolean,
  created_at: Date,
  updated_at: Date,
  last_login: Date,
  preferences: {
    default_industry: String,
    notification_settings: Object,
    ui_preferences: Object
  }
}
```

### 2. Projects Collection
```javascript
{
  _id: ObjectId,
  user_id: ObjectId, // Reference to users
  name: String,
  description: String,
  industry: String, // beverages, nutraceuticals, cosmetics
  product_type: String,
  custom_product_type: String,
  status: String, // draft, in_progress, completed, archived
  
  // Wizard Data
  goals: {
    budget: Number,
    nutrition_targets: Object,
    sustainability: String,
    compliance: String
  },
  constraints: {
    allergies: [String],
    certifications_needed: [String],
    regulatory_requirements: Object
  },
  branding: {
    colors: Object,
    personas: [Object],
    values: [String]
  },
  gtm_strategy: {
    channels: [String],
    marketing_focus: [String],
    launch_timeline: Object
  },
  
  // Formulation Data (embedded for performance)
  current_formulation: {
    version: Number,
    generated_at: Date,
    quality_score: Number,
    quality_level: String, // excellent, good, acceptable, needs_optimization
    ready_for_results: Boolean,
    
    // Main Recipe
    main_recipe: {
      id: String,
      name: String,
      description: String,
      target_market: String,
      ingredients: [{
        name: String,
        percentage: Number,
        function: String,
        cost_per_kg: Number,
        supplier_suggestions: [{
          name: String,
          location: String,
          quality_grade: String,
          certification: String,
          lead_time_days: Number
        }],
        sourcing_region: String,
        alternative_names: [String],
        cas_number: String,
        nutritional_contribution: String
      }],
      nutritional_profile: {
        macronutrients: {
          protein_g: Number,
          carbohydrates_g: Number,
          fat_g: Number,
          fiber_g: Number,
          calories_per_serving: Number
        },
        micronutrients: {
          vitamin_c_mg: Number,
          vitamin_d_iu: Number,
          calcium_mg: Number,
          iron_mg: Number,
          magnesium_mg: Number
        },
        bioactive_compounds: {
          antioxidants_orac: Number,
          polyphenols_mg: Number,
          omega3_mg: Number
        },
        daily_value_percentages: Object
      },
      production_specs: {
        batch_size: String,
        shelf_life: String,
        storage_conditions: String,
        manufacturing_process: String,
        quality_control: String,
        packaging_requirements: String
      },
      compliance_details: {
        regulatory_status: {
          fda: String,
          fssai: String,
          eu: String,
          organic: String
        },
        certifications_needed: [String],
        labeling_requirements: String,
        health_claims: String,
        allergen_warnings: String
      },
      cost_analysis: {
        raw_material_cost: Number,
        processing_cost: Number,
        packaging_cost: Number,
        total_cogs: Number,
        suggested_retail: Number,
        margin_percentage: Number,
        break_even_volume: Number,
        cost_breakdown: Object
      },
      scores: {
        nutrition: Number,
        sustainability: Number,
        cost_efficiency: Number,
        compliance: Number,
        market_appeal: Number,
        innovation_index: Number,
        scalability: Number
      },
      sustainability_metrics: {
        carbon_footprint_kg: Number,
        water_usage_liters: Number,
        renewable_energy_percentage: Number,
        sustainable_sourcing_percentage: Number,
        packaging_recyclability: Number,
        biodegradability_score: Number
      }
    },
    
    // Variations (3 variations)
    variations: [{
      id: String,
      name: String,
      variation_type: String,
      target_geography: String,
      key_differences: String,
      ingredients: [Object], // Same structure as main_recipe.ingredients
      traditional_benefits: String,
      innovation_highlights: String,
      cost_analysis: Object, // Simplified version
      scores: Object // Same structure as main_recipe.scores
    }],
    
    // Market Analysis
    market_analysis: {
      target_segments: [{
        segment: String,
        size_percentage: Number,
        willingness_to_pay: String,
        key_motivators: [String]
      }],
      competitive_landscape: [{
        competitor: String,
        price_point: String,
        key_differentiator: String
      }],
      market_opportunity: {
        tam_size_crores: Number,
        growth_rate_cagr: Number,
        entry_barriers: String
      }
    },
    
    // Regulatory Pathway
    regulatory_pathway: {
      approval_timeline: String,
      required_studies: [String],
      regulatory_costs: String,
      key_milestones: [String]
    },
    
    // Manufacturing Recommendations
    manufacturing_recommendations: {
      preferred_locations: [String],
      equipment_requirements: String,
      quality_standards: String,
      capacity_planning: String
    },
    
    // Validation Errors (for quality assessment)
    validation_errors: [{
      field: String,
      message: String
    }]
  },
  
  // Conversation History (playground chat)
  conversation_history: [{
    role: String, // user | assistant
    message: String,
    action: String, // modify, fix, rethink, etc.
    timestamp: Date,
    changes: [String], // List of changes made
    version: Number // Which formulation version this applies to
  }],
  
  // Version History
  formulation_versions: [{
    version: Number,
    formulation_data: Object, // Snapshot of formulation at that version
    created_at: Date,
    changes_summary: String
  }],
  
  // Metadata
  is_active: Boolean,
  last_accessed: Date,
  created_at: Date,
  updated_at: Date
}
```

### 3. Sessions Collection (for authentication)
```javascript
{
  _id: ObjectId,
  session_id: String,
  user_id: ObjectId,
  data: Object,
  expires_at: Date,
  created_at: Date
}
```

## Migration Benefits

### 1. **Simplified Architecture**
- **Remove Redis dependency** for formulation caching (everything in MongoDB)
- **Single source of truth** for all project and formulation data
- **Atomic operations** for updating projects with formulations

### 2. **Better Performance**
- **Embedded formulations** eliminate joins
- **Indexes on nested fields** for fast queries
- **Aggregation pipeline** for complex analytics

### 3. **Enhanced Queries**
```javascript
// Find projects with high-scoring formulations
db.projects.find({
  "current_formulation.quality_score": { $gte: 85 },
  "current_formulation.ready_for_results": true
})

// Find projects by ingredient
db.projects.find({
  "current_formulation.main_recipe.ingredients.name": "Pomegranate Extract"
})

// Aggregate cost analysis across projects
db.projects.aggregate([
  { $match: { "user_id": ObjectId("...") } },
  { $group: { 
      _id: "$industry", 
      avg_cost: { $avg: "$current_formulation.main_recipe.cost_analysis.total_cogs" }
    }}
])
```

### 4. **Flexible Schema Evolution**
- AI formulations can have new fields without schema changes
- Easy to add new formulation components
- Version compatibility maintained automatically

## Implementation Plan

### Phase 1: Setup MongoDB (1-2 days)
1. Install MongoDB and Mongoose
2. Create connection configuration
3. Define Mongoose schemas
4. Setup indexes

### Phase 2: Data Migration (2-3 days)
1. Export existing MySQL data
2. Transform data to MongoDB structure
3. Import data with proper relationships
4. Verify data integrity

### Phase 3: Update API Layer (3-4 days)
1. Replace Sequelize with Mongoose models
2. Update all CRUD operations
3. Optimize queries for MongoDB
4. Remove Redis caching logic

### Phase 4: Testing & Optimization (2-3 days)
1. Update all unit tests
2. Performance testing
3. Index optimization
4. Error handling improvements

## Files to Update
- `/backend/src/models/` - Replace with Mongoose models
- `/backend/src/config/database.js` - MongoDB connection
- `/backend/src/routes/` - Update all routes
- `/backend/src/services/` - Remove Redis dependencies
- `/backend/package.json` - Update dependencies

## Risk Mitigation
1. **Backup current MySQL data** before migration
2. **Parallel testing** during development
3. **Gradual rollout** with feature flags
4. **Rollback plan** if issues arise

Would you like me to proceed with implementing this MongoDB migration?