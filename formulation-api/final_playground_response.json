{"success": true, "projectId": "68be7dada3491b9e3c9a91da", "data": {"components": {"mainRecipe": null, "recipes": [], "nutritionalProfile": null, "costAnalysis": null, "manufacturingProcedure": null, "bomDetails": null, "complianceDetails": null, "marketAnalysis": null, "errors": {"mainRecipe": "must NOT have fewer than 4 items", "recipes": "must NOT have fewer than 4 items"}}, "validationErrors": [{"field": "/recipes", "message": "must NOT have fewer than 4 items"}], "hasErrors": true, "qualityScore": 0, "qualityLevel": "needs_optimization", "readyForResults": false, "recommendPlayground": true, "qualityIssues": ["Missing main recipe", "Insufficient recipes (minimum 2 required)", "Missing cost analysis", "Missing manufacturing procedure", "1 validation errors present"], "version": 1, "formulation": {"version": 1, "generated_at": "2025-09-08T06:55:06.512Z", "quality_score": 0, "quality_level": "needs_optimization", "ready_for_results": false, "recipes": [], "validation_errors": [{"field": "/recipes", "message": "must NOT have fewer than 4 items"}], "conversation_history": [{"role": "system", "message": "Initial formulation generated", "timestamp": "2025-09-08T06:55:06.512Z", "raw_response": "Claude AI formulation generation failed: Claude response validation failed: /recipes: must NOT have fewer than 4 items"}]}}}