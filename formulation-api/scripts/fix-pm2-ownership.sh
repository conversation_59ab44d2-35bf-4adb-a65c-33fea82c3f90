#!/bin/bash

# Fix PM2 ownership and permissions script
# This script ensures PM2 processes and application files have consistent ownership

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="${APP_DIR:-$(pwd)}"
TARGET_USER="${TARGET_USER:-ubuntu}"
PM2_USER="${PM2_USER:-ubuntu}"

echo -e "${BLUE}🔧 PM2 Ownership Fix Script${NC}"
echo -e "${BLUE}===============================${NC}"
echo "App Directory: $APP_DIR"
echo "Target User: $TARGET_USER"
echo "PM2 User: $PM2_USER"
echo

# Function to check if user exists
check_user() {
    if ! id "$1" &>/dev/null; then
        echo -e "${RED}❌ User '$1' does not exist${NC}"
        exit 1
    fi
}

# Function to check if PM2 is running
check_pm2() {
    if ! command -v pm2 &> /dev/null; then
        echo -e "${RED}❌ PM2 is not installed${NC}"
        exit 1
    fi
}

# Function to get current PM2 processes user
get_pm2_user() {
    local pm2_processes=$(ps aux | grep "[p]m2" | head -1 | awk '{print $1}')
    if [ ! -z "$pm2_processes" ]; then
        echo "$pm2_processes"
    else
        echo "none"
    fi
}

# Function to stop PM2 processes
stop_pm2() {
    echo -e "${YELLOW}🛑 Stopping PM2 processes...${NC}"
    
    # Try to stop as current user first
    if pm2 list | grep -q "online\|stopping\|errored"; then
        pm2 stop all 2>/dev/null || true
        pm2 delete all 2>/dev/null || true
    fi
    
    # If PM2 is running as different user, try to stop as that user
    local current_pm2_user=$(get_pm2_user)
    if [ "$current_pm2_user" != "none" ] && [ "$current_pm2_user" != "$(whoami)" ]; then
        echo "Stopping PM2 processes running as $current_pm2_user..."
        sudo -u "$current_pm2_user" pm2 stop all 2>/dev/null || true
        sudo -u "$current_pm2_user" pm2 delete all 2>/dev/null || true
    fi
    
    # Force kill any remaining PM2 processes
    sudo pkill -f "pm2" 2>/dev/null || true
    sleep 2
    
    echo -e "${GREEN}✅ PM2 processes stopped${NC}"
}

# Function to change file ownership
fix_ownership() {
    echo -e "${YELLOW}📁 Fixing file ownership...${NC}"
    
    # Change ownership of application files
    sudo chown -R "$TARGET_USER:$TARGET_USER" "$APP_DIR"
    
    # Fix permissions
    find "$APP_DIR" -type f -name "*.js" -exec chmod 644 {} \;
    find "$APP_DIR" -type f -name "*.json" -exec chmod 644 {} \;
    find "$APP_DIR" -type f -name "*.sh" -exec chmod 755 {} \;
    find "$APP_DIR" -type d -exec chmod 755 {} \;
    
    # Make sure logs directory is writable
    if [ -d "$APP_DIR/logs" ]; then
        chmod 755 "$APP_DIR/logs"
        find "$APP_DIR/logs" -type f -exec chmod 644 {} \;
    fi
    
    # Fix node_modules permissions if exists
    if [ -d "$APP_DIR/node_modules" ]; then
        find "$APP_DIR/node_modules" -type d -exec chmod 755 {} \;
        find "$APP_DIR/node_modules" -type f -exec chmod 644 {} \;
        # Make binaries executable
        find "$APP_DIR/node_modules/.bin" -type f -exec chmod 755 {} \; 2>/dev/null || true
    fi
    
    echo -e "${GREEN}✅ File ownership fixed${NC}"
}

# Function to fix PM2 home directory
fix_pm2_home() {
    echo -e "${YELLOW}🏠 Fixing PM2 home directory...${NC}"
    
    local pm2_home="/home/<USER>/.pm2"
    
    # Create PM2 home if it doesn't exist
    if [ ! -d "$pm2_home" ]; then
        sudo -u "$PM2_USER" mkdir -p "$pm2_home"
    fi
    
    # Fix PM2 directory ownership
    sudo chown -R "$PM2_USER:$PM2_USER" "$pm2_home"
    chmod 755 "$pm2_home"
    
    # Fix PM2 log directories
    sudo -u "$PM2_USER" mkdir -p "$pm2_home/logs" "$pm2_home/pids"
    
    echo -e "${GREEN}✅ PM2 home directory fixed${NC}"
}

# Function to start PM2 as correct user
start_pm2() {
    echo -e "${YELLOW}🚀 Starting PM2 as $PM2_USER...${NC}"
    
    # Change to app directory
    cd "$APP_DIR"
    
    # Start PM2 as the target user
    if [ -f "ecosystem.config.js" ]; then
        echo "Starting with ecosystem.config.js..."
        sudo -u "$PM2_USER" HOME="/home/<USER>" pm2 start ecosystem.config.js
    elif [ -f "package.json" ] && grep -q '"start"' package.json; then
        echo "Starting with npm start..."
        sudo -u "$PM2_USER" HOME="/home/<USER>" pm2 start npm --name "formulation-api" -- start
    else
        echo "Starting server.js directly..."
        sudo -u "$PM2_USER" HOME="/home/<USER>" pm2 start server.js --name "formulation-api"
    fi
    
    # Save PM2 configuration
    sudo -u "$PM2_USER" HOME="/home/<USER>" pm2 save
    
    # Setup PM2 startup script
    local startup_cmd=$(sudo -u "$PM2_USER" HOME="/home/<USER>" pm2 startup | grep "sudo env" | head -1)
    if [ ! -z "$startup_cmd" ]; then
        echo "Setting up PM2 startup script..."
        eval "$startup_cmd"
    fi
    
    echo -e "${GREEN}✅ PM2 started successfully${NC}"
}

# Function to verify setup
verify_setup() {
    echo -e "${YELLOW}🔍 Verifying setup...${NC}"
    
    # Check file ownership
    local app_owner=$(stat -c "%U" "$APP_DIR" 2>/dev/null || stat -f "%Su" "$APP_DIR")
    echo "App directory owner: $app_owner"
    
    # Check PM2 processes
    local pm2_running_user=$(get_pm2_user)
    echo "PM2 running as: $pm2_running_user"
    
    # List PM2 processes
    echo -e "${BLUE}PM2 Process List:${NC}"
    sudo -u "$PM2_USER" HOME="/home/<USER>" pm2 list
    
    if [ "$app_owner" = "$TARGET_USER" ] && [ "$pm2_running_user" = "$PM2_USER" ]; then
        echo -e "${GREEN}✅ All ownership and permissions are correct!${NC}"
        return 0
    else
        echo -e "${RED}❌ There are still ownership issues${NC}"
        return 1
    fi
}

# Main execution
main() {
    echo -e "${BLUE}Starting PM2 ownership fix...${NC}\n"
    
    # Checks
    check_user "$TARGET_USER"
    check_user "$PM2_USER"
    check_pm2
    
    # Check if running as root or with sudo access
    if [ "$EUID" -eq 0 ] || sudo -n true 2>/dev/null; then
        echo -e "${GREEN}✅ Running with sufficient privileges${NC}"
    else
        echo -e "${RED}❌ This script requires sudo access${NC}"
        exit 1
    fi
    
    # Confirm before proceeding
    read -p "This will stop PM2, change ownership, and restart. Continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Cancelled."
        exit 0
    fi
    
    # Execute steps
    stop_pm2
    fix_ownership
    fix_pm2_home
    start_pm2
    
    echo
    verify_setup
    
    if [ $? -eq 0 ]; then
        echo -e "\n${GREEN}🎉 PM2 ownership fix completed successfully!${NC}"
        echo -e "${BLUE}Useful commands:${NC}"
        echo "  Monitor processes: sudo -u $PM2_USER pm2 monit"
        echo "  View logs: sudo -u $PM2_USER pm2 logs"
        echo "  Restart app: sudo -u $PM2_USER pm2 restart formulation-api"
    else
        echo -e "\n${RED}❌ Fix completed but there may be issues${NC}"
        exit 1
    fi
}

# Handle command line arguments
case "${1:-main}" in
    "check")
        echo "Current status:"
        echo "App owner: $(stat -c "%U" "$APP_DIR" 2>/dev/null || stat -f "%Su" "$APP_DIR")"
        echo "PM2 user: $(get_pm2_user)"
        verify_setup
        ;;
    "stop")
        stop_pm2
        ;;
    "start")
        start_pm2
        ;;
    "main"|"")
        main
        ;;
    *)
        echo "Usage: $0 [check|stop|start]"
        echo "  check - Check current ownership status"
        echo "  stop  - Stop PM2 processes only"
        echo "  start - Start PM2 processes only"
        echo "  (no args) - Run full fix process"
        ;;
esac