#!/usr/bin/env node

/**
 * Create Admin User for Development
 * Creates an admin user in the database for API testing
 */

require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

async function createAdminUser() {
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/agrizy_formulation_dev';
    await mongoose.connect(mongoUri);
    
    const db = mongoose.connection.db;
    console.log('✅ Connected to MongoDB');

    // Hash password
    const password = 'Asdram123@';
    const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Admin user data
    const adminUser = {
      email: '<EMAIL>',
      password_hash: passwordHash,
      first_name: 'Admin',
      last_name: 'User',
      company: 'Agrizy',
      role: 'admin',
      is_active: true,
      preferences: {
        notification_settings: {
          email: true,
          push: false
        },
        ui_preferences: {
          theme: 'light',
          language: 'en'
        }
      },
      created_at: new Date(),
      updated_at: new Date()
    };

    // Insert or update admin user
    const usersCollection = db.collection('users');
    const result = await usersCollection.updateOne(
      { email: adminUser.email },
      { $set: adminUser },
      { upsert: true }
    );

    if (result.upsertedCount > 0) {
      console.log('✅ Admin user created successfully');
    } else {
      console.log('✅ Admin user updated successfully');
    }

    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: Asdram123@');
    console.log('\nYou can now use these credentials to log in and test the API');

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the script
createAdminUser();