#!/usr/bin/env node
/*
 * Fix numeric range constraints for known parameters across param_bindings.
 * Currently adjusts pH target to 0–14 with step 0.1.
 *
 * Usage:
 *  node scripts/fix-numeric-ranges.js            # dry-run (default)
 *  node scripts/fix-numeric-ranges.js --commit   # apply changes
 */

require('dotenv').config();
const mongoose = require('mongoose');
const yargs = require('yargs/yargs');

const argv = yargs(process.argv.slice(2))
  .option('commit', { type: 'boolean', default: false, describe: 'Persist changes to DB' })
  .option('dry-run', { type: 'boolean', default: true, describe: 'Simulate without writing' })
  .help()
  .argv;

if (argv.commit === true) argv['dry-run'] = false;
const commit = argv.commit && !argv['dry-run'];

const CONFIG = {
  // Cosmetics / skincare
  ph_target:          { min: 0,   max: 14,   step: 0.1, unit: 'pH' },
  preservative_load_pct: { min: 0,   max: 2,    step: 0.1, unit: '%' },
  fragrance_load_pct: { min: 0,   max: 2,    step: 0.1, unit: '%' },
  oil_phase_pct:      { min: 0,   max: 50,   step: 1,   unit: '%' },
  water_phase_pct:    { min: 0,   max: 100,  step: 1,   unit: '%' },
  viscosity_cps:      { min: 10,  max: 200000, step: 10, unit: 'cps' },
  spf_target:         { min: 0,   max: 100,  step: 1,   unit: 'SPF' },
  uva_pf_target:      { min: 0,   max: 50,   step: 1,   unit: 'UVA-PF' },
  pigment_load_pct:   { min: 0,   max: 25,   step: 0.5, unit: '%' },

  // Nutraceuticals / foods
  serving_size_g:       { min: 0.1, max: 100,  step: 0.1, unit: 'g' },
  sugar_pct:            { min: 0,   max: 100,  step: 0.5, unit: '%' },
  protein_pct:          { min: 0,   max: 100,  step: 0.5, unit: '%' },
  fat_pct:              { min: 0,   max: 100,  step: 0.5, unit: '%' },
  fiber_pct:            { min: 0,   max: 100,  step: 0.5, unit: '%' },
  moisture_max_pct:     { min: 0,   max: 20,   step: 0.1, unit: '%' },
  sodium_mg:            { min: 0,   max: 2000, step: 10,  unit: 'mg' },
  disintegration_time_min: { min: 0, max: 60, step: 1, unit: 'min' },
};

async function main() {
  const MONGODB_URI = process.env.MONGODB_URI || process.env.MONGO_URL || 'mongodb://localhost:27017/agrizy_formulation_dev';
  console.log(`→ Connecting MongoDB @ ${MONGODB_URI}`);
  await mongoose.connect(MONGODB_URI, { dbName: undefined });
  const db = mongoose.connection.db;

  const bindingsCol = db.collection('param_bindings');

  let totalWouldUpdate = 0;
  for (const [key, cfg] of Object.entries(CONFIG)) {
    const q = { parameter_key: key, status: { $ne: 'inactive' } };
    const docs = await bindingsCol.find(q).project({ _id: 1, widget: 1, validators: 1, guidance: 1 }).toArray();
    if (!docs.length) continue;
    console.log(`→ Found ${docs.length} ${key} binding(s)`);

    let changed = 0;
    for (const b of docs) {
      const w = b.widget || {};
      const v = b.validators || {};
      const g = b.guidance || {};
      const needType = w.type !== 'slider';
      const needMin = w.min === undefined || Number(w.min) !== cfg.min;
      const needMax = w.max === undefined || Number(w.max) !== cfg.max;
      const needStep = w.step === undefined || Number(w.step) !== cfg.step;
      const needVMin = v.min === undefined || Number(v.min) !== cfg.min;
      const needVMax = v.max === undefined || Number(v.max) !== cfg.max;
      const needUnit = cfg.unit && (!g.unit || g.unit !== cfg.unit);

      if (needType || needMin || needMax || needStep || needVMin || needVMax || needUnit) {
        changed++;
        if (commit) {
          const set = {
            'widget.type': 'slider',
            'widget.min': cfg.min,
            'widget.max': cfg.max,
            'widget.step': cfg.step,
            'validators.min': cfg.min,
            'validators.max': cfg.max,
          };
          if (needUnit) set['guidance.unit'] = cfg.unit;
          await bindingsCol.updateOne(
            { _id: b._id },
            { $set: set, $push: { audit: { ts: new Date(), action: 'fix-range', key, details: `Set ${cfg.min}–${cfg.max} step ${cfg.step}${cfg.unit ? ' '+cfg.unit : ''}` } } }
          );
        }
      }
    }
    totalWouldUpdate += changed;
    console.log(`✔ ${changed} ${key} binding(s) ${commit ? 'updated' : 'would be updated'} to ${cfg.min}–${cfg.max}`);
  }

  await mongoose.disconnect();
  console.log(`✔ Done. ${commit ? 'Changes persisted.' : 'No changes made (dry-run).'} (${totalWouldUpdate} total ${commit ? 'updated' : 'would be updated'})`);
}

main().catch(err => {
  console.error('Script failed:', err);
  process.exit(1);
});
