#!/usr/bin/env node
/**
 * Minimal job worker for MongoDB-backed queue
 * Processes 'generate_project' jobs
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });
const { connect } = require('../src/models');
const { mongoose } = require('../src/config/mongodb');
const QueueService = require('../src/services/queue.service');
const Project = require('../src/models/mongo/Project');
const formulationService = require('../src/services/formulation.service');

const WORKER_ID = `worker_${Math.random().toString(36).slice(2, 8)}`;

async function processGenerateProject(job) {
  const { projectId, payload } = job;
  const project = await Project.findById(projectId);
  if (!project) throw new Error(`Project ${projectId} not found`);

  // Mark project as generating
  project.status = 'generating';
  project.generation_started_at = new Date();
  await project.save();

  await formulationService.generateAndPersist({
    projectId: project._id,
    userId: payload.userId,
    formData: {
      industry: payload.industry,
      productType: payload.sub_category || payload.category || project.product_type,
      goals: payload.goals,
      productDescription: project.custom_product_type
    }
  });
}

async function main() {
  await connect(process.env.NODE_ENV || 'development');
  const db = mongoose.connection.db;
  const queue = new QueueService(db);
  await queue.ensureIndexes();

  console.log(`[Worker ${WORKER_ID}] started`);

  while (true) {
    try {
      const job = await queue.claimNext(WORKER_ID);
    
    if (!job) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      continue;
    }
    
    console.log(`[Worker ${WORKER_ID}] Claimed job:`, job._id, 'type:', job.type);

      try {
        if (job.type === 'generate_project') {
          await processGenerateProject(job);
        } else {
          throw new Error(`Unknown job type: ${job.type}`);
        }
        await queue.complete(job._id, { ok: true });
      } catch (err) {
        console.error(`[Worker ${WORKER_ID}] job failed`, err);
        
        // Check if this is the final attempt
        const isFinalAttempt = job.attempts + 1 >= job.maxAttempts;
        
        if (isFinalAttempt && job.type === 'generate_project' && job.projectId) {
          // Update project status to failed on final attempt
          try {
            await Project.updateOne(
              { _id: job.projectId },
              { 
                status: 'failed',
                generation_failed_at: new Date(),
                failure_reason: err.message
              }
            );
            console.log(`[Worker ${WORKER_ID}] Project ${job.projectId} marked as failed`);
          } catch (updateErr) {
            console.error(`[Worker ${WORKER_ID}] Failed to update project status:`, updateErr);
          }
        }
        
        await queue.fail(job._id, err, job.attempts, job.maxAttempts);
      }
    } catch (loopErr) {
      console.error('Worker loop error:', loopErr);
      await new Promise(r => setTimeout(r, 2000));
    }
  }
}

main().catch(e => {
  console.error('Worker fatal error:', e);
  process.exit(1);
});
