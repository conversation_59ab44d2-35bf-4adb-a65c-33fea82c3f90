const { connect } = require('../src/models');
const { mongoose } = require('../src/config/mongodb');

async function examineDataStructure() {
  try {
    console.log('Connecting to MongoDB...');
    await connect('development');
    
    const db = mongoose.connection.db;
    
    console.log('\n=== EXAMINING PARAMETER BINDINGS STRUCTURE ===');
    const bindings = await db.collection('param_bindings').find().limit(3).toArray();
    console.log('Sample bindings:');
    bindings.forEach((b, i) => {
      console.log(`${i+1}:`, JSON.stringify(b, null, 2));
    });
    
    console.log('\n=== EXAMINING TAXONOMIES STRUCTURE ===');
    const taxonomies = await db.collection('taxonomies').find().limit(3).toArray();
    console.log('Sample taxonomies:');
    taxonomies.forEach((t, i) => {
      console.log(`${i+1}:`, JSON.stringify(t, null, 2));
    });
    
    console.log('\n=== EXAMINING PARAMETERS STRUCTURE ===');
    const parameters = await db.collection('parameters').find().limit(3).toArray();
    console.log('Sample parameters:');
    parameters.forEach((p, i) => {
      console.log(`${i+1}:`, JSON.stringify(p, null, 2));
    });
    
    // Check if there are any bindings with taxonomy_id field
    const bindingsWithTaxonomyId = await db.collection('param_bindings').find({
      taxonomy_id: { $exists: true }
    }).limit(5).toArray();
    
    console.log(`\n=== BINDINGS WITH taxonomy_id FIELD ===`);
    console.log(`Found ${bindingsWithTaxonomyId.length} bindings with taxonomy_id`);
    bindingsWithTaxonomyId.forEach((b, i) => {
      console.log(`${i+1}: taxonomy_id = ${b.taxonomy_id} (type: ${typeof b.taxonomy_id})`);
    });
    
    // Check the actual field names in bindings
    const sampleBinding = await db.collection('param_bindings').findOne();
    if (sampleBinding) {
      console.log('\n=== BINDING FIELD NAMES ===');
      console.log('Fields in sample binding:', Object.keys(sampleBinding));
    }
    
  } catch (error) {
    console.error('Error examining data structure:', error);
  } finally {
    await mongoose.connection.close();
    process.exit(0);
  }
}

examineDataStructure();