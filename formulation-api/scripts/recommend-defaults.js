#!/usr/bin/env node
/*
 * Recommend parameter defaults via <PERSON> and persist to MongoDB
 *
 * Usage:
 *  node scripts/recommend-defaults.js --commit               # write changes
 *  node scripts/recommend-defaults.js --level sub_category   # filter level
 *  node scripts/recommend-defaults.js --slug face-serum      # filter slug
 *  node scripts/recommend-defaults.js --limit 20 --commit    # limit targets
 *  node scripts/recommend-defaults.js --dry-run              # simulate (default)
 */

require('dotenv').config();
const mongoose = require('mongoose');
const Anthropic = require('@anthropic-ai/sdk');

const ParametersService = require('../src/services/parameters.service');
const TaxonomiesService = require('../src/services/taxonomies.service');

const CLAUDE_MODEL = process.env.CLAUDE_MODEL || 'claude-3-5-sonnet-20240620';

// Strip a lone "--" passed by some npm/pnpm runners so both
//   pnpm run recommend:defaults -- --level ...
// and
//   node scripts/recommend-defaults.js --level ...
// work identically.
const cleanedArgvInput = process.argv.slice(2).filter(a => a !== '--');

const argv = require('yargs/yargs')(cleanedArgvInput)
  .option('commit', { type: 'boolean', default: false, describe: 'Persist changes to DB' })
  .alias('commit', ['comit']) // accept common typo
  .option('dry-run', { type: 'boolean', default: true, describe: 'Do not write to DB' })
  .option('level', { type: 'string', choices: ['industry', 'category', 'sub_category'], describe: 'Target level filter' })
  .option('slug', { type: 'string', describe: 'Target slug filter' })
  .option('limit', { type: 'number', default: 0, describe: 'Limit number of targets' })
  .help()
  .argv;

async function main() {
  const MONGODB_URI = process.env.MONGODB_URI || process.env.MONGO_URL || 'mongodb://localhost:27017/agrizy_formulation_dev';
  const client = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY || process.env.CLAUDE_API_KEY });

  // If --commit is provided, override dry-run automatically
  if (argv.commit === true) argv['dry-run'] = false;
  const commit = argv.commit && !argv['dry-run'];

  console.log(`→ Connecting MongoDB @ ${MONGODB_URI}`);
  await mongoose.connect(MONGODB_URI, { dbName: undefined });
  const db = mongoose.connection.db;

  const paramsService = new ParametersService(db);
  const taxService = new TaxonomiesService(db);
  const bindingsCol = db.collection('param_bindings');

  // Discover unique targets from bindings
  const match = { status: { $ne: 'inactive' } };
  if (argv.level) match['target.level'] = argv.level;
  if (argv.slug) match['target.slug'] = argv.slug;

  const targets = await bindingsCol.aggregate([
    { $match: match },
    { $group: { _id: '$target', count: { $sum: 1 } } },
    { $sort: { count: -1 } }
  ]).toArray();

  const totalTargets = argv.limit ? Math.min(argv.limit, targets.length) : targets.length;
  console.log(`→ Found ${targets.length} targets; processing ${totalTargets}`);

  const UI_ONLY_KEYS = new Set(['active_panel']);

  let updates = 0;
  for (let i = 0; i < totalTargets; i++) {
    const target = targets[i]._id; // { level, slug }
    const level = target.level;
    const slug = target.slug;

    // Build taxonomy context (industry/category/sub_category)
    let industry = null, category = null, subCategory = null;
    try {
      if (level === 'sub_category') {
        const h = await taxService.getHierarchy(slug);
        industry = h.industry.slug; category = h.category.slug; subCategory = h.sub_category.slug;
      } else if (level === 'category') {
        const cat = await taxService.getBySlug(slug);
        const ind = await db.collection('taxonomies').findOne({ _id: cat.parent_id });
        industry = ind?.slug || null; category = cat.slug; subCategory = null;
      } else if (level === 'industry') {
        industry = slug; category = null; subCategory = null;
      }
    } catch (e) {
      console.warn(`! Skipping taxonomy context for ${level}:${slug} → ${e.message}`);
    }

    console.log(`\n▶ Target ${i + 1}/${totalTargets}: ${level}:${slug}`);

    // Load merged bindings (parameter + binding)
    const merged = await paramsService.getBindingsForTarget(level, slug);

    // Deduplicate by parameter_key to avoid double prompting/logging.
    // If duplicates exist, we'll still update all occurrences in DB via updateMany below.
    const byKey = new Map();
    for (const m of merged) {
      if (UI_ONLY_KEYS.has(m.parameter_key)) continue; // exclude UI-only bindings
      if (!byKey.has(m.parameter_key)) {
        byKey.set(m.parameter_key, m);
      }
    }
    const dupCount = merged.length - byKey.size;
    if (dupCount > 0) {
      console.warn(`  ⚠️ Found ${dupCount} duplicate binding(s); will prompt once per parameter and update all duplicates consistently.`);
    }
    const uniqueMerged = Array.from(byKey.values());

    // Optional: get answers options in batch
    const answerKeys = Array.from(new Set(merged.map(m => m.answers_key).filter(Boolean)));
    const answerMap = answerKeys.length ? await paramsService.getMultipleAnswerSets(answerKeys) : {};

    // Process each binding
    for (const mb of uniqueMerged) {
      const options = mb.answers_key ? (answerMap[mb.answers_key]?.options || []) : [];

      // Build a compact prompt for Claude
      const prompt = `You are an expert formulation scientist. Based on the parameter context, return a single JSON object with recommended default values for product configuration.\n\nContext:\n- Industry: ${industry || 'n/a'}\n- Category: ${category || 'n/a'}\n- Sub-category: ${subCategory || 'n/a'}\n- Parameter key: ${mb.parameter_key}\n- Label: ${mb.name}\n- Type: ${mb.type}\n- Widget: ${mb.widget?.type || 'text'}\n- Validators: ${JSON.stringify(mb.validators || {})}\n- Guidance: ${JSON.stringify(mb.guidance || {})}\n- Unit: ${(mb.guidance && mb.guidance.unit) ? mb.guidance.unit : ''}\n- Options: ${JSON.stringify(options)}\n\nRules:\n- If type is number/slider, return a value within [min,max].\n- If enum/select, choose one of the provided options (use option.value).\n- Keep culturally/regionally neutral choices if uncertain.\n- Avoid unsafe or non-compliant suggestions.\n- Provide a short rationale.\n\nRespond ONLY with JSON having keys: default_value, guidance_average (number optional), rationale.`;

      // Call Claude with retry
      let respText = '';
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          const resp = await client.messages.create({
            model: CLAUDE_MODEL,
            max_tokens: 300,
            temperature: 0.3,
            messages: [{ role: 'user', content: prompt }]
          });
          respText = resp?.content?.[0]?.text || '';
          break;
        } catch (err) {
          const waitMs = 500 * attempt;
          console.warn(`  ⚠️ Claude error (attempt ${attempt}): ${err.message} → retrying in ${waitMs}ms`);
          await new Promise(r => setTimeout(r, waitMs));
        }
      }

      if (!respText) {
        console.warn(`  ✖ No response for ${mb.parameter_key}; skipping`);
        continue;
      }

      // Safe JSON parse
      let parsed = null;
      try {
        const start = respText.indexOf('{');
        const end = respText.lastIndexOf('}');
        parsed = JSON.parse(respText.slice(start, end + 1));
      } catch (e) {
        console.warn(`  ✖ Failed to parse JSON for ${mb.parameter_key}: ${e.message}`);
        continue;
      }

      // Validate & coerce
      let { default_value, guidance_average } = parsed;
      const rationale = parsed.rationale || '';
      const widgetType = mb.widget?.type;
      if (widgetType === 'slider' || widgetType === 'range' || mb.type === 'number') {
        const min = mb.widget?.min ?? mb.validators?.min ?? 0;
        const max = mb.widget?.max ?? mb.validators?.max ?? 100;
        const toNum = (v) => (typeof v === 'number' ? v : Number(String(v).replace(/[^0-9.+-]/g, '')));
        default_value = toNum(default_value);
        if (Number.isFinite(default_value) === false) default_value = undefined;
        if (default_value !== undefined) default_value = Math.min(Math.max(default_value, min), max);
        if (guidance_average !== undefined) {
          guidance_average = toNum(guidance_average);
          if (Number.isFinite(guidance_average)) {
            guidance_average = Math.min(Math.max(guidance_average, min), max);
          } else guidance_average = undefined;
        }
      }
      if ((mb.widget?.type === 'select' || mb.type === 'enum') && options.length) {
        const validValues = new Set(options.map(o => o.value));
        if (!validValues.has(default_value)) {
          // try to map by label
          const hit = options.find(o => String(o.label).toLowerCase() === String(default_value || '').toLowerCase());
          default_value = hit ? hit.value : '';
        }
      }

      // Nothing useful → skip
      if (default_value === undefined || default_value === null || default_value === '') {
        console.log(`  • No default for ${mb.parameter_key}`);
        continue;
      }

      const update = {
        default_value,
      };
      if (guidance_average !== undefined) {
        update['guidance.average'] = guidance_average;
      }

      console.log(`  • ${mb.parameter_key} → default=${JSON.stringify(default_value)}${guidance_average !== undefined ? `, avg=${guidance_average}` : ''}`);

      if (commit) {
        const res = await bindingsCol.updateMany(
          { 'target.level': level, 'target.slug': slug, parameter_key: mb.parameter_key },
          { $set: update, $push: { audit: { ts: new Date(), action: 'recommend-default', rationale: rationale?.slice(0, 500) || '', source: 'claude' } } }
        );
        updates += res.modifiedCount || 0;
      }
    }
  }

  console.log(`\n✔ Done. ${updates} binding(s) ${commit ? 'updated' : 'would be updated (dry-run)'}.
Tip: run with --commit to persist.`);
  await mongoose.disconnect();
}

main().catch(err => {
  console.error('Script failed:', err);
  process.exit(1);
});
