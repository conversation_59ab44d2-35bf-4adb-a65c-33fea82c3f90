const { connect } = require('../src/models');
const { mongoose } = require('../src/config/mongodb');

async function fixBindingReferences() {
  try {
    console.log('Connecting to MongoDB...');
    await connect('development');
    
    const db = mongoose.connection.db;
    
    console.log('\n=== FIXING PARAMETER BINDING REFERENCES ===');
    
    const paramBindings = db.collection('param_bindings');
    const taxonomies = db.collection('taxonomies');
    
    // Get all bindings
    const bindings = await paramBindings.find({}).toArray();
    console.log(`Found ${bindings.length} parameter bindings`);
    
    // Get all taxonomies for reference
    const allTaxonomies = await taxonomies.find({}).toArray();
    const taxonomyMap = new Map();
    allTaxonomies.forEach(tax => {
      taxonomyMap.set(tax._id.toString(), tax);
      if (tax.slug) {
        taxonomyMap.set(tax.slug, tax);
      }
    });
    
    console.log(`Found ${allTaxonomies.length} taxonomies for reference`);
    
    let fixedCount = 0;
    let invalidCount = 0;
    
    for (const binding of bindings) {
      let needsUpdate = false;
      const updates = {};
      
      // Fix taxonomy_id if it's a string
      if (binding.taxonomy_id) {
        if (typeof binding.taxonomy_id === 'string') {
          // Try to find by ObjectId string first
          let taxonomy = taxonomyMap.get(binding.taxonomy_id);
          
          if (!taxonomy) {
            // Try to find by slug
            taxonomy = taxonomyMap.get(binding.taxonomy_id);
          }
          
          if (taxonomy) {
            updates.taxonomy_id = taxonomy._id;
            needsUpdate = true;
          } else {
            console.log(`⚠️  Invalid taxonomy reference: ${binding.taxonomy_id}`);
            invalidCount++;
          }
        }
      }
      
      // Fix parameter_id if it's a string
      if (binding.parameter_id && typeof binding.parameter_id === 'string') {
        try {
          updates.parameter_id = new mongoose.Types.ObjectId(binding.parameter_id);
          needsUpdate = true;
        } catch (error) {
          console.log(`⚠️  Invalid parameter_id: ${binding.parameter_id}`);
        }
      }
      
      if (needsUpdate) {
        await paramBindings.updateOne(
          { _id: binding._id },
          { $set: updates }
        );
        fixedCount++;
      }
    }
    
    console.log(`Fixed ${fixedCount} bindings`);
    console.log(`Found ${invalidCount} invalid references`);
    
    // Verify the fix
    console.log('\n=== VERIFICATION AFTER FIX ===');
    
    const bindingsWithValidTaxonomy = await paramBindings.aggregate([
      {
        $lookup: {
          from: 'taxonomies',
          localField: 'taxonomy_id',
          foreignField: '_id',
          as: 'taxonomy'
        }
      },
      {
        $match: {
          'taxonomy.0': { $exists: true }
        }
      },
      {
        $count: 'validBindings'
      }
    ]).toArray();
    
    const validBindingsCount = bindingsWithValidTaxonomy[0]?.validBindings || 0;
    const totalBindings = await paramBindings.countDocuments();
    console.log(`Valid taxonomy bindings: ${validBindingsCount}/${totalBindings}`);
    
    // Show sample valid bindings
    const sampleValidBindings = await paramBindings.aggregate([
      {
        $lookup: {
          from: 'taxonomies',
          localField: 'taxonomy_id',
          foreignField: '_id',
          as: 'taxonomy'
        }
      },
      {
        $lookup: {
          from: 'parameters',
          localField: 'parameter_id',
          foreignField: '_id',
          as: 'parameter'
        }
      },
      {
        $match: {
          'taxonomy.0': { $exists: true },
          'parameter.0': { $exists: true }
        }
      },
      {
        $limit: 5
      },
      {
        $project: {
          'taxonomy.name': 1,
          'taxonomy.level': 1,
          'parameter.name': 1,
          'parameter.type': 1
        }
      }
    ]).toArray();
    
    console.log('\nSample valid bindings:');
    sampleValidBindings.forEach(binding => {
      const tax = binding.taxonomy[0];
      const param = binding.parameter[0];
      console.log(`  ${tax.level}: ${tax.name} -> ${param.type}: ${param.name}`);
    });
    
  } catch (error) {
    console.error('Error fixing binding references:', error);
  } finally {
    await mongoose.connection.close();
    process.exit(0);
  }
}

fixBindingReferences();