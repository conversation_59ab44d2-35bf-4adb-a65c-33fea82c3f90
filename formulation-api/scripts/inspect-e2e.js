#!/usr/bin/env node
require('dotenv').config();
const { connect } = require('../src/models');
const { mongoose } = require('../src/config/mongodb');

async function main() {
  await connect(process.env.NODE_ENV || 'development');
  const db = mongoose.connection.db;
  const filterId = process.argv[2];
  const jobsQ = filterId ? { projectId: new mongoose.Types.ObjectId(filterId) } : {};
  const projQ = filterId ? { _id: new mongoose.Types.ObjectId(filterId) } : {};

  const jobs = await db.collection('jobs')
    .find(jobsQ)
    .sort({ createdAt: -1 })
    .limit(5)
    .toArray();
  const projects = await db.collection('projects')
    .find(projQ)
    .sort({ updated_at: -1 })
    .limit(filterId ? 1 : 3)
    .project({ name:1, status:1, generation_job_id:1, updated_at:1, description:1 })
    .toArray();
  console.log(JSON.stringify({ filterId: filterId || null, jobs, projects }, null, 2));
  await mongoose.disconnect();
}

main().catch(e => { console.error(e); process.exit(1); });
