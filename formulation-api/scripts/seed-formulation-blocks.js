const { connect, FormulationBlock, FormulationBinding } = require('../src/models/mongo');

const defaultFormulationBlocks = [
  {
    key: 'ingredients',
    name: 'Ingredients',
    description: 'Complete list of ingredients with quantities, percentages, and specifications',
    prompt_instructions: 'Generate a comprehensive ingredients list including active ingredients, base ingredients, preservatives, and additives. Include percentage composition, INCI names, and supplier specifications where applicable.',
    output_format: 'table',
    is_required: true,
    display_order: 1,
    block_type: 'ingredients',
    validation_rules: {
      required_fields: ['ingredient_name', 'percentage', 'function'],
      total_percentage: 100
    }
  },
  {
    key: 'cost_analysis',
    name: 'Cost Analysis',
    description: 'Detailed cost breakdown including raw materials, packaging, and manufacturing costs',
    prompt_instructions: 'Provide comprehensive cost analysis including ingredient costs per unit, packaging costs, manufacturing overhead, and total cost per unit. Include cost optimization suggestions.',
    output_format: 'table',
    is_required: true,
    display_order: 2,
    block_type: 'analysis',
    dependencies: ['ingredients'],
    validation_rules: {
      required_fields: ['item', 'cost_per_unit', 'total_cost'],
      currency: 'USD'
    }
  },
  {
    key: 'nutritional_profile',
    name: 'Nutritional Profile',
    description: 'Nutritional information and health benefits analysis',
    prompt_instructions: 'Generate detailed nutritional profile including macronutrients, micronutrients, calories, and health benefits. Include regulatory compliance for nutritional claims.',
    output_format: 'structured_list',
    is_required: false,
    display_order: 3,
    block_type: 'analysis',
    dependencies: ['ingredients']
  },
  {
    key: 'compliance',
    name: 'Compliance',
    description: 'Regulatory compliance and safety information',
    prompt_instructions: 'Provide comprehensive compliance information including FDA regulations, international standards, safety data, allergen information, and required labeling.',
    output_format: 'structured_list',
    is_required: true,
    display_order: 4,
    block_type: 'compliance',
    dependencies: ['ingredients']
  },
  {
    key: 'manufacturing',
    name: 'Manufacturing',
    description: 'Manufacturing process, equipment requirements, and production guidelines',
    prompt_instructions: 'Detail the manufacturing process including step-by-step procedures, equipment requirements, temperature controls, mixing times, and quality control checkpoints.',
    output_format: 'structured_process',
    is_required: true,
    display_order: 5,
    block_type: 'manufacturing',
    dependencies: ['ingredients']
  },
  {
    key: 'sustainability',
    name: 'Sustainability',
    description: 'Environmental impact assessment and sustainability metrics',
    prompt_instructions: 'Analyze environmental impact including carbon footprint, water usage, waste generation, sustainable sourcing options, and eco-friendly alternatives.',
    output_format: 'structured_list',
    is_required: false,
    display_order: 6,
    block_type: 'sustainability',
    dependencies: ['ingredients', 'manufacturing']
  },
  {
    key: 'packaging',
    name: 'Packaging',
    description: 'Packaging specifications, materials, and design considerations',
    prompt_instructions: 'Specify packaging requirements including material types, barrier properties, shelf life considerations, labeling requirements, and sustainable packaging options.',
    output_format: 'structured_list',
    is_required: true,
    display_order: 7,
    block_type: 'packaging',
    dependencies: ['ingredients', 'compliance']
  }
];

// Default bindings for body care products
const defaultBindings = [
  // Body Lotion bindings
  { level: 'sub_category', slug: 'body_lotion', block_key: 'ingredients', is_required: true, display_order: 1 },
  { level: 'sub_category', slug: 'body_lotion', block_key: 'cost_analysis', is_required: true, display_order: 2 },
  { level: 'sub_category', slug: 'body_lotion', block_key: 'compliance', is_required: true, display_order: 3 },
  { level: 'sub_category', slug: 'body_lotion', block_key: 'manufacturing', is_required: true, display_order: 4 },
  { level: 'sub_category', slug: 'body_lotion', block_key: 'packaging', is_required: true, display_order: 5 },
  { level: 'sub_category', slug: 'body_lotion', block_key: 'sustainability', is_required: false, display_order: 6 },
  
  // Body Care category bindings
  { level: 'category', slug: 'body_care', block_key: 'ingredients', is_required: true, display_order: 1 },
  { level: 'category', slug: 'body_care', block_key: 'cost_analysis', is_required: true, display_order: 2 },
  { level: 'category', slug: 'body_care', block_key: 'compliance', is_required: true, display_order: 3 },
  { level: 'category', slug: 'body_care', block_key: 'manufacturing', is_required: true, display_order: 4 },
  { level: 'category', slug: 'body_care', block_key: 'packaging', is_required: true, display_order: 5 },
  
  // Personal Care industry bindings
  { level: 'industry', slug: 'personal_care', block_key: 'ingredients', is_required: true, display_order: 1 },
  { level: 'industry', slug: 'personal_care', block_key: 'cost_analysis', is_required: true, display_order: 2 },
  { level: 'industry', slug: 'personal_care', block_key: 'compliance', is_required: true, display_order: 3 },
  { level: 'industry', slug: 'personal_care', block_key: 'manufacturing', is_required: true, display_order: 4 },
  { level: 'industry', slug: 'personal_care', block_key: 'packaging', is_required: true, display_order: 5 }
];

async function seedFormulationBlocks() {
  try {
    console.log('Connecting to MongoDB...');
    await connect('development');
    
    console.log('Clearing existing formulation blocks...');
    await FormulationBlock.deleteMany({});
    
    console.log('Creating formulation blocks...');
    const createdBlocks = await FormulationBlock.insertMany(defaultFormulationBlocks);
    console.log(`Created ${createdBlocks.length} formulation blocks`);
    
    console.log('Clearing existing formulation bindings...');
    await FormulationBinding.deleteMany({});
    
    console.log('Creating formulation bindings...');
    const createdBindings = await FormulationBinding.insertMany(defaultBindings);
    console.log(`Created ${createdBindings.length} formulation bindings`);
    
    console.log('\nFormulation Blocks created:');
    createdBlocks.forEach(block => {
      console.log(`- ${block.name} (${block.key}) - ${block.block_type}`);
    });
    
    console.log('\nFormulation Bindings created:');
    createdBindings.forEach(binding => {
      console.log(`- ${binding.level}:${binding.slug} -> ${binding.block_key}`);
    });
    
    console.log('\nSeed data created successfully!');
    process.exit(0);
    
  } catch (error) {
    console.error('Error seeding formulation blocks:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  seedFormulationBlocks();
}

module.exports = { seedFormulationBlocks, defaultFormulationBlocks, defaultBindings };