#!/usr/bin/env node
// Process a single queued job and exit
require('dotenv').config();
const { connect } = require('../src/models');
const { mongoose } = require('../src/config/mongodb');
const QueueService = require('../src/services/queue.service');
const Project = require('../src/models/mongo/Project');
const formulationService = require('../src/services/formulation.service');

async function main() {
  await connect(process.env.NODE_ENV || 'development');
  const db = mongoose.connection.db;
  const queue = new QueueService(db);
  await queue.ensureIndexes();

  const job = await queue.claimNext(`once_${Date.now()}`);
  if (!job) {
    console.log(JSON.stringify({ ok: false, message: 'No queued jobs' }));
    return process.exit(0);
  }
  try {
    if (job.type === 'generate_project') {
      const project = await Project.findById(job.projectId);
      if (!project) {
        console.log(`Project ${job.projectId} not found (likely deleted), discarding job`);
        await queue.complete(job._id, { ok: true, discarded: true, reason: 'project_deleted' });
        return process.exit(0);
      }
      
      project.status = 'generating';
      project.generation_started_at = new Date();
      await project.save();
      
      await formulationService.generateAndPersist({
        projectId: job.projectId,
        userId: job.payload?.userId,
        formData: {
          industry: job.payload?.industry,
          productType: job.payload?.sub_category || job.payload?.category || project.product_type,
          goals: job.payload?.goals || {},
          productDescription: project.custom_product_type
        }
      });
    }
    await queue.complete(job._id, { ok: true });
    console.log(JSON.stringify({ ok: true, processedJob: String(job._id), projectId: String(job.projectId) }));
  } catch (e) {
    // Check if this is the final attempt
    const isFinalAttempt = job.attempts + 1 >= job.maxAttempts;
    
    if (isFinalAttempt && job.type === 'generate_project' && job.projectId) {
      // Update project status to failed on final attempt
      try {
        await Project.updateOne(
          { _id: job.projectId },
          { 
            status: 'failed',
            generation_failed_at: new Date(),
            failure_reason: e.message
          }
        );
        console.log(`Project ${job.projectId} marked as failed`);
      } catch (updateErr) {
        console.error(`Failed to update project status:`, updateErr);
      }
    }
    
    await queue.fail(job._id, e, job.attempts, job.maxAttempts);
    console.error(JSON.stringify({ ok: false, error: e.message }));
    process.exit(1);
  }
  process.exit(0);
}

main().catch(e => { console.error(e); process.exit(1); });

