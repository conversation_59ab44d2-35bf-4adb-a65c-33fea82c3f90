#!/usr/bin/env node
/*
 * Cleanup script: deactivate UI-only bindings and delete the parameter doc for `active_panel`.
 *
 * Usage:
 *  node scripts/cleanup-active-panel.js            # dry-run (default)
 *  node scripts/cleanup-active-panel.js --commit   # apply changes
 */

require('dotenv').config();
const mongoose = require('mongoose');

const argv = require('yargs/yargs')(process.argv.slice(2))
  .option('commit', { type: 'boolean', default: false, describe: 'Persist changes to DB' })
  .option('dry-run', { type: 'boolean', default: true, describe: 'Simulate without writing' })
  .help()
  .argv;

if (argv.commit === true) argv['dry-run'] = false;
const commit = argv.commit && !argv['dry-run'];

async function main() {
  const MONGODB_URI = process.env.MONGODB_URI || process.env.MONGO_URL || 'mongodb://localhost:27017/agrizy_formulation_dev';
  console.log(`→ Connecting MongoDB @ ${MONGODB_URI}`);
  await mongoose.connect(MONGODB_URI, { dbName: undefined });
  const db = mongoose.connection.db;

  const paramsCol = db.collection('parameters');
  const bindingsCol = db.collection('param_bindings');

  const paramKey = 'active_panel';

  const [paramCount, bindingCount] = await Promise.all([
    paramsCol.countDocuments({ key: paramKey }),
    bindingsCol.countDocuments({ parameter_key: paramKey })
  ]);

  console.log(`→ Found parameter docs: ${paramCount}, binding docs: ${bindingCount}`);

  if (!commit) {
    console.log('✔ Dry-run: would perform the following operations:');
    if (bindingCount > 0) console.log(`  • Deactivate ${bindingCount} binding(s) in param_bindings (set status="inactive" + audit)`);
    if (paramCount > 0) console.log('  • Delete 1 parameter document from parameters');
  } else {
    if (bindingCount > 0) {
      const res1 = await bindingsCol.updateMany(
        { parameter_key: paramKey },
        { $set: { status: 'inactive' }, $push: { audit: { ts: new Date(), action: 'deactivate-ui-only', note: 'cleanup-active-panel' } } }
      );
      console.log(`✔ Deactivated ${res1.modifiedCount || 0} binding(s)`);
    }

    if (paramCount > 0) {
      const res2 = await paramsCol.deleteOne({ key: paramKey });
      console.log(`✔ Deleted parameter document: ${res2.deletedCount || 0}`);
    }
  }

  await mongoose.disconnect();
  console.log(`✔ Done. ${commit ? 'Changes persisted.' : 'No changes made (dry-run).'}`);
}

main().catch(err => {
  console.error('Script failed:', err);
  process.exit(1);
});

