const { connect } = require('../src/models');
const { mongoose } = require('../src/config/mongodb');
const fs = require('fs');
const path = require('path');

async function restoreTaxonomies() {
  try {
    console.log('Connecting to MongoDB...');
    await connect('development');
    
    const db = mongoose.connection.db;
    const collection = db.collection('taxonomies');
    
    // Read the backup file
    const backupFile = path.join(__dirname, '../data/taxonomies_backup.jsonl');
    const fileContent = fs.readFileSync(backupFile, 'utf8');
    
    // Parse JSONL format (each line is a JSON object)
    const taxonomies = fileContent
      .trim()
      .split('\n')
      .map(line => {
        const data = JSON.parse(line);
        // Convert string _id to ObjectId if needed
        if (typeof data._id === 'string') {
          data._id = new mongoose.Types.ObjectId(data._id);
        }
        return data;
      });
    
    console.log(`Found ${taxonomies.length} taxonomies in backup file`);
    
    // Check current count
    const currentCount = await collection.countDocuments();
    console.log(`Current taxonomies in database: ${currentCount}`);
    
    // Clear existing taxonomies
    console.log('Clearing existing taxonomies...');
    await collection.deleteMany({});
    
    // Insert restored taxonomies
    console.log('Inserting restored taxonomies...');
    const result = await collection.insertMany(taxonomies);
    
    console.log(`Successfully restored ${result.insertedCount} taxonomies`);
    
    // Verify the restoration
    const finalCount = await collection.countDocuments();
    console.log(`Final count: ${finalCount} taxonomies`);
    
    // Show breakdown by level
    const breakdown = await collection.aggregate([
      { $group: { _id: '$level', count: { $sum: 1 } } },
      { $sort: { _id: 1 } }
    ]).toArray();
    
    console.log('\nBreakdown by level:');
    breakdown.forEach(item => {
      console.log(`  ${item._id}: ${item.count}`);
    });
    
  } catch (error) {
    console.error('Error restoring taxonomies:', error);
  } finally {
    await mongoose.connection.close();
    process.exit(0);
  }
}

restoreTaxonomies();