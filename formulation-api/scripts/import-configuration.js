#!/usr/bin/env node

/**
 * Import Configuration Data into MongoDB
 * Loads JSON files into respective collections
 */

require('dotenv').config();
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

const dataDir = path.join(__dirname, '..', 'data');

async function importData() {
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/agrizy_formulation_dev';
    await mongoose.connect(mongoUri);

    const db = mongoose.connection.db;
    console.log('✅ Connected to MongoDB');

    // Import taxonomies with hierarchy
    await importTaxonomies(db);

    // Collections to import (excluding taxonomies)
    const collections = [
      { name: 'parameters', file: 'parameters.json' },
      { name: 'param_bindings', file: 'param_bindings.json' },
      { name: 'param_answers', file: 'param_answers.json' },
      { name: 'ui_flows', file: 'ui_flows.json' },
      { name: 'guardrails', file: 'guardrails.json' }
    ];

    for (const collection of collections) {
      const filePath = path.join(dataDir, collection.file);

      if (!fs.existsSync(filePath)) {
        console.log(`⚠️ File not found: ${collection.file}`);
        continue;
      }

      console.log(`\nImporting ${collection.name}...`);

      // Read JSON file
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));

      // Clear existing data
      const col = db.collection(collection.name);
      const deleteResult = await col.deleteMany({});
      console.log(`  Cleared ${deleteResult.deletedCount} existing documents`);

      // Insert new data
      if (data.length > 0) {
        const insertResult = await col.insertMany(data);
        console.log(`  ✅ Inserted ${insertResult.insertedCount} documents`);
      } else {
        console.log(`  No data to insert`);
      }
    }

    console.log('\n✅ Data import complete!');

    // Test a query
    console.log('\nTesting data retrieval...');
    const taxonomies = await db.collection('taxonomies')
      .find({ level: 'industry' })
      .toArray();
    console.log(`Found ${taxonomies.length} industries:`, taxonomies.map(t => t.name).join(', '));

  } catch (error) {
    console.error('❌ Error importing data:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

async function importTaxonomies(db) {
    const filePath = path.join(dataDir, 'taxonomies.json');
    if (!fs.existsSync(filePath)) {
        console.log('⚠️ File not found: taxonomies.json');
        return;
    }

    console.log('\nImporting taxonomies...');
    const taxonomiesData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    const collection = db.collection('taxonomies');

    // Clear existing data
    const deleteResult = await collection.deleteMany({});
    console.log(`  Cleared ${deleteResult.deletedCount} existing documents`);

    const idMap = new Map();

    // Separate by level
    const industries = taxonomiesData.filter(t => t.level === 'industry');
    const categories = taxonomiesData.filter(t => t.level === 'category');
    const subCategories = taxonomiesData.filter(t => t.level === 'sub_category');

    // Insert industries
    for (const industry of industries) {
        const originalId = industry._id;
        delete industry._id;
        const result = await collection.insertOne(industry);
        idMap.set(originalId, result.insertedId);
    }
    console.log(`  ✅ Inserted ${industries.length} industries`);

    // Insert categories
    for (const category of categories) {
        const originalId = category._id;
        const parentId = idMap.get(category.parent_id);
        if (parentId) {
            category.parent_id = parentId;
        } else {
            console.warn(`⚠️ Parent not found for category: ${category.name}`);
        }
        delete category._id;
        const result = await collection.insertOne(category);
        idMap.set(originalId, result.insertedId);
    }
    console.log(`  ✅ Inserted ${categories.length} categories`);

    // Insert sub-categories
    for (const subCategory of subCategories) {
        const parentId = idMap.get(subCategory.parent_id);
        if (parentId) {
            subCategory.parent_id = parentId;
        }
        else {
            console.warn(`⚠️ Parent not found for sub-category: ${subCategory.name}`);
        }
        delete subCategory._id;
        await collection.insertOne(subCategory);
    }
    console.log(`  ✅ Inserted ${subCategories.length} sub-categories`);
}


// Run import
importData();