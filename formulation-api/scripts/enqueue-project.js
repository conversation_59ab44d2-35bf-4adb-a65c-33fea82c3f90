#!/usr/bin/env node
// Create a test project and enqueue a generate_project job
require('dotenv').config();
const { connect } = require('../src/models');
const { mongoose } = require('../src/config/mongodb');
const User = require('../src/models/mongo/User');
const Project = require('../src/models/mongo/Project');
const QueueService = require('../src/services/queue.service');

async function main() {
  await connect(process.env.NODE_ENV || 'development');
  const db = mongoose.connection.db;

  // Ensure a test user exists
  let user = await User.findOne({ email: '<EMAIL>' });
  if (!user) {
    user = await User.create({
      email: '<EMAIL>',
      password_hash: '$2b$12$6c4oAcZ0xYx8O0y5xQnR2OuKHTm4/E7l0BzV3o8h6g1Ylq8uK2m7O', // "password" bcrypt
      first_name: 'E2E',
      last_name: 'Tester',
      company: 'Agrizy',
      role: 'user'
    });
  }

  // Create project with taxonomy slugs likely present
  const industry = process.argv[2] || 'herbal-cosmetics';
  const sub = process.argv[3] || 'face-serum';

  const project = await Project.create({
    user_id: user._id,
    name: `E2E ${sub} Project`,
    description: 'E2E test project',
    industry,
    product_type: sub,
    goals: {},
    status: 'queued'
  });

  const queue = new QueueService(db);
  await queue.ensureIndexes();
  const jobId = await queue.enqueue({
    type: 'generate_project',
    projectId: project._id,
    payload: { userId: user._id, industry, sub_category: sub, goals: {} },
    maxAttempts: 2
  });

  await Project.updateOne({ _id: project._id }, { $set: { generation_job_id: jobId } });
  console.log(JSON.stringify({ ok: true, projectId: String(project._id), jobId: String(jobId) }));
  process.exit(0);
}

main().catch(e => { console.error(e); process.exit(1); });

