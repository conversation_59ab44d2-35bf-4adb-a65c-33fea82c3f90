const { connect } = require('../src/models');
const SchemaBuilder = require('../src/services/schemaBuilder');

async function testSchemaBuilder() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await connect('development');
    console.log('✅ Connected to MongoDB');

    console.log('\n🧪 Testing SchemaBuilder...');
    const schemaBuilder = new SchemaBuilder();
    
    console.log('\n📋 Testing schema generation for food_beverages > functional_drinks > energy_drinks');
    const schema = await schemaBuilder.getSchemaForTaxonomy(
      'food_beverages',
      'functional_drinks', 
      'energy_drinks'
    );
    
    console.log('\n✅ Schema generated successfully!');
    console.log('📊 Schema structure:');
    console.log(`- Type: ${schema.type}`);
    console.log(`- Properties count: ${Object.keys(schema.properties || {}).length}`);
    console.log(`- Required fields: ${schema.required?.length || 0}`);
    
    if (schema.properties) {
      console.log('\n📋 Available properties:');
      Object.keys(schema.properties).forEach(key => {
        console.log(`  - ${key}: ${schema.properties[key].type || 'object'}`);
      });
    }
    
    console.log('\n🎉 SchemaBuilder test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    process.exit(0);
  }
}

testSchemaBuilder();