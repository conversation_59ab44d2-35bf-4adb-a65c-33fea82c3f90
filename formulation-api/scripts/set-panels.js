#!/usr/bin/env node
/*
 * Set `panel` values on parameters and param_bindings.
 * - Binding.panel (if set) takes precedence over parameter.panel in services.
 * - This script infers panels from binding.display.section when available.
 *
 * Usage:
 *  node scripts/set-panels.js                   # dry-run (default)
 *  node scripts/set-panels.js --commit          # write changes
 *  node scripts/set-panels.js --level sub_category --slug face-serum  # optional filter
 */

require('dotenv').config();
const mongoose = require('mongoose');
const yargs = require('yargs/yargs');

const argv = yargs(process.argv.slice(2))
  .option('commit', { type: 'boolean', default: false, describe: 'Persist changes to DB' })
  .option('dry-run', { type: 'boolean', default: true, describe: 'Simulate without writing' })
  .option('level', { type: 'string', choices: ['industry', 'category', 'sub_category'], describe: 'Target level filter for bindings' })
  .option('slug', { type: 'string', describe: 'Target slug filter for bindings' })
  .help()
  .argv;

if (argv.commit === true) argv['dry-run'] = false;
const commit = argv.commit && !argv['dry-run'];

function titleCase(s) {
  if (!s || typeof s !== 'string') return undefined;
  return s
    .split(/[_\-\s]+/)
    .filter(Boolean)
    .map(w => w.charAt(0).toUpperCase() + w.slice(1))
    .join(' ');
}

function mapSectionToPanel(section) {
  const s = String(section || '').toLowerCase();
  const map = {
    basics: 'Basics',
    general: 'Basics',
    formulation: 'Formulation',
    composition: 'Formulation',
    packaging: 'Packaging',
    performance: 'Performance',
    safety: 'Safety',
    compliance: 'Compliance',
    skin: 'Skin',
    cost: 'Cost',
  };
  return map[s] || titleCase(s);
}

async function main() {
  const MONGODB_URI = process.env.MONGODB_URI || process.env.MONGO_URL || 'mongodb://localhost:27017/agrizy_formulation_dev';
  console.log(`→ Connecting MongoDB @ ${MONGODB_URI}`);
  await mongoose.connect(MONGODB_URI, { dbName: undefined });
  const db = mongoose.connection.db;

  const paramsCol = db.collection('parameters');
  const bindingsCol = db.collection('param_bindings');

  // Optional filter for bindings scope
  const bindingMatch = { status: { $ne: 'inactive' } };
  if (argv.level) bindingMatch['target.level'] = argv.level;
  if (argv.slug) bindingMatch['target.slug'] = argv.slug;

  const bindings = await bindingsCol.find(bindingMatch).project({
    _id: 1,
    parameter_key: 1,
    panel: 1,
    display: 1,
    target: 1
  }).toArray();

  console.log(`→ Loaded ${bindings.length} binding(s) for processing`);

  // Build counts per parameter_key → panel (from display.section)
  const panelCountsByParamKey = new Map();
  for (const b of bindings) {
    const inferred = mapSectionToPanel(b?.display?.section);
    if (!inferred) continue;
    if (!panelCountsByParamKey.has(b.parameter_key)) panelCountsByParamKey.set(b.parameter_key, new Map());
    const m = panelCountsByParamKey.get(b.parameter_key);
    m.set(inferred, (m.get(inferred) || 0) + 1);
  }

  // Decide preferred panel per parameter_key by majority vote
  const preferredPanelByParamKey = new Map();
  for (const [key, counts] of panelCountsByParamKey.entries()) {
    let best = null; let bestCount = -1;
    for (const [panel, count] of counts.entries()) {
      if (count > bestCount) { best = panel; bestCount = count; }
    }
    if (best) preferredPanelByParamKey.set(key, best);
  }

  // 1) Update bindings.panel when missing, using display.section inference
  let bindingsToUpdate = 0;
  for (const b of bindings) {
    if (typeof b.panel === 'string' && b.panel.trim() !== '') continue; // already set
    const inferred = mapSectionToPanel(b?.display?.section);
    if (!inferred) continue;
    bindingsToUpdate++;
    if (commit) {
      await bindingsCol.updateOne({ _id: b._id }, { $set: { panel: inferred }, $push: { audit: { ts: new Date(), action: 'set-panel-binding', value: inferred } } });
    }
  }
  console.log(`→ ${bindingsToUpdate} binding(s) ${commit ? 'updated' : 'would be updated'} with panel from display.section`);

  // 2) Update parameters.panel when missing, using preferred panel from bindings
  const paramKeys = Array.from(new Set(bindings.map(b => b.parameter_key)));
  const params = await paramsCol.find({ key: { $in: paramKeys } }).project({ _id: 1, key: 1, panel: 1 }).toArray();

  let paramsToUpdate = 0;
  for (const p of params) {
    if (typeof p.panel === 'string' && p.panel.trim() !== '') continue; // already set
    const pref = preferredPanelByParamKey.get(p.key);
    if (!pref) continue;
    paramsToUpdate++;
    if (commit) {
      await paramsCol.updateOne({ _id: p._id }, { $set: { panel: pref } });
    }
  }
  console.log(`→ ${paramsToUpdate} parameter(s) ${commit ? 'updated' : 'would be updated'} with inferred panel`);

  await mongoose.disconnect();
  console.log(`✔ Done. ${commit ? 'Changes persisted.' : 'No changes made (dry-run).'}`);
}

main().catch(err => {
  console.error('Script failed:', err);
  process.exit(1);
});

