const { connect } = require('../src/models');
const claudeService = require('../src/services/claudeService');
const logger = require('../src/utils/logger');

async function testDynamicSchemaGeneration() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await connect('development');
    console.log('✅ Connected to MongoDB');

    // Test formulation data
    const testFormData = {
      industry: 'food_beverages',
      category: 'functional_drinks',
      subCategory: 'energy_drinks',
      productType: 'Energy Drink',
      productDescription: 'Natural energy drink with organic ingredients',
      targetMarket: 'Health-conscious consumers',
      goals: {
        budgetPerUnit: '$2.50',
        compliance: 'FDA, USDA Organic'
      }
    };

    console.log('\n🧪 Testing dynamic schema generation...');
    console.log('Form Data:', JSON.stringify(testFormData, null, 2));

    // Generate formulation using dynamic schema
    console.log('\n🤖 Generating formulation with Claude AI...');
    const result = await claudeService.generateFormulation(testFormData);

    console.log('\n✅ Formulation generated successfully!');
    console.log('📊 Result summary:');
    console.log(`- Total recipes: ${result.recipes?.length || 0}`);
    console.log(`- Product name: ${result.product_name || 'Not specified'}`);
    console.log(`- Has nutritional profile: ${!!result.nutritional_profile}`);
    console.log(`- Has ingredients: ${!!result.ingredients}`);
    
    if (result.recipes && result.recipes.length > 0) {
      console.log('\n📋 First recipe preview:');
      const firstRecipe = result.recipes[0];
      console.log(`- Name: ${firstRecipe.name || 'Unnamed'}`);
      console.log(`- Ingredients count: ${firstRecipe.ingredients?.length || 0}`);
      console.log(`- Has instructions: ${!!firstRecipe.instructions}`);
    }

    console.log('✅ Dynamic schema integration test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    // If it's an AJV validation error, show more details
    if (error.message.includes('validation failed')) {
      console.log('\n🔍 Checking the generated schema for issues...');
      
      // Get the schema that was used
      const SchemaBuilder = require('../src/services/schemaBuilder');
      const schemaBuilder = new SchemaBuilder();
      const testSchema = await schemaBuilder.getSchemaForTaxonomy(
        'food_beverages',
        'functional_drinks', 
        'energy_drinks'
      );
      
      console.log('Schema validation status:');
      const isValid = schemaBuilder.validateSchema(testSchema);
      console.log('Schema is valid:', isValid);
      
      // Show first few required arrays to check for duplicates
      console.log('\nChecking for duplicates in schema:');
      const schemaStr = JSON.stringify(testSchema);
      const requiredMatches = schemaStr.match(/"required":\s*\[[^\]]*\]/g);
      if (requiredMatches) {
        requiredMatches.slice(0, 3).forEach((match, i) => {
          console.log(`Required array ${i + 1}:`, match);
        });
      }
    }
    console.error('Stack trace:', error.stack);
  } finally {
    process.exit(0);
  }
}

testDynamicSchemaGeneration();