// Simple test without database connection
const SchemaBuilder = require('../src/services/schemaBuilder');

console.log('🧪 Testing SchemaBuilder import...');

try {
  const schemaBuilder = new SchemaBuilder();
  console.log('✅ SchemaBuilder imported and instantiated successfully!');
  
  // Test the base schema method
  const baseSchema = schemaBuilder.getBaseSchema();
  console.log('📋 Base schema properties:', Object.keys(baseSchema.properties || {}));
  
  console.log('🎉 Basic SchemaBuilder test completed!');
} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error('Stack trace:', error.stack);
}