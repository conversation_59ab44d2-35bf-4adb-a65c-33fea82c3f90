const { connect } = require('../src/models');
const { mongoose } = require('../src/config/mongodb');

async function verifyDataIntegrity() {
  try {
    console.log('Connecting to MongoDB...');
    await connect('development');
    
    const db = mongoose.connection.db;
    
    console.log('\n=== DATABASE COLLECTIONS OVERVIEW ===');
    const collections = await db.listCollections().toArray();
    console.log(`Total collections: ${collections.length}`);
    
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      console.log(`  ${collection.name}: ${count} documents`);
    }
    
    console.log('\n=== TAXONOMY INTEGRITY CHECK ===');
    const taxonomies = db.collection('taxonomies');
    
    // Check taxonomy hierarchy
    const industries = await taxonomies.find({ level: 'industry' }).toArray();
    const categories = await taxonomies.find({ level: 'category' }).toArray();
    const subCategories = await taxonomies.find({ level: 'sub_category' }).toArray();
    
    console.log(`Industries: ${industries.length}`);
    console.log(`Categories: ${categories.length}`);
    console.log(`Sub-categories: ${subCategories.length}`);
    
    // Verify parent-child relationships
    let orphanedCategories = 0;
    let orphanedSubCategories = 0;
    
    for (const category of categories) {
      if (category.parent_id) {
        const parent = await taxonomies.findOne({ _id: new mongoose.Types.ObjectId(category.parent_id) });
        if (!parent) {
          console.log(`⚠️  Orphaned category: ${category.name} (parent_id: ${category.parent_id})`);
          orphanedCategories++;
        }
      }
    }
    
    for (const subCategory of subCategories) {
      if (subCategory.parent_id) {
        const parent = await taxonomies.findOne({ _id: new mongoose.Types.ObjectId(subCategory.parent_id) });
        if (!parent) {
          console.log(`⚠️  Orphaned sub-category: ${subCategory.name} (parent_id: ${subCategory.parent_id})`);
          orphanedSubCategories++;
        }
      }
    }
    
    console.log(`Orphaned categories: ${orphanedCategories}`);
    console.log(`Orphaned sub-categories: ${orphanedSubCategories}`);
    
    console.log('\n=== PARAMETER BINDINGS INTEGRITY ===');
    const paramBindings = db.collection('param_bindings');
    const bindingsCount = await paramBindings.countDocuments();
    console.log(`Total parameter bindings: ${bindingsCount}`);
    
    // Check if bindings reference valid taxonomies
    const sampleBindings = await paramBindings.find().limit(5).toArray();
    console.log('Sample bindings:');
    for (const binding of sampleBindings) {
      if (binding.taxonomy_id) {
        const taxonomy = await taxonomies.findOne({ _id: new mongoose.Types.ObjectId(binding.taxonomy_id) });
        const status = taxonomy ? '✅' : '❌';
        console.log(`  ${status} Binding ${binding._id} -> Taxonomy: ${taxonomy ? taxonomy.name : 'NOT FOUND'}`);
      }
    }
    
    console.log('\n=== PARAMETERS INTEGRITY ===');
    const parameters = db.collection('parameters');
    const parametersCount = await parameters.countDocuments();
    console.log(`Total parameters: ${parametersCount}`);
    
    // Check parameter types and constraints
    const paramTypes = await parameters.aggregate([
      { $group: { _id: '$type', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray();
    
    console.log('Parameter types:');
    paramTypes.forEach(type => {
      console.log(`  ${type._id}: ${type.count}`);
    });
    
    console.log('\n=== GUARDRAILS INTEGRITY ===');
    const guardrails = db.collection('guardrails');
    const guardrailsCount = await guardrails.countDocuments();
    console.log(`Total guardrails: ${guardrailsCount}`);
    
    // Check guardrail taxonomy references
    const sampleGuardrails = await guardrails.find().limit(3).toArray();
    console.log('Sample guardrails:');
    for (const guardrail of sampleGuardrails) {
      if (guardrail.taxonomy_levels) {
        console.log(`  Rule: ${guardrail.rule_name}`);
        console.log(`    Applies to: ${Object.keys(guardrail.taxonomy_levels).join(', ')}`);
      }
    }
    
    console.log('\n=== PROJECTS AND FORMULATIONS ===');
    const projects = db.collection('projects');
    const formulations = db.collection('formulations');
    
    const projectsCount = await projects.countDocuments();
    const formulationsCount = await formulations.countDocuments();
    
    console.log(`Projects: ${projectsCount}`);
    console.log(`Formulations: ${formulationsCount}`);
    
    // Check project-formulation relationships
    const sampleProjects = await projects.find().limit(3).toArray();
    for (const project of sampleProjects) {
      const projectFormulations = await formulations.countDocuments({ project_id: project._id });
      console.log(`  Project "${project.name}": ${projectFormulations} formulations`);
    }
    
    console.log('\n=== SUMMARY ===');
    const issues = orphanedCategories + orphanedSubCategories;
    if (issues === 0) {
      console.log('✅ All data integrity checks passed!');
    } else {
      console.log(`⚠️  Found ${issues} integrity issues that need attention`);
    }
    
    console.log('\n=== COLLECTION CONSTRAINTS CHECK ===');
    // Check for required fields and data consistency
    const taxonomyIssues = await taxonomies.find({
      $or: [
        { name: { $exists: false } },
        { slug: { $exists: false } },
        { level: { $exists: false } },
        { level: { $nin: ['industry', 'category', 'sub_category'] } }
      ]
    }).toArray();
    
    if (taxonomyIssues.length > 0) {
      console.log(`❌ Found ${taxonomyIssues.length} taxonomies with missing required fields`);
      taxonomyIssues.forEach(tax => {
        console.log(`  - ${tax._id}: missing ${!tax.name ? 'name' : !tax.slug ? 'slug' : 'valid level'}`);
      });
    } else {
      console.log('✅ All taxonomies have required fields');
    }
    
  } catch (error) {
    console.error('Error verifying data integrity:', error);
  } finally {
    await mongoose.connection.close();
    process.exit(0);
  }
}

verifyDataIntegrity();