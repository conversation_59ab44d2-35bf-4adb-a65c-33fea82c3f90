#!/usr/bin/env node

/**
 * Create Test User for Development
 * Creates a test user in the database for API testing
 */

require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

async function createTestUser() {
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/agrizy_formulation_dev';
    await mongoose.connect(mongoUri);
    
    const db = mongoose.connection.db;
    console.log('✅ Connected to MongoDB');

    // Hash password
    const password = 'Test123!@#';
    const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Test user data
    const testUser = {
      email: '<EMAIL>',
      password_hash: passwordHash,
      first_name: 'Test',
      last_name: 'User',
      company: 'Agrizy',
      role: 'admin',
      is_active: true,
      preferences: {
        notification_settings: {
          email: true,
          push: false
        },
        ui_preferences: {
          theme: 'light',
          language: 'en'
        }
      },
      created_at: new Date(),
      updated_at: new Date()
    };

    // Insert or update test user
    const usersCollection = db.collection('users');
    const result = await usersCollection.updateOne(
      { email: testUser.email },
      { $set: testUser },
      { upsert: true }
    );

    if (result.upsertedCount > 0) {
      console.log('✅ Test user created successfully');
    } else {
      console.log('✅ Test user updated successfully');
    }

    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: Test123!@#');
    console.log('\nYou can now use these credentials to log in and test the API');

  } catch (error) {
    console.error('❌ Error creating test user:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the script
createTestUser();