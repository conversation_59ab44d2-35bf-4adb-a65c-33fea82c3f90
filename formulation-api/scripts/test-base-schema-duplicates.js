const SchemaBuilder = require('../src/services/schemaBuilder');
const baseSchema = require('../src/schemas/formulationSchema.json');

// Test if base schema has duplicates
const schemaBuilder = new SchemaBuilder();

console.log('🔍 Checking base schema for duplicates...');

// Create a copy of base schema
const schemaCopy = JSON.parse(JSON.stringify(baseSchema));

// Apply duplicate removal
schemaBuilder.removeDuplicatesFromSchema(schemaCopy);

// Compare original and cleaned schema
const originalStr = JSON.stringify(baseSchema);
const cleanedStr = JSON.stringify(schemaCopy);

if (originalStr === cleanedStr) {
  console.log('✅ Base schema has no duplicates');
} else {
  console.log('⚠️  Base schema has duplicates that were removed');
  console.log('Original length:', originalStr.length);
  console.log('Cleaned length:', cleanedStr.length);
}

// Now test the SchemaBuilder's getBaseSchema method
console.log('\n🧪 Testing SchemaBuilder.getBaseSchema()...');
const builderSchema = schemaBuilder.getBaseSchema();
schemaBuilder.removeDuplicatesFromSchema(builderSchema);

const builderStr = JSON.stringify(builderSchema);
if (originalStr === builderStr) {
  console.log('✅ SchemaBuilder base schema matches original');
} else {
  console.log('⚠️  SchemaBuilder base schema differs from original');
}

console.log('\n✅ Base schema duplicate test completed!');