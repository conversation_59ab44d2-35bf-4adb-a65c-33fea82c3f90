const { connect } = require('../src/models');
const { mongoose } = require('../src/config/mongodb');
const fs = require('fs');
const path = require('path');

const COLLECTIONS_TO_RESTORE = [
  { file: 'taxonomies.jsonl', collection: 'taxonomies' },
  { file: 'parameters.jsonl', collection: 'parameters' },
  { file: 'param_bindings.jsonl', collection: 'param_bindings' },
  { file: 'param_answers.jsonl', collection: 'param_answers' },
  { file: 'guardrails.jsonl', collection: 'guardrails' },
  { file: 'components.jsonl', collection: 'components' },
  { file: 'ui_flows.jsonl', collection: 'ui_flows' }
];

function parseJsonl(filePath) {
  const fileContent = fs.readFileSync(filePath, 'utf8');
  return fileContent
    .trim()
    .split('\n')
    .filter(line => line.trim())
    .map(line => {
      const data = JSON.parse(line);
      // Convert string _id to ObjectId if needed
      if (typeof data._id === 'string') {
        data._id = new mongoose.Types.ObjectId(data._id);
      }
      // Convert other ObjectId fields
      if (data.parent_id && typeof data.parent_id === 'string') {
        data.parent_id = new mongoose.Types.ObjectId(data.parent_id);
      }
      if (data.taxonomy_id && typeof data.taxonomy_id === 'string') {
        data.taxonomy_id = new mongoose.Types.ObjectId(data.taxonomy_id);
      }
      if (data.parameter_id && typeof data.parameter_id === 'string') {
        data.parameter_id = new mongoose.Types.ObjectId(data.parameter_id);
      }
      return data;
    });
}

async function restoreAllCollections() {
  try {
    console.log('Connecting to MongoDB...');
    await connect('development');
    
    const db = mongoose.connection.db;
    
    console.log('\n=== RESTORING ALL COLLECTIONS FROM BACKUP ===');
    
    for (const { file, collection } of COLLECTIONS_TO_RESTORE) {
      const filePath = path.join(__dirname, '../data', file);
      
      if (!fs.existsSync(filePath)) {
        console.log(`⚠️  Backup file not found: ${file}`);
        continue;
      }
      
      console.log(`\nRestoring ${collection}...`);
      
      // Parse backup data
      const backupData = parseJsonl(filePath);
      console.log(`  Found ${backupData.length} documents in backup`);
      
      // Check current count
      const currentCount = await db.collection(collection).countDocuments();
      console.log(`  Current documents in database: ${currentCount}`);
      
      if (backupData.length > currentCount) {
        console.log(`  📦 Backup has more data (${backupData.length} vs ${currentCount}), restoring...`);
        
        // Clear existing data
        await db.collection(collection).deleteMany({});
        
        // Insert backup data
        if (backupData.length > 0) {
          const result = await db.collection(collection).insertMany(backupData);
          console.log(`  ✅ Restored ${result.insertedCount} documents`);
        }
      } else {
        console.log(`  ✅ Current data is complete (${currentCount} documents)`);
      }
    }
    
    console.log('\n=== VERIFICATION AFTER RESTORATION ===');
    
    // Verify taxonomy hierarchy
    const taxonomies = db.collection('taxonomies');
    const industries = await taxonomies.countDocuments({ level: 'industry' });
    const categories = await taxonomies.countDocuments({ level: 'category' });
    const subCategories = await taxonomies.countDocuments({ level: 'sub_category' });
    
    console.log(`Taxonomies: ${industries} industries, ${categories} categories, ${subCategories} sub-categories`);
    
    // Verify parameter bindings
    const paramBindings = db.collection('param_bindings');
    const bindingsCount = await paramBindings.countDocuments();
    console.log(`Parameter bindings: ${bindingsCount}`);
    
    // Check binding-taxonomy relationships
    const bindingsWithValidTaxonomy = await paramBindings.aggregate([
      {
        $lookup: {
          from: 'taxonomies',
          localField: 'taxonomy_id',
          foreignField: '_id',
          as: 'taxonomy'
        }
      },
      {
        $match: {
          'taxonomy.0': { $exists: true }
        }
      },
      {
        $count: 'validBindings'
      }
    ]).toArray();
    
    const validBindingsCount = bindingsWithValidTaxonomy[0]?.validBindings || 0;
    console.log(`Valid taxonomy bindings: ${validBindingsCount}/${bindingsCount}`);
    
    // Verify parameters
    const parameters = db.collection('parameters');
    const parametersCount = await parameters.countDocuments();
    console.log(`Parameters: ${parametersCount}`);
    
    // Verify guardrails
    const guardrails = db.collection('guardrails');
    const guardrailsCount = await guardrails.countDocuments();
    console.log(`Guardrails: ${guardrailsCount}`);
    
    console.log('\n=== FINAL COLLECTION SUMMARY ===');
    const collections = await db.listCollections().toArray();
    for (const collection of collections.sort((a, b) => a.name.localeCompare(b.name))) {
      const count = await db.collection(collection.name).countDocuments();
      console.log(`  ${collection.name}: ${count} documents`);
    }
    
    console.log('\n✅ All collections restored and verified successfully!');
    
  } catch (error) {
    console.error('Error restoring collections:', error);
  } finally {
    await mongoose.connection.close();
    process.exit(0);
  }
}

restoreAllCollections();