const SchemaBuilder = require('../src/services/schemaBuilder');

// Test duplicate removal function
const schemaBuilder = new SchemaBuilder();

// Create test object with duplicates
const testObj = {
  required: ['name', 'description', 'name', 'type', 'description'],
  properties: {
    nested: {
      required: ['field1', 'field2', 'field1'],
      items: {
        required: ['item1', 'item2', 'item1']
      }
    },
    arrayProp: [
      {
        required: ['a', 'b', 'a']
      }
    ]
  }
};

console.log('Before duplicate removal:');
console.log(JSON.stringify(testObj, null, 2));

schemaBuilder.removeDuplicatesFromSchema(testObj);

console.log('\nAfter duplicate removal:');
console.log(JSON.stringify(testObj, null, 2));

console.log('\n✅ Duplicate removal test completed!');