#!/usr/bin/env node
// Quick taxonomy inspector: prints industries → categories → subcategories

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const { mongoConnection, mongoose } = require('../src/config/mongodb');

async function main() {
  try {
    await mongoConnection.connect(process.env.NODE_ENV || 'development');
    const db = mongoose.connection.db;

    const col = db.collection('taxonomies');
    const byLevelCounts = await col.aggregate([
      { $group: { _id: '$level', count: { $sum: 1 } } },
      { $sort: { _id: 1 } }
    ]).toArray();

    console.log('=== Taxonomies: document counts by level ===');
    byLevelCounts.forEach(({ _id, count }) => console.log(`${_id}: ${count}`));

    const industries = await col.find({ level: 'industry' }).project({ name: 1, slug: 1 }).sort({ name: 1 }).toArray();
    console.log(`\n=== Industries (${industries.length}) ===`);
    for (const industry of industries) {
      console.log(`- ${industry.name} (${industry.slug})`);
      const categories = await col.find({ level: 'category', parent_id: industry._id }).project({ name: 1, slug: 1 }).sort({ name: 1 }).toArray();
      for (const cat of categories) {
        process.stdout.write(`  • ${cat.name} (${cat.slug})\n`);
        const subcats = await col.find({ level: 'sub_category', parent_id: cat._id }).project({ name: 1, slug: 1 }).sort({ name: 1 }).toArray();
        if (subcats.length) {
          subcats.forEach(sc => console.log(`     - ${sc.name} (${sc.slug})`));
        }
      }
    }

    await mongoConnection.disconnect();
  } catch (err) {
    console.error('Inspector failed:', err.message);
    process.exitCode = 1;
  }
}

main();

