#!/usr/bin/env node
// Quick inspector for taxonomy + param bindings presence
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });
const { connect } = require('../src/models');
const { mongoose } = require('../src/config/mongodb');

async function main() {
  await connect(process.env.NODE_ENV || 'development');
  const db = mongoose.connection.db;
  const tax = db.collection('taxonomies');
  const bindings = db.collection('param_bindings');
  const params = db.collection('parameters');

  const levels = ['industry', 'category', 'sub_category'];
  console.log('=== Taxonomy counts by level ===');
  for (const lvl of levels) {
    const count = await tax.countDocuments({ level: lvl });
    console.log(`${lvl}: ${count}`);
  }

  console.log('\n=== Param binding counts by target.level ===');
  for (const lvl of levels) {
    const count = await bindings.countDocuments({ 'target.level': lvl });
    console.log(`${lvl}: ${count}`);
  }

  console.log('\n=== Example binding slugs per level (top 10) ===');
  for (const lvl of levels) {
    const agg = await bindings.aggregate([
      { $match: { 'target.level': lvl } },
      { $group: { _id: '$target.slug', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]).toArray();
    console.log(`\nLevel: ${lvl}`);
    agg.forEach(a => console.log(`- ${a._id}: ${a.count}`));
  }

  console.log('\n=== Sanity check: parameters referenced but missing definitions ===');
  const missing = await bindings.aggregate([
    { $lookup: { from: 'parameters', localField: 'parameter_key', foreignField: 'key', as: 'p' } },
    { $match: { p: { $size: 0 } } },
    { $group: { _id: '$parameter_key', count: { $sum: 1 } } },
    { $sort: { count: -1 } }
  ]).toArray();
  if (missing.length === 0) console.log('OK: All bindings reference existing parameters');
  else {
    console.log('Missing parameter definitions for keys:');
    missing.forEach(m => console.log(`- ${m._id} (${m.count})`));
  }

  await mongoose.disconnect();
}

main().catch(e => { console.error(e); process.exit(1); });

