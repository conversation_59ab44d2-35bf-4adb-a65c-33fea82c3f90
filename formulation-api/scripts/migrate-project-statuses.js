const { mongoose } = require('../src/config/mongodb');
const logger = require('../src/utils/logger');

/**
 * Migration script to update project statuses to simplified flow:
 * queued → generating → ready/failed → closed → archived
 * 
 * Old statuses mapping:
 * - 'draft' → 'queued'
 * - 'in_progress' → 'generating' 
 * - 'completed' → 'ready'
 * - Keep existing: 'queued', 'generating', 'ready', 'failed', 'closed', 'archived'
 */

async function migrateProjectStatuses() {
  try {
    logger.info('Starting project status migration...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/agrizy-formulation');
    logger.info('Connected to MongoDB');
    
    const db = mongoose.connection.db;
    const projectsCollection = db.collection('projects');
    
    // Define status mappings
    const statusMappings = {
      'draft': 'queued',
      'in_progress': 'generating',
      'completed': 'ready'
      // Keep as-is: 'queued', 'generating', 'ready', 'failed', 'closed', 'archived'
    };
    
    // Get count of projects that need migration
    const oldStatuses = Object.keys(statusMappings);
    const projectsToMigrate = await projectsCollection.countDocuments({
      status: { $in: oldStatuses }
    });
    
    logger.info(`Found ${projectsToMigrate} projects that need status migration`);
    
    if (projectsToMigrate === 0) {
      logger.info('No projects need migration. All statuses are already up to date.');
      return;
    }
    
    // Perform the migration for each old status
    let totalUpdated = 0;
    
    for (const [oldStatus, newStatus] of Object.entries(statusMappings)) {
      const result = await projectsCollection.updateMany(
        { status: oldStatus },
        { 
          $set: { 
            status: newStatus,
            migration_updated_at: new Date(),
            migration_note: `Status migrated from '${oldStatus}' to '${newStatus}'`
          }
        }
      );
      
      logger.info(`Migrated ${result.modifiedCount} projects from '${oldStatus}' to '${newStatus}'`);
      totalUpdated += result.modifiedCount;
    }
    
    // Verify migration results
    const remainingOldStatuses = await projectsCollection.countDocuments({
      status: { $in: oldStatuses }
    });
    
    if (remainingOldStatuses > 0) {
      logger.warn(`Warning: ${remainingOldStatuses} projects still have old statuses`);
    }
    
    // Get final status distribution
    const statusDistribution = await projectsCollection.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]).toArray();
    
    logger.info('Final status distribution:', statusDistribution);
    logger.info(`Migration completed successfully. Updated ${totalUpdated} projects.`);
    
  } catch (error) {
    logger.error('Migration failed:', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  } finally {
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateProjectStatuses()
    .then(() => {
      logger.info('Migration script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateProjectStatuses };