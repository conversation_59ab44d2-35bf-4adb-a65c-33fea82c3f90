require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const logger = require('./src/utils/logger');
const errorHandler = require('./src/middleware/errorHandler');

// Import MongoDB connection and models
const { connect, ping } = require('./src/models');

// Import routes
const authRoutes = require('./src/routes/auth');
const projectRoutes = require('./src/routes/projects');
const formulationRoutes = require('./src/routes/formulations');
const playgroundRoutes = require('./src/routes/playground');
const logsRoutes = require('./src/routes/logs');
const configurationRoutesInit = require('./src/routes/configuration');
const adminRoutesInit = require('./src/routes/admin');
const formulationBlockRoutesInit = require('./src/routes/formulation-blocks');

const app = express();
const PORT = process.env.PORT || 7700;

// Trust proxy for rate limiting
app.set('trust proxy', 1);

// Security middleware
app.use(helmet());
logger.info('🛡️ Helmet security middleware enabled', { service: 'agrizy-formulation-api', environment: process.env.NODE_ENV || 'development' });

// CORS configuration (support both ALLOWED_ORIGINS and FRONTEND_URL envs)
const allowedOrigins = (process.env.ALLOWED_ORIGINS || process.env.FRONTEND_URL || 'http://localhost:5173,http://localhost:7070')
  .split(',')
  .map(s => s.trim());
app.use(cors({
  origin: allowedOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Rate limiting (COMPLETELY DISABLED FOR DEVELOPMENT)
const RATE_LIMIT_DISABLED = true; // Force disable all rate limiting
logger.info('🚫 All rate limiting completely disabled for development');

// Commented out rate limiter creation
// const RATE_LIMIT_WINDOW_MS = parseInt(process.env.RATE_LIMIT_WINDOW_MS || `${15 * 60 * 1000}`, 10);
// const RATE_LIMIT_MAX = parseInt(process.env.RATE_LIMIT_MAX || process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10);
// const limiter = rateLimit({
//   windowMs: RATE_LIMIT_WINDOW_MS,
//   max: RATE_LIMIT_MAX,
//   standardHeaders: true,
//   legacyHeaders: false,
//   message: { error: 'Too many requests, please try again later.' }
// });

// Rate limiting completely disabled
// if (!RATE_LIMIT_DISABLED && process.env.NODE_ENV === 'production') {
//   app.use(limiter);
//   logger.info('General rate limiting enabled');
// } else {
//   logger.info('General rate limiting disabled');
// }

// Auth-specific rate limiting (COMPLETELY DISABLED)
const AUTH_RATE_LIMIT_DISABLED = true; // Force disable auth rate limiting
logger.info('🚫 Auth rate limiting completely disabled for development');

// Commented out auth rate limiter creation
// const AUTH_RATE_LIMIT_WINDOW_MS = parseInt(process.env.AUTH_RATE_LIMIT_WINDOW_MS || `${15 * 60 * 1000}`, 10);
// const AUTH_RATE_LIMIT_MAX = parseInt(process.env.AUTH_RATE_LIMIT_MAX || process.env.AUTH_RATE_LIMIT_MAX_REQUESTS || '5', 10);
// const authLimiter = rateLimit({
//   windowMs: AUTH_RATE_LIMIT_WINDOW_MS,
//   max: AUTH_RATE_LIMIT_MAX,
//   standardHeaders: true,
//   legacyHeaders: false,
//   message: { error: 'Too many authentication attempts, please try again later.' }
// });

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });
  next();
});

// Health check endpoint
app.get('/health', async (req, res) => {
  const mongoHealth = await ping();
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    services: {
      mongodb: mongoHealth ? 'connected' : 'disconnected'
    }
  });
});

// Initialize database and start server
const startServer = async () => {
  try {
    // Connect to MongoDB
    await connect(process.env.NODE_ENV || 'development');
    logger.info('✅ MongoDB connection established successfully', {
      service: 'agrizy-formulation-api',
      environment: process.env.NODE_ENV || 'development'
    });

    // Test MongoDB connection
    const mongoHealthy = await ping();
    if (mongoHealthy) {
      logger.info('✅ MongoDB health check passed', {
        service: 'agrizy-formulation-api',
        environment: process.env.NODE_ENV || 'development'
      });
    } else {
      throw new Error('MongoDB health check failed');
    }

    // Get database instance for configuration routes
    const mongoose = require('mongoose');
    const db = mongoose.connection.db;

    // Create indexes for configuration collections
    const { createIndexes } = require('./src/db/indexes');
    try {
      await createIndexes(db);
      logger.info('✅ MongoDB indexes created successfully');
    } catch (indexError) {
      logger.warn('⚠️ Failed to create some indexes:', indexError);
      // Continue anyway - indexes are for optimization
    }

    // Initialize configuration routes with database
    const configurationRoutes = configurationRoutesInit(db);
    const adminRoutes = adminRoutesInit(db);
    const formulationBlockRoutes = formulationBlockRoutesInit();
    
    // API routes (Rate limiting completely disabled)
    app.use('/api/auth', authRoutes);
    logger.info('🚫 Auth routes configured without any rate limiting');

    // Commented out conditional auth rate limiting
    // if (AUTH_RATE_LIMIT_DISABLED) {
    //   app.use('/api/auth', authRoutes);
    //   logger.info('Auth rate limiting disabled');
    // } else {
    //   app.use('/api/auth', authLimiter, authRoutes);
    //   logger.info('Auth rate limiting enabled');
    // }
    app.use('/api/projects', projectRoutes);
    app.use('/api/formulations', formulationRoutes);
    app.use('/api/playground', playgroundRoutes);
    app.use('/api/logs', logsRoutes);
    app.use('/api', configurationRoutes); // Configuration routes
    app.use('/api/admin', adminRoutes); // Admin CRUD routes (protected)
    app.use('/api/formulation-blocks', formulationBlockRoutes); // Formulation blocks CRUD routes (protected)

    // 404 handler (must be after all route definitions)
    app.use((req, res) => {
      res.status(404).json({
        error: 'Route not found',
        path: req.originalUrl,
        method: req.method
      });
    });

    // Error handling middleware (must be last)
    app.use(errorHandler);

    // Start server
    app.listen(PORT, () => {
      logger.info(`🚀 Agrizy Formulation API Server running on port ${PORT}`, {
        service: 'agrizy-formulation-api',
        environment: process.env.NODE_ENV || 'development',
        port: PORT
      });
    });

  } catch (error) {
    logger.error('❌ Failed to start server:', {
      error: error.message,
      stack: error.stack,
      service: 'agrizy-formulation-api',
      environment: process.env.NODE_ENV || 'development'
    });
    process.exit(1);
  }
};

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  const { disconnect } = require('./src/models');
  await disconnect();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  const { disconnect } = require('./src/models');
  await disconnect();
  process.exit(0);
});

startServer();
