# Deployment Guide

## PM2 Ownership and Permissions

### The Problem

When deploying Node.js applications with PM2, ownership mismatches can cause:
- Permission denied errors when reading/writing files
- Log file access issues
- Module loading failures
- Process startup failures

### The Solution

Use the provided `scripts/fix-pm2-ownership.sh` script to ensure consistent ownership.

## Quick Deployment

### 1. Initial Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js (via NodeSource)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
sudo npm install pm2@latest -g

# Create application user (if not exists)
sudo useradd -m -s /bin/bash ubuntu
sudo usermod -aG sudo ubuntu
```

### 2. Deploy Application

```bash
# Clone repository
cd /home/<USER>
git clone <your-repo-url> formulation-api
cd formulation-api

# Install dependencies
npm install --production

# Copy environment file
cp .env.example .env
# Edit .env with your configuration
nano .env
```

### 3. Fix Ownership and Start PM2

```bash
# Run the ownership fix script
sudo ./scripts/fix-pm2-ownership.sh

# Or run steps manually:
# 1. Set target user (default: ubuntu)
export TARGET_USER=ubuntu
export PM2_USER=ubuntu

# 2. Run the fix
sudo TARGET_USER=ubuntu PM2_USER=ubuntu ./scripts/fix-pm2-ownership.sh
```

## Manual PM2 Setup

If you prefer manual setup:

### 1. Stop Existing PM2 Processes

```bash
# Stop all PM2 processes
pm2 stop all
pm2 delete all

# Or if PM2 is running as different user
sudo -u ubuntu pm2 stop all
sudo -u ubuntu pm2 delete all
```

### 2. Fix File Ownership

```bash
# Change ownership of entire application
sudo chown -R ubuntu:ubuntu /home/<USER>/formulation-api

# Fix permissions
find /home/<USER>/formulation-api -type f -name "*.js" -exec chmod 644 {} \;
find /home/<USER>/formulation-api -type f -name "*.json" -exec chmod 644 {} \;
find /home/<USER>/formulation-api -type f -name "*.sh" -exec chmod 755 {} \;
find /home/<USER>/formulation-api -type d -exec chmod 755 {} \;

# Create logs directory
mkdir -p /home/<USER>/formulation-api/logs
sudo chown ubuntu:ubuntu /home/<USER>/formulation-api/logs
```

### 3. Setup PM2 for Target User

```bash
# Switch to target user
sudo -u ubuntu -i

# Navigate to app directory
cd /home/<USER>/formulation-api

# Start application with PM2
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
# Follow the instructions provided by the command

# Exit back to sudo user and run the generated command
exit
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u ubuntu --hp /home/<USER>
```

## Environment Configuration

### Required Environment Variables

```bash
# Core application
NODE_ENV=production
PORT=3001

# MongoDB connection
MONGODB_URI=mongodb://localhost:27017/agrizy_formulation

# JWT secret
JWT_SECRET=your_secure_jwt_secret_here

# Multi-pass system (optional)
ENABLE_MULTIPASS=false
DEEPINFRA_API_KEY=your_deepinfra_key
OPENROUTER_API_KEY=your_openrouter_key

# Claude API key
ANTHROPIC_API_KEY=your_anthropic_key
```

### Production .env Example

```bash
NODE_ENV=production
PORT=3001
MONGODB_URI=mongodb://localhost:27017/agrizy_formulation_prod
JWT_SECRET=super_secure_random_string_change_this
ANTHROPIC_API_KEY=sk-ant-api03-your-key-here
ENABLE_MULTIPASS=false
```

## Monitoring and Maintenance

### PM2 Commands (run as app user)

```bash
# Monitor processes
sudo -u ubuntu pm2 monit

# View logs
sudo -u ubuntu pm2 logs
sudo -u ubuntu pm2 logs formulation-api

# Restart application
sudo -u ubuntu pm2 restart formulation-api

# Reload with zero downtime
sudo -u ubuntu pm2 reload formulation-api

# View process list
sudo -u ubuntu pm2 list

# View detailed info
sudo -u ubuntu pm2 show formulation-api
```

### Log Management

```bash
# Rotate logs
sudo -u ubuntu pm2 flush

# View error logs
sudo -u ubuntu tail -f logs/error.log

# View access logs
sudo -u ubuntu tail -f logs/out.log

# View combined logs
sudo -u ubuntu tail -f logs/combined.log
```

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   ```bash
   # Check file ownership
   ls -la /home/<USER>/formulation-api
   
   # Fix ownership
   sudo ./scripts/fix-pm2-ownership.sh
   ```

2. **PM2 Process Not Starting**
   ```bash
   # Check PM2 status
   sudo -u ubuntu pm2 list
   
   # View error logs
   sudo -u ubuntu pm2 logs formulation-api --err
   
   # Restart with verbose logs
   sudo -u ubuntu pm2 restart formulation-api --log-type raw
   ```

3. **Database Connection Issues**
   ```bash
   # Test MongoDB connection
   mongosh "mongodb://localhost:27017/agrizy_formulation_prod"
   
   # Check if MongoDB is running
   sudo systemctl status mongod
   
   # Start MongoDB if needed
   sudo systemctl start mongod
   ```

4. **Port Already in Use**
   ```bash
   # Check what's using the port
   sudo netstat -tulpn | grep :3001
   
   # Kill process using the port
   sudo kill -9 <PID>
   
   # Or change PORT in .env
   ```

### Health Checks

```bash
# Application health
curl http://localhost:3001/health

# PM2 health
sudo -u ubuntu pm2 ping

# MongoDB health
mongosh --eval "db.adminCommand('ping')"

# System resources
sudo -u ubuntu pm2 monit
htop
```

## Security Considerations

### File Permissions

- Application files: `644` for files, `755` for directories
- Executable scripts: `755`
- Environment files: `600` (sensitive data)
- Log files: `644` (readable by monitoring tools)

### User Security

```bash
# Limit sudo access for app user
sudo visudo
# Add: ubuntu ALL=(ALL) NOPASSWD: /usr/bin/pm2

# Set up proper firewall
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 3001  # Application (or use reverse proxy)
sudo ufw enable
```

### Reverse Proxy (Nginx)

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # SSE endpoints for multi-pass progress
    location /api/formulations/v2/progress {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header Cache-Control "no-cache";
        proxy_buffering off;
        proxy_read_timeout 24h;
    }
}
```

## Auto-deployment with GitHub Actions

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [master]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ubuntu
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /home/<USER>/formulation-api
          git pull origin master
          npm install --production
          sudo ./scripts/fix-pm2-ownership.sh
          sudo -u ubuntu pm2 reload ecosystem.config.js --env production
```

## Backup and Recovery

### Database Backup

```bash
# Create backup
mongodump --db agrizy_formulation_prod --out /home/<USER>/backups/$(date +%Y%m%d)

# Restore backup
mongorestore --db agrizy_formulation_prod /home/<USER>/backups/20240828/agrizy_formulation_prod
```

### Application Backup

```bash
# Backup application and logs
tar -czf /home/<USER>/backups/app-$(date +%Y%m%d).tar.gz \
    /home/<USER>/formulation-api \
    --exclude=node_modules \
    --exclude=.git
```

## Performance Optimization

### PM2 Cluster Mode

```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'formulation-api',
    script: 'server.js',
    instances: 'max',  // Use all CPU cores
    exec_mode: 'cluster',
    max_memory_restart: '1G'
  }]
};
```

### Node.js Optimization

```bash
# Increase memory limit
node --max-old-space-size=2048 server.js

# Or in ecosystem.config.js
node_args: ['--max-old-space-size=2048']
```

### MongoDB Optimization

```javascript
// Connection with connection pooling
const mongoOptions = {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  bufferCommands: false,
  bufferMaxEntries: 0
};
```