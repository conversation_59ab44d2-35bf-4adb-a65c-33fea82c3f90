# AgriZy Formulation Platform - API

This is the backend API for the AgriZy Formulation Platform, an enterprise-grade Node.js/Express server that provides RESTful APIs for AI-powered formulation development in the health and wellness industry.

## Technology Stack

- **Runtime**: Node.js
- **Framework**: Express 5.1.0
- **Database**: MongoDB with Mongoose 8.16.4
- **Authentication**: JWT with bcrypt 6.0.0
- **AI Integration**: Anthropic Claude API
- **Security**: Helmet, CORS, Rate Limiting
- **Logging**: Winston 3.17.0
- **Validation**: Joi 17.13.3, AJV 8.17.1

## Features

- RESTful API endpoints
- JWT-based authentication with refresh tokens
- AI-powered formulation generation via Claude API
- MongoDB database with complex schemas
- Rate limiting and security middleware
- Comprehensive logging and error handling
- Schema validation for API requests/responses
- Real-time formulation optimization

## Getting Started

### Prerequisites

- Node.js 18+
- MongoDB 6.0+
- Claude API key from Anthropic

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
```

3. Configure your `.env` file:
```env
NODE_ENV=development
PORT=3001
MONGODB_URI=mongodb://localhost:27017/agrizy_formulation_dev
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key
ANTHROPIC_API_KEY=your-claude-api-key
```

4. Start development server:
```bash
npm run dev
```

5. Start production server:
```bash
npm start
```

### Database Setup

1. Start MongoDB:
```bash
# macOS with Homebrew
brew services start mongodb-community

# Or run directly
mongod
```

2. Initialize database (optional):
```bash
npm run mongo:migrate
```

## API Documentation

### Authentication Endpoints

- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh JWT token
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Project Endpoints

- `GET /api/projects` - Get user projects
- `POST /api/projects` - Create new project
- `GET /api/projects/:id` - Get specific project
- `PUT /api/projects/:id` - Update project
- `DELETE /api/projects/:id` - Delete project

### Formulation Endpoints

- `POST /api/formulations/generate` - Generate AI formulation
- `GET /api/formulations/:projectId` - Get project formulations
- `PUT /api/formulations/:projectId` - Update formulation
- `POST /api/formulations/:id/export` - Export formulation

### Playground Endpoints

- `POST /api/playground/generate` - Generate formulation for playground
- `GET /api/playground/load/:projectId` - Load formulation data
- `POST /api/playground/chat/:projectId` - Chat-based optimization
- `GET /api/playground/status/:projectId` - Get playground status

## Project Structure

```
src/
├── config/             # Database and app configuration
├── controllers/        # Request handlers
├── middleware/         # Express middleware
├── models/            # MongoDB schemas
├── routes/            # API route definitions
├── services/          # External service integrations
├── utils/             # Utility functions
└── schemas/           # JSON schemas for validation
```

## Security Features

- **Rate Limiting**: 100 requests/15min, 5 auth requests/15min
- **CORS**: Configured for specific origins
- **Helmet**: Security headers
- **JWT**: Access and refresh token rotation
- **Password Hashing**: bcrypt with 12 salt rounds
- **Input Validation**: Joi and AJV schema validation

## Database Schema

### User Model
- Authentication and profile information
- Email domain validation
- Password hashing and session management

### Project Model
- Formulation projects with embedded data
- Version history and rollback support
- Conversation history for playground

### Session Model
- JWT session management with TTL

## Development

### Available Scripts

- `npm run dev` - Start development server with nodemon
- `npm start` - Start production server
- `npm run mongo:migrate` - Run database migrations
- `npm run mongo:seed` - Seed database with test data

### Testing

Run the test scripts:
```bash
# Test Claude integration
node test-claude-integration.js

# Test playground API
node test-playground-api.js

# Test schema validation
node test-schema-validation.js
```

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `NODE_ENV` | Environment (development/production) | Yes |
| `PORT` | Server port (default: 3001) | No |
| `MONGODB_URI` | MongoDB connection string | Yes |
| `JWT_SECRET` | JWT signing secret | Yes |
| `JWT_REFRESH_SECRET` | Refresh token secret | Yes |
| `ANTHROPIC_API_KEY` | Claude API key | Yes |

## Deployment

### Production Setup

1. Set environment to production:
```env
NODE_ENV=production
```

2. Use production MongoDB instance
3. Configure proper JWT secrets
4. Set up process manager (PM2 recommended)
5. Configure reverse proxy (Nginx recommended)

### Docker Support

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

## Contributing

1. Create a feature branch
2. Follow existing code patterns
3. Add proper error handling
4. Test with provided test scripts
5. Submit a pull request

## License

Proprietary - AgriZy Wellness Platform