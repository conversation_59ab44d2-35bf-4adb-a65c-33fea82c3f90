{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "mongo:seed": "node scripts/seed-mongodb.js", "mongo:migrate": "node scripts/migrate-to-mongodb.js", "recommend:defaults": "node scripts/recommend-defaults.js", "mongo:cleanup-active-panel": "node scripts/cleanup-active-panel.js", "mongo:set-panels": "node scripts/set-panels.js", "mongo:fix-ranges": "node scripts/fix-numeric-ranges.js", "worker:start": "node scripts/job-worker.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@anthropic-ai/sdk": "^0.57.0", "ajv": "^8.17.1", "bcrypt": "^6.0.0", "dotenv": "^16.4.5", "cors": "^2.8.5", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.4", "node-fetch": "^3.3.2", "yargs": "^17.7.2", "winston": "^3.17.0"}, "devDependencies": {"nodemon": "^3.1.10"}}