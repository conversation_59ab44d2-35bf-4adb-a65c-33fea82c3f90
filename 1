# Q pre block. Keep at the top of this file.
[[ -f "${HOME}/Library/Application Support/amazon-q/shell/zshrc.pre.zsh" ]] && builtin source "${HOME}/Library/Application Support/amazon-q/shell/zshrc.pre.zsh"
# If you come from bash you might have to change your $PATH.
# export PATH=$HOME/bin:/usr/local/bin:$PATH

# Path to your oh-my-zsh installation.
export ZSH="$HOME/.oh-my-zsh"
export LANG="en_US.UTF-8"
export LC_ALL="en_US.UTF-8"

# Set name of the theme to load --- if set to "random", it will
# load a random theme each time oh-my-zsh is loaded, in which case,
# to know which specific one was loaded, run: echo $RANDOM_THEME
# See https://github.com/ohmyzsh/ohmyzsh/wiki/Themes
ZSH_THEME="powerlevel9k/powerlevel9k"

# Set list of themes to pick from when loading at random
# Setting this variable when ZSH_THEME=random will cause zsh to load
# a theme from this variable instead of looking in $ZSH/themes/
# If set to an empty array, this variable will have no effect.
# ZSH_THEME_RANDOM_CANDIDATES=( "robbyrussell" "agnoster" )

# Uncomment the following line to use case-sensitive completion.
# CASE_SENSITIVE="true"

# Uncomment the following line to use hyphen-insensitive completion.
# Case-sensitive completion must be off. _ and - will be interchangeable.
# HYPHEN_INSENSITIVE="true"

# Uncomment one of the following lines to change the auto-update behavior
# zstyle ':omz:update' mode disabled  # disable automatic updates
# zstyle ':omz:update' mode auto      # update automatically without asking
# zstyle ':omz:update' mode reminder  # just remind me to update when it's time

# Uncomment the following line to change how often to auto-update (in days).
# zstyle ':omz:update' frequency 13

# Uncomment the following line if pasting URLs and other text is messed up.
# DISABLE_MAGIC_FUNCTIONS="true"

# Uncomment the following line to disable colors in ls.
# DISABLE_LS_COLORS="true"

# Uncomment the following line to disable auto-setting terminal title.
# DISABLE_AUTO_TITLE="true"

# Uncomment the following line to enable command auto-correction.
# ENABLE_CORRECTION="true"

# Uncomment the following line to display red dots whilst waiting for completion.
# You can also set it to another string to have that shown instead of the default red dots.
# e.g. COMPLETION_WAITING_DOTS="%F{yellow}waiting...%f"
# Caution: this setting can cause issues with multiline prompts in zsh < 5.7.1 (see #5765)
# COMPLETION_WAITING_DOTS="true"

# Uncomment the following line if you want to disable marking untracked files
# under VCS as dirty. This makes repository status check for large repositories
# much, much faster.
# DISABLE_UNTRACKED_FILES_DIRTY="true"

# Uncomment the following line if you want to change the command execution time
# stamp shown in the history command output.
# You can set one of the optional three formats:
# "mm/dd/yyyy"|"dd.mm.yyyy"|"yyyy-mm-dd"
# or set a custom format using the strftime function format specifications,
# see 'man strftime' for details.
# HIST_STAMPS="mm/dd/yyyy"

# Would you like to use another custom folder than $ZSH/custom?
# ZSH_CUSTOM=/path/to/new-custom-folder

# Which plugins would you like to load?
# Standard plugins can be found in $ZSH/plugins/
# Custom plugins may be added to $ZSH_CUSTOM/plugins/
# Example format: plugins=(rails git textmate ruby lighthouse)
# Add wisely, as too many plugins slow down shell startup.
plugins=(
          git
          docker
          virtualenv
        )
POWERLEVEL9K_RIGHT_PROMPT_ELEMENTS=(status virtualenv)
source $ZSH/oh-my-zsh.sh

# User configuration

# export MANPATH="/usr/local/man:$MANPATH"

# You may need to manually set your language environment
# export LANG=en_US.UTF-8

# Preferred editor for local and remote sessions
# if [[ -n $SSH_CONNECTION ]]; then
#   export EDITOR='vim'
# else
#   export EDITOR='mvim'
# fi

# Compilation flags
# export ARCHFLAGS="-arch x86_64"

# Set personal aliases, overriding those provided by oh-my-zsh libs,
# plugins, and themes. Aliases can be placed here, though oh-my-zsh
# users are encouraged to define aliases within the ZSH_CUSTOM folder.
# For a full list of active aliases, run `alias`.
#
# Example aliases
# alias zshconfig="mate ~/.zshrc"
# alias ohmyzsh="mate ~/.oh-my-zsh"

export PATH="/Users/<USER>/home/<USER>"
export PATH="$(brew --prefix)/opt/python@3/libexec/bin:$PATH"
export PATH="$HOME/.jenv/bin:$PATH"
export PATH="/Users/<USER>/fvm/default/bin:$PATH"
eval "$(jenv init -)"
#jenv enable-plugin export

export JAVA_HOME=/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home

#THIS MUST BE AT THE END OF THE FILE FOR SDKMAN TO WORK!!!
export SDKMAN_DIR="$HOME/.sdkman"
[[ -s "$HOME/.sdkman/bin/sdkman-init.sh" ]] && source "$HOME/.sdkman/bin/sdkman-init.sh"

test -e "${HOME}/.iterm2_shell_integration.zsh" && source "${HOME}/.iterm2_shell_integration.zsh"
export VIRTUAL_ENV_DISABLE_PROMPT=0
export ELASTIC_PASSWORD="=d2wi*JI1VwHGQbpF9Tc"

eval "$(pyenv init -)"
eval "$(pyenv virtualenv-init -)"
source $HOME/.zsh/scripts/wk_function.sh
source /opt/homebrew/opt/chruby/share/chruby/chruby.sh
source /opt/homebrew/opt/chruby/share/chruby/auto.sh
chruby ruby-3.3.1

# Q post block. Keep at the bottom of this file.
[[ -f "${HOME}/Library/Application Support/amazon-q/shell/zshrc.post.zsh" ]] && builtin source "${HOME}/Library/Application Support/amazon-q/shell/zshrc.post.zsh"

## [Completion]
## Completion scripts setup. Remove the following line to uninstall
[[ -f /Users/<USER>/.dart-cli-completion/zsh-config.zsh ]] && . /Users/<USER>/.dart-cli-completion/zsh-config.zsh || true
## [/Completion]

alias gl='git log --oneline --abbrev-commit --all --graph --decorate --color'


PATH="/Users/<USER>/perl5/bin${PATH:+:${PATH}}"; export PATH;
PERL5LIB="/Users/<USER>/perl5/lib/perl5${PERL5LIB:+:${PERL5LIB}}"; export PERL5LIB;
PERL_LOCAL_LIB_ROOT="/Users/<USER>/perl5${PERL_LOCAL_LIB_ROOT:+:${PERL_LOCAL_LIB_ROOT}}"; export PERL_LOCAL_LIB_ROOT;
PERL_MB_OPT="--install_base \"/Users/<USER>/perl5\""; export PERL_MB_OPT;
PERL_MM_OPT="INSTALL_BASE=/Users/<USER>/perl5"; export PERL_MM_OPT;

export DBD_MYSQL_CFLAGS=-I/opt/homebrew/Cellar/mysql/8.3.0_1/include/mysql
export DBD_MYSQL_LIBS="-L/opt/homebrew/Cellar/mysql/8.3.0_1/lib -lmysqlclient -lz  -lzstd  -lssl  -lcrypto -lresolv"
export DBD_MYSQL_CONFIG=mysql_config
export DBD_MYSQL_NOCATCHSTDERR=0
export DBD_MYSQL_NOFOUNDROWS=0
export DBD_MYSQL_TESTDB=test
export DBD_MYSQL_TESTHOST=localhost
export DBD_MYSQL_TESTPASSWORD=asdram
export DBD_MYSQL_TESTPORT=3306
export DBD_MYSQL_TESTUSER=root

export PATH=$PATH:/opt/local/bin
# -- START ACTIVESTATE INSTALLATION
export PATH="/Users/<USER>/.local/ActiveState/StateTool/release/bin:$PATH"
# -- STOP ACTIVESTATE INSTALLATION
# -- START ACTIVESTATE DEFAULT RUNTIME ENVIRONMENT
export PATH="/Users/<USER>/Library/Caches/activestate/bin:$PATH"
if [[ ! -z "$ACTIVESTATE_ACTIVATED" && -f "$ACTIVESTATE_ACTIVATED/activestate.yaml" ]]; then
  echo "State Tool is operating on project $ACTIVESTATE_ACTIVATED_NAMESPACE, located at $ACTIVESTATE_ACTIVATED"
fi
# -- STOP ACTIVESTATE DEFAULT RUNTIME ENVIRONMENT

if command -v ngrok &>/dev/null; then
    eval "$(ngrok completion)"
fi

export PATH=$PATH:~/.npm-global/bin

export NVM_DIR="$([ -z "${XDG_CONFIG_HOME-}" ] && printf %s "${HOME}/.nvm" || printf %s "${XDG_CONFIG_HOME}/nvm")"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

# place this after nvm initialization!
autoload -U add-zsh-hook

load-nvmrc() {
  local nvmrc_path
  nvmrc_path="$(nvm_find_nvmrc)"

  if [ -n "$nvmrc_path" ]; then
    local nvmrc_node_version
    nvmrc_node_version=$(nvm version "$(cat "${nvmrc_path}")")

    if [ "$nvmrc_node_version" = "N/A" ]; then
      nvm install
    elif [ "$nvmrc_node_version" != "$(nvm version)" ]; then
      nvm use
    fi
  elif [ -n "$(PWD=$OLDPWD nvm_find_nvmrc)" ] && [ "$(nvm version)" != "$(nvm version default)" ]; then
    echo "Reverting to nvm default version"
    nvm use default
  fi
}

add-zsh-hook chpwd load-nvmrc
load-nvmrc

# Auto-use Node.js v24.7.0 in new sessions
nvm use v24.7.0 > /dev/null 2>&1 || true

# bun completions
[ -s "/Users/<USER>/.bun/_bun" ] && source "/Users/<USER>/.bun/_bun"

# bun
export BUN_INSTALL="$HOME/.bun"
export PATH="$BUN_INSTALL/bin:$PATH"

export PATH="/opt/homebrew/opt/coreutils/libexec/gnubin:$PATH"
source /Users/<USER>/.config/broot/launcher/bash/br
alias bro="br -sdpw  --sort-by-date"
export PATH="$HOME/.cargo/bin:$PATH"
export PATH="$PATH:/Applications/Postgres.app/Contents/Versions/16/bin"
export RUST_LOG=sqlx=info

source ~/.set_devenv



# added by Servbay
export PATH=/Applications/ServBay/bin:/Applications/ServBay/sbin:/Applications/ServBay/script:$PATH

# pnpm
export PNPM_HOME="/Users/<USER>/Library/pnpm"
case ":$PATH:" in
  *":$PNPM_HOME:"*) ;;
  *) export PATH="$PNPM_HOME:$PATH" ;;
esac
# pnpm end
___MY_VMOPTIONS_SHELL_FILE="${HOME}/.jetbrains.vmoptions.sh"; if [ -f "${___MY_VMOPTIONS_SHELL_FILE}" ]; then . "${___MY_VMOPTIONS_SHELL_FILE}"; fi

export PYTHONPYCACHEPREFIX="${HOME}/.cache/Python"

# Added by Windsurf
export PATH="/Users/<USER>/.codeium/windsurf/bin:$PATH"
export PATH="/opt/homebrew/opt/postgresql@16/bin:$PATH"
export LDFLAGS="-L/opt/homebrew/opt/postgresql@16/lib"
export CPPFLAGS="-I/opt/homebrew/opt/postgresql@16/include"
export PKG_CONFIG_PATH="/opt/homebrew/opt/postgresql@16/lib/pkgconfig"

 # Set Postgres+pgvector settings
 export R2R_POSTGRES_USER=db_user
 export R2R_POSTGRES_PASSWORD=asdram
 export R2R_POSTGRES_HOST=0.0.0.0
 export R2R_POSTGRES_PORT=5432
 export R2R_POSTGRES_DBNAME=rr_db
 export R2R_PROJECT_NAME=agrizy_rr
 

export ANTHROPIC_API_KEY="************************************************************************************************************"
export OPENAI_API_KEY="********************************************************************************************************************************************************************"
export OPENROUTER_API_KEY="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"
export ZAI_KEY="a83d790954c44f32958935caf9e3df46.wiRq8LOEcrke6nX4"


# Created by `pipx` on 2024-12-06 16:07:58
export PATH="$PATH:/Users/<USER>/.local/bin"
export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"

export GITHUB_TOKEN='****************************************'
export GITHUB_ACCESS_TOKEN='****************************************'
export GITHUB_PERSONAL_ACCESS_TOKEN='****************************************'
export ANTHROPIC_API_KEY="************************************************************************************************************"
export HUGGINGFACE_TOKEN="*************************************"


export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools
export PATH=$PATH:$ANDROID_HOME/build-tools/35.0.0:$ANDROID_HOME/build-tools/36.0.0

nvm alias default v24.7.0

# For Bash/Zsh
kop() {
    if [ -z "$1" ]; then
        echo "Usage: kop <port_number>"
        return 1
    fi
    
    local port="$1"
    local pids=$(lsof -t -i:$port)
    
    if [ -z "$pids" ]; then
        echo "No process found running on port $port"
        return 1
    fi
    
    echo "Killing processes running on port $port: $pids"
    echo "$pids" | xargs kill -9
    echo "Process(es) killed successfully"
}


export CONFLUENCE_URL='https://teamagrizy.atlassian.net/wiki/'
export CONFLUENCE_USERNAME='<EMAIL>'
export CONFLUENCE_API_TOKEN='ATATT3xFfGF0JEiTSE2j9jF6a3sorQtylY6Z4uMuiuzxFcten_rb37Osi9wRZb29_jmTLYqBZqk9H3p__YvYBF0Zaj2ZGh9TEtl6SLW3t5UO8qEaSTZHNXI0IUbB9MULjGLl3fFCsLuhMFJ-JMcP0Zifr3SaT1BzfknCn2p5KVddHidcR4X_cf8=3E93D18F'
export JIRA_URL='https://teamagrizy.atlassian.net'
export JIRA_USERNAME='<EMAIL>'
export JIRA_API_TOKEN='ATATT3xFfGF0JEiTSE2j9jF6a3sorQtylY6Z4uMuiuzxFcten_rb37Osi9wRZb29_jmTLYqBZqk9H3p__YvYBF0Zaj2ZGh9TEtl6SLW3t5UO8qEaSTZHNXI0IUbB9MULjGLl3fFCsLuhMFJ-JMcP0Zifr3SaT1BzfknCn2p5KVddHidcR4X_cf8=3E93D18F'


fpath+=~/.zfunc; autoload -Uz compinit; compinit
export OLLAMA_CONTEXT_LENGTH=32768




# AWS Profile Management Aliases
AWS_ALIASES_QUIET=1
alias aws-default='unset AWS_PROFILE && echo "✅ Using default AWS profile"'
alias aws-current='echo "Current AWS Profile: ${AWS_PROFILE:-default}"'
alias aws-whoami='aws sts get-caller-identity'

# Quick AWS commands with profile switching
alias agrizy-aws='AWS_PROFILE=agrizy aws'
alias hf-aws='AWS_PROFILE=hotelfox aws'
alias hotelfox-aws='AWS_PROFILE=hotelfox aws'

# Profile status in prompt helper
aws-status() {
    if [[ -n "$AWS_PROFILE" ]]; then
        echo "AWS:$AWS_PROFILE"
    else
        echo "AWS:default"
    fi
}
source ~/home/<USER>/aws-cli-aliases/load-aws-aliases.sh
export PATH="/opt/homebrew/bin:$PATH"

[[ "$TERM_PROGRAM" == "kiro" ]] && . "$(kiro --locate-shell-integration-path zsh)"

alias claude="/Users/<USER>/.claude/local/claude"
export AWS_PROFILE=agrizy

[ ! -f "$HOME/.x-cmd.root/X" ] || . "$HOME/.x-cmd.root/X" # boot up x-cmd.

# SuperClaude API Key
export TWENTYFIRST_API_KEY="f20d70daf5e7b305122eece524040dbe493d7f569f90c6348554277bb59492eb"

# SuperClaude API Key
export MORPH_API_KEY="sk-TdFETaUbhVi1hcz7f64ubgY20kdL6aD-IKEVJdICPbYbE4km"
alias ch="node /Users/<USER>/home/<USER>/codex-history-list/dist/cli.js  --cwd-filter  `pwd`"

