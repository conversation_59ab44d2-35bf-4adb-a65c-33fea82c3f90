#!/bin/bash

echo "Testing back navigation functionality..."
echo ""

# Test authentication first
echo "1. Testing authentication..."
AUTH_RESPONSE=$(curl -s -X POST http://localhost:7700/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "test123"
  }')

TOKEN=$(echo $AUTH_RESPONSE | grep -o '"token":"[^"]*' | sed 's/"token":"//')

if [ -z "$TOKEN" ]; then
  echo "❌ Authentication failed"
  exit 1
fi

echo "✅ Authentication successful"
echo ""

echo "2. Navigation test URLs:"
echo "   Dashboard: http://localhost:7070/dashboard"
echo "   Playground: http://localhost:7070/playground/68ae70e08f71d60917fd1019"
echo ""
echo "3. Test the back button:"
echo "   a. Navigate to the playground URL above"
echo "   b. Click the arrow button (←) in the top left"
echo "   c. You should be redirected to /dashboard"
echo ""
echo "✅ Navigation fixes applied successfully!"
echo ""
echo "The back button now uses React Router navigation:"
echo "- handleBackToDashboard() now calls navigate('/dashboard')"
echo "- Both back buttons updated to use proper routing"