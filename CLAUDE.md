# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the **Agrizy Wellness Formulation Platform** - an enterprise-grade React web application for professional formulation development in the health and wellness industry. The platform serves three primary markets: functional beverages, nutraceuticals, and herbal cosmetics.

The application provides a comprehensive multi-step workflow from industry selection through formulation generation to branding and go-to-market strategy development.

## Architecture

### Tech Stack
- **Frontend**: React 19+ with functional components and hooks
- **Backend**: Node.js with Express.js REST API
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT with session management
- **Build Tool**: Vite 7.0+ for fast development and building
- **Styling**: Tailwind CSS 4.1+ with utility-first approach
- **Icons**: Lucide React for consistent iconography
- **Routing**: React Router DOM 7.7+ (configured but basic implementation)
- **State Management**: React Context API with custom hooks

### Application Structure
```
project/
├── frontend/            # React application
│   ├── src/
│   │   ├── components/  # Reusable UI components
│   │   ├── pages/       # Page components for workflow steps
│   │   ├── context/     # React Context providers
│   │   ├── App.jsx      # Main application router
│   │   └── main.jsx     # React entry point
│   └── package.json     # Frontend dependencies
├── backend/             # Express.js API server
│   ├── src/
│   │   ├── routes/      # API route handlers
│   │   ├── models/      # MongoDB schemas (Mongoose)
│   │   ├── middleware/  # Authentication & validation
│   │   ├── controllers/ # Business logic
│   │   ├── services/    # External service integrations
│   │   └── utils/       # Utility functions
│   ├── server.js        # Express server entry point
│   └── package.json     # Backend dependencies
└── CLAUDE.md           # Project documentation
```

### Key Components Architecture
The application follows a modular component architecture:

1. **AppContext Provider**: Centralized state management in `src/context/AppContext.jsx`
2. **Page Components**: Individual pages in `src/pages/` for each workflow step
3. **Reusable Components**: Shared UI elements in `src/components/`
4. **Main App Router**: Route handling and component orchestration in `App.jsx`

### Database Architecture (MongoDB)
The application uses MongoDB with embedded document patterns for optimal performance:

#### Core Collections
- **users**: User accounts with authentication and preferences
- **projects**: Formulation projects with embedded formulation data
- **sessions**: JWT session management with TTL expiry

#### Data Models
```javascript
// User Schema
{
  email: String,
  password_hash: String,
  first_name: String,
  last_name: String,
  company: String,
  role: String,
  is_active: Boolean,
  preferences: {
    notification_settings: Object,
    ui_preferences: Object
  }
}

// Project Schema (with embedded formulations)
{
  user_id: ObjectId,
  name: String,
  description: String,
  industry: String,
  product_type: String,
  current_formulation: {
    version: Number,
    quality_score: Number,
    recipes: [RecipeSchema],
    variations: [VariationSchema],
    conversation_history: [ChatSchema]
  },
  formulation_versions: [VersionSchema]
}
```

## Common Commands

### Frontend Development
```bash
# Navigate to frontend directory
cd /path/to/project

# Install dependencies
npm install

# Start development server (Vite)
npm run dev

# Production build
npm run build

# Preview production build
npm run preview

# Lint JavaScript/JSX files
npm run lint
```

### Backend Development
```bash
# Navigate to backend directory
cd backend/

# Install dependencies
npm install

# Start development server (nodemon)
npm run dev

# Start production server
npm start

# Run MongoDB migration (if needed)
npm run mongo:migrate
```

### Database Setup
```bash
# Start MongoDB (macOS with Homebrew)
brew services start mongodb-community

# Connect to MongoDB shell
mongosh

# Switch to project database
use agrizy_formulation_dev

# View collections
show collections

# Create test user (run from backend directory)
node scripts/create-test-user.js
```

### Key Development Notes
- **Frontend**: Runs on `http://localhost:5173` (Vite dev server)
- **Backend**: Runs on `http://localhost:3001` (Express API server)
- **Database**: MongoDB on `localhost:27017`
- Hot module replacement (HMR) enabled via Vite
- ESLint configured with React hooks and refresh plugins
- Tailwind CSS configured for `src/**/*.{js,jsx}` files
- JWT authentication with 24-hour session expiry
- MongoDB TTL indexes for automatic session cleanup

## Application Workflow

The application implements a 7-step guided workflow:

1. **Landing Page** (`landing`) - Marketing and entry point
2. **Industry Selection** (`industry-selection`) - Choose from beverages, nutraceuticals, cosmetics
3. **Product Type Selection** (`product-type`) - Specific product categories + custom option
4. **Goal Setup** (`goal-setup`) - Budget, nutrition targets, sustainability, compliance preferences
5. **Formulation Interface** (`formulation`) - AI-generated formulation with ingredient breakdown
6. **Branding Step** (`branding`) - Product identity, color schemes, target personas
7. **GTM Strategy** (`gtm`) - Launch strategy, distribution channels, marketing focus
8. **Results Dashboard** (`results`) - Interactive formulation editor with real-time optimization

## State Management

### Frontend State (AppContext)
```javascript
formData: {
  industry: '',           // beverages | nutraceuticals | cosmetics
  productType: '',        // predefined types or 'custom'
  productDescription: '',  // user-defined product type
  goals: {},             // budget, targets, preferences
  constraints: {},       // compliance requirements
  formulation: {},       // AI-generated formulation data
  branding: {},          // brand identity selections
  gtm: {}               // go-to-market strategy
}
```

### Backend State (MongoDB)
- **Persistent Storage**: All formulation data stored in MongoDB
- **Session Management**: JWT tokens with MongoDB session tracking
- **Version Control**: Automatic versioning of formulation changes
- **Embedded Documents**: Formulations stored within project documents for performance

### Navigation Flow
- State managed via `currentStep` string
- Progress tracked with `ProgressBar` component
- Data persistence across steps via context and backend API

## Key Features & Components

### InteractiveSmartMeter
Advanced progress meter with real-time updates:
- Animated progress bars with color coding
- Interactive sliders for value adjustment
- Support for different metrics (nutrition, sustainability, cost, etc.)
- Icon integration and customizable styling

### Formulation System
AI-powered formulation generation with Claude AI:
- Dynamic ingredient selection based on industry and product type
- Real-time quality scoring (nutrition, sustainability, compliance, cost efficiency)
- Interactive playground for formulation optimization
- Automatic ingredient ratio adjustment and normalization
- Conversation-based formulation refinement
- Version control with rollback capabilities
- Quality assessment with automatic playground recommendations

### Brand & GTM Integration
Complete product launch planning:
- Brand identity selection (colors, personas, values)
- Go-to-market strategy definition
- Distribution channel selection
- Marketing focus areas

## Styling System

### Tailwind Configuration
- Standard Tailwind CSS with default configuration
- Content scanning for `./src/**/*.{js,jsx}`
- Custom color schemes implemented via component logic

### Color Palette
- **Primary Green**: `green-500` series for success, nature, wellness
- **Blue**: `blue-500` series for trust, technology, innovation  
- **Purple**: `purple-500` series for premium, luxury, wellness
- **Orange**: `orange-500` series for energy, cost, efficiency
- **Gray Scale**: `gray-50` to `gray-900` for neutral elements

### Responsive Design
- Mobile-first approach with `md:` and `lg:` breakpoints
- Grid layouts that adapt to screen sizes
- Consistent spacing and typography scales

## Development Patterns

### Component Structure
- Modular architecture with separate files for each component
- Functional components with hooks pattern throughout
- Custom `useApp()` hook for context access in `src/context/AppContext.jsx`
- Consistent prop patterns and state management across components
- Page components in `src/pages/` handle route-specific logic
- Reusable UI components in `src/components/` for shared functionality

### Form Handling
- Controlled components with React state
- Real-time validation and updates
- Multi-step form data persistence
- Interactive sliders and checkboxes

### Mock Data Integration
- Hardcoded ingredient database (8 ingredients)
- Simulated AI generation with 3-second delay
- Real-time calculation algorithms
- Sample formulations and scoring

## Current Status

### Completed Features
- **Full Backend Integration**: Express.js API with MongoDB database
- **User Authentication**: JWT-based authentication with session management
- **Data Persistence**: All formulation data stored in MongoDB
- **Project Management**: Create, update, and manage formulation projects
- **AI Formulation Generation**: Claude AI integration for formulation creation
- **Interactive Playground**: Real-time formulation editing and optimization
- **Quality Assessment**: Automated scoring and validation system
- **Version Control**: Built-in formulation version history

### Current Limitations

### Frontend
- No localStorage backup for offline capability
- Basic React Router implementation
- No deep linking to specific workflow steps

### Export Functionality
- Export buttons show alerts instead of generating files
- No actual PDF or CSV generation implemented
- Mock file download functionality

### Advanced Features
- No real-time collaboration
- No advanced data visualization
- No supplier integration APIs

## Testing & Quality

### Current Status
- No test suite implemented
- Manual testing completed for core workflow
- ESLint configured for code quality

### Recommended Testing Setup
```bash
# Add testing dependencies
npm install -D vitest @testing-library/react @testing-library/jest-dom
npm install -D @testing-library/user-event jsdom
```

### Critical Test Areas
1. Multi-step workflow navigation
2. Form data persistence across steps
3. Interactive component functionality (sliders, meters)
4. Responsive design across breakpoints
5. State management and context updates

## Performance Considerations

### Current Optimizations
- Vite for fast development and building
- React 19 with automatic optimizations
- Efficient re-rendering via context patterns
- Tailwind CSS purging for production

### Future Enhancements
- Component lazy loading for large formulation database
- Debounced input handling for real-time calculations
- Image optimization for marketing assets
- Bundle size analysis and optimization

## Deployment Notes

### Build Configuration
- Vite handles production builds with automatic optimization
- Static assets processed and bundled
- CSS automatically purged and minified

### Environment Requirements
- Node.js 18+ for development
- MongoDB 6.0+ for database
- Modern browser with ES2020+ support
- Claude AI API key for formulation generation

## Extension Points

### Advanced Backend Features
- Real-time collaboration via WebSockets
- Advanced ingredient database with supplier APIs
- Comprehensive analytics and reporting
- Real export functionality (PDF/CSV generation)
- Automated compliance checking
- Manufacturing partner integrations

### Enhanced Features
- Advanced AI formulation optimization
- Supplier integration and real-time pricing
- Team collaboration and project sharing
- Manufacturing partner integration

### UI/UX Enhancements
- Advanced data visualization (charts, graphs)
- Drag-and-drop ingredient management
- Advanced filtering and search capabilities
- Custom branding and white-label options

## Important Notes

- **Modular Architecture**: Application now uses proper component separation with dedicated directories for components, pages, and context
- **Tailwind Configuration**: Enhanced with custom colors, animations, and keyframes for the wellness theme
- **Mock Data**: Current formulation system uses hardcoded data - replace with real ingredient database
- **State Management**: Uses React Context API - consider upgrading to Redux or Zustand for more complex state requirements
- **Component Development**: New components should follow the established pattern in `src/components/` and `src/pages/`
- **Accessibility**: Basic accessibility implemented - consider comprehensive WCAG compliance for enterprise use
- **Browser Support**: Targets modern browsers - may need polyfills for older browser support