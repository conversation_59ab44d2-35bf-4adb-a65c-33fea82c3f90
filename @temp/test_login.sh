#!/bin/bash

# Test login with different <NAME_EMAIL>
API_URL="http://localhost:7000/api/auth/login"
EMAIL="<EMAIL>"

# Array of common passwords to test
passwords=(
    "password123"
    "admin123"
    "Password123!"
    "Test@123"
    "admin@123"
    "Agrizy123!"
    "Wellness123!"
    "formulation123"
    "Admin123!"
    "agrizy"
    "wellness"
    "123456"
)

echo "Testing login for: $EMAIL"
echo "API URL: $API_URL"
echo "=========================================="

for password in "${passwords[@]}"; do
    echo -n "Testing password '$password': "
    
    response=$(curl -s -w "%{http_code}" -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"$EMAIL\",\"password\":\"$password\"}" \
        -o /tmp/response.json)
    
    if [ "$response" = "200" ]; then
        echo "✅ SUCCESS!"
        echo "Login successful with password: $password"
        echo "Response:"
        cat /tmp/response.json | jq .
        break
    elif [ "$response" = "401" ]; then
        echo "❌ Failed (Invalid credentials)"
    elif [ "$response" = "000" ]; then
        echo "❌ Failed (Server not responding)"
        echo "Make sure the API server is running on port 7000"
        break
    else
        echo "❌ Failed (HTTP $response)"
        cat /tmp/response.json
    fi
done

echo ""
echo "=========================================="
echo "If none worked, you may need to create a new user:"
echo "curl -X POST http://localhost:7000/api/auth/register \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"email\":\"<EMAIL>\",\"password\":\"Password123!\",\"first_name\":\"Test\",\"last_name\":\"User\"}'"