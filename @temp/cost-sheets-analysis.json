{"totalFiles": 13, "analyzedFiles": 13, "consolidatedPatterns": {"ingredientTypes": ["Purified Water", "Vitamin C (3-O-Ethyl-L-Ascorbic Acid)", "Niacinamide", "Propylene Glycol", "<PERSON>", "<PERSON><PERSON>", "Glycerine", "4-Butylresorcinol", "<PERSON><PERSON><PERSON><PERSON>", "Phenoxyethanol + Ethylhexylglycerin", "Ferulic Acid", "Green Tea Extract", "Glutathione reductase", "Hyaluronic Acid", "<PERSON><PERSON><PERSON><PERSON>", "Vitamin E (Tocopherol)", "CoQ10 (Ubiquinone)", "<PERSON><PERSON>xant<PERSON>", "DM Water", "Urea", "Simulgel INS 100", "Zemea", "Cresmer EW", "Glycerin", "Cetyl Alcohol", "<PERSON> Hazel", "Stearic Acid", "<PERSON>", "DUB CO", "Salicylic Acid", "Olivitis 15", "Euxyl PE 9010", "Heliofeel", "Boswellin Super", "Squalene", "Tinogard TT", "Vitamin E Acetate", "Sodium Hydroxide Pellets", "Disodium EDTA"], "costCategories": ["rawMaterialCost", "totalCost", "packagingCost"], "commonPatterns": ["cost_analysis", "ingredient_list"]}, "detailedAnalyses": [{"fileName": "Costing - Anti Dandruff Hair Oil.xlsx", "sheets": {"Sheet2": [[], ["Product Description: Anti Dandruff Hair Oil (100 ml)"], ["Helianthus annus oil / ( Sun flower) Oil", 56.15, 210, 11.7915], ["Castor oil /Ricinus communis oil", 0.8, 250, 0.2], ["Flax seed oil/Linseed oil/ Linum usitatissimum oil ", 4, 500, 2], ["Sesame oil / Sesamum Indicum Oil", 2, 350, 0.7], ["Rice bran oil / Oryza sativa  oil", 2, 300, 0.6], ["Soy bean oil / Glycine max seed Oil", 2, 719, 1.438], ["Dantuff’C / Climbazole", 0.04, 3500, 0.14], ["Azadirachta indica oil / Neem Oil (Carrier -Coconut oil /Gingelly oil)", 1, 250, 0.25], ["Citrus limon fruit peel oil", 0.4, 1450, 0.58], ["Curcuma longa rhizome oil - (Carrier -Coconut oil /Gingelly oil)", 0.0004, 950, 0.00038], ["Rosamarinus officinalis leaf oil - (Carrier -Coconut oil /Gingelly oil)", 0.4, 3500, 1.4], ["Melaleuca leucadendron /Tea tree Leaf Oil", 0.2, null, 0], ["Cocus nucifera oil ( Coconut oil )", 30, 400, 12], ["Olive oil / Olea Europaea Oil.", 0.01, 400, 0.004], ["Zingiber officinale (Shunthi) Rhizome Oil -(Carrier -Coconut oil /Gingelly oil)", 0.2, 3500, 0.7], ["Ocimum sanctums (Tulasi) seed Oil - (Carrier -Coconut oil /Gingelly oil)", 0.2, 900, 0.18], ["Butylated Hydroxy Toluene (BHT)", 0.2, 400, 0.08], ["Vitamin E Acetate / Tocopheryl Acetate", 0.1, 2100, 0.21], ["Olive & Almond Frag-270815", 0.2, 1000, 0.2], ["Salicylic acid ", 0.1, 300, 0.03], [null, null, null, "350/- kg"]], "Costing": [["Product ", null, "Anti Dandruff Hair Oil", null, null, null, null, 45790], ["Client", null, "Agrizy Wellness"], ["Manufacturer Name"], ["Account "], ["Pack Size", null, 100, "ml"], ["Pack Size with tolerance", null, 100.5, "ml"], ["<PERSON><PERSON> Si<PERSON> ", null, 100, "Kg"], ["Batch Quantity", null, 996, "Nos"], ["Yield 95%", null, 947, "Nos"], [], ["Raw Material Cost"], ["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost", "Freight", "Price in Rs. per Kg", "Value in Rs.", "% contri"], [1, "<PERSON><PERSON><PERSON> annuus (Sunflower) Seed Oil", 56.**************, 56.**************, 170, 20, 190, 10668.************, 0.****************], [2, "Cocos nucifera (Coconut) Oil", 30, 30, 330, 20, 350, 10500, 0.****************], [3, "Linum usitatissimum (Flaxseed / Linseed) Oil", 4, 4, 300, 20, 320, 1280, 0.*****************], [4, "Sesamum indicum (Sesame) Seed Oil", 2, 2, 230, 20, 250, 500, 0.*****************], [5, "Or<PERSON>za sativa (Rice Bran) Oil", 2, 2, 260, 20, 280, 560, 0.019771288558425925], [6, "Glycine max (Soybean) Seed Oil", 2, 2, 200, 20, 220, 440, 0.015534583867334655], [7, "Azadirachta indica (Neem) Oil (in Coconut Oil / Gingelly Oil)", 1, 1, 250, 20, 270, 270, 0.009532585554955356], [8, "Rici<PERSON> communis (Castor) Oil", 0.8, 0.8, 280, 20, 300, 240, 0.008473409382182539], [9, "Citrus limon (Lemon) Peel Oil", 0.4, 0.4, 1600, 20, 1620, 648, 0.022878205331892856], [10, "<PERSON><PERSON><PERSON><PERSON> officinalis (Rosemary) Leaf Oil (in Coconut Oil / Gingelly Oil)", 0.4, 0.4, 2000, 20, 2020, 808, 0.028527144920014548], [11, "<PERSON><PERSON><PERSON> leucaden<PERSON> (Tea Tree) Leaf Oil", 0.2, 0.2, 1600, 20, 1620, 324, 0.011439102665946428], [12, "Zingiber officinale (Ginger / Shunthi) Rhizome Oil (in Coconut Oil / Gingelly Oil)", 0.2, 0.2, 4200, 20, 4220, 844, 0.02979815632734193], [13, "Ocimum sanctum (Tulsi) Seed Oil (in Coconut Oil / Gingelly Oil)", 0.2, 0.2, 2800, 20, 2820, 564, 0.019912512048128966], [14, "Butylated Hydroxy Toluene (BHT) (Antioxidant)", 0.2, 0.2, 400, 20, 420, 84, 0.002965693283763889], [15, "Fragrance: Olive & Almond (Code: 270815)", 0.2, 0.2, 1000, 20, 1020, 204, 0.0072023979748551585], [16, "Tocopheryl Acetate (Vitamin E Acetate)", 0.1, 0.1, 2100, 20, 2120, 212, 0.007484844954261243], [17, "Salicylic Acid", 0.1, 0.1, 300, 20, 320, 32, 0.0011297879176243386], [18, "Climbazole (Dandruff-Control Active) (e.g., Dantuff® C)", 0.04, 0.04, 3500, 20, 3520, 140.8, 0.00497106683754709], [19, "Olea europaea (Olive) Oil", 0.01, 0.01, 400, 20, 420, 4.2, 0.00014828466418819445], [20, "Curcuma longa (Turmeric) Rhizome Oil (in Coconut Oil / Gingelly Oil)", 0.0004, 0.0004, 1100, 20, 1120, 0.448, 1.581703084674074e-05], [null, "Testing Charges", null, null, null, null, null, 0, 0], [null, "Total", 100.0*************, 100.0*************, null, null, null, 28323.899999999998, 1], [], ["Total Bulk Cost", null, 28323.899999999998, null, null, null, "SKU", null, "100ml"], ["Total Bulk Cost with 95% recovery ", null, 29814.699999999997, null, null, null, "RM cost Per Unit", null, 31.5], [], ["Packaging Material Cost"], ["Sr. No.", "Packaging Material", "Qty ", "Order Qty", "<PERSON><PERSON><PERSON>", "Basic Cost", "Freight", "Total price", "Per Unit Cost 100ml"], [1, "PET/HDPE Round Bottle 100ml", 1, 1000, 1000, 8, 0.4, 8.4, 8400], [2, "Flip Top cap", 1, 1000, 1000, 2, 0.1, 2.1, 2100], [3, "Label / Shrink Sleeves 100ml", 1, 1000, 1000, 2, 0.1, 2.1, 2100], [4, "Mono Carton 100ml", 1, 1000, 1000, 5.5, 0.275, 5.775, 5775], [5, "Plain Shrink Sleeves", 1, 1000, 1000, 1.5, 0.0750*************, 1.575, 1575], [6, "Outer Carton 100ml", 0.16666666666666666, 1000, 167, 15, 0.75, 15.75, 2630.25], [7, "5-<PERSON>ly Shipper Box 100ml", 0.018518518518518517, 1000, 19, 90, 4.5, 94.5, 1795.5], [8, "Plain Brown BOPP Tape 100ml", 0.05, 1000, 1, 50, 2.5, 52.5, 52.5], [], [null, null, null, null, "PM Cost/ Unit", null, null, null, 25.8], [null, null, 22.28, null, "PM Cost/ Unit with Wastage", null, null, 0.03, 26.6], [null, null, null, null, "RM Per Unit Cost", null, null, null, 31.5], [null, null, null, null, "CC/PC Charges", null, null, 0.25, 14.6], [null, null, null, null, "Product Cost (Ex-Works)", null, null, null, 72.7], [null, null, null, null, "Our Selling Price (INR/ Unit)", null, null, null, 90.89999999999999], [null, null, null, null, "% Margin", null, null, null, 0.2503438789546078], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []]}, "ingredients": [], "costStructure": {"totalCost": 29814.699999999997}, "patterns": []}, {"fileName": "Costing - Anti Hairfall Hair oil.xlsx", "sheets": {"Sheet1": [["Item Code", "Anti-Hairfall Hair oil ", "100g", "Price Per Kg", "Per Kg cost"], [null, "Helianthus annus oil / ( Sun flower) Oil", 78.78, 210, 16.5438], [null, "Linum usitatissimum (Flax / Linseed) seed oil", 9, 250, 2.25], [null, "Glycine max ( Soy bean) seed Oil", 2, 143, 0.286], [null, "Sesamum indicum (Sesame ) seed Oil", 2, 473, 0.946], [null, "Oryza sativa ( Rice bran ) husk oil", 2, 115, 0.23], [null, "Ricinus communis (Castor) oil", 2, 250, 0.5], [null, "Eclipta alba (Bringaraj) leaf oil- (Carrier -Coconut oil /Gingelly oil)", 0.4, 750, 0.3], [null, "<PERSON><PERSON><PERSON> officinalis (Rosemary) leaf oil- (Carrier -Coconut oil /Gingelly oil)", 0.4, 1500, 0.6], [null, "Citrus limon (Lemon) Fruit peel oil", 0.2, 1200, 0.24], [null, "Prunus amygdalus (Almond) seed oil ", 0.2, 587.55, 0.11750999999999999], [null, "Cocos nucifera (Coconut) endosperm oil", 2, 320, 0.64], [null, "Murraya koenigii (Curry leaves) leaf oil-(Carrier -Coconut oil /Gingelly oil)", 0.2, 1000, 0.2], [null, "Pisum sativum (Green pea) seed oil -(Carrier -Coconut oil /Gingelly oil)", 0.2, 650, 0.13], [null, "Nigella sativa (Black cumin)seed oil", 0.2, 700, 0.14], [null, "Butylated Hydroxy Toluene (BHT)", 0.2, 300, 0.06], [null, "Winter green oil ( Natural Salicylic acid)", 0.1, 300, 0.03], [null, "Vitamin E Acetate / Tocopheryl Acetate", 0.1, 2089, 0.2089], [null, "Root Biotech Ho", 2, 12000, 24], [null, "Frag-<PERSON><PERSON><PERSON> 1766M", 0.12, 1200, 0.144], [null, null, null, "Total", "470/Kg"]], "Costing": [["Product ", null, "Anti-Hairfall Hair oil ", null, null, null, null, 45790], ["Client", null, "Agrizy Wellness"], ["Manufacturer Name"], ["Account "], ["Pack Size", null, 100, "ml"], ["Pack Size with tolerance", null, 100.5, "ml"], ["<PERSON><PERSON> Si<PERSON> ", null, 100, "Kg"], ["Batch Quantity", null, 996, "Nos"], ["Yield 95%", null, 947, "Nos"], [], ["Raw Material Cost"], ["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost", "Freight", "Price in Rs. per Kg", "Value in Rs.", "% contri"], [1, "<PERSON><PERSON><PERSON> annuus (Sunflower) Seed Oil", 76.68, 76.68, 170, 20, 190, 14569.2, 0.****************], [2, "Linum usitatissimum (Flaxseed / Linseed) Seed Oil", 9, 9, 280, 20, 300, 2700, 0.*****************], [3, "Glycine max (Soybean) Seed Oil", 2, 2, 168, 20, 188, 376, 0.007972097658196313], [4, "Sesamum indicum (Sesame) Seed Oil", 2, 2, 250, 20, 270, 540, 0.*****************], [5, "Or<PERSON>za sativa (Rice Bran) Oil", 2, 2, 260, 20, 280, 560, 0.*****************], [6, "Rici<PERSON> communis (Castor) Oil", 2, 2, 280, 20, 300, 600, 0.012721432433291989], [7, "Cocos nucifera (Coconut) Oil", 2, 2, 320, 20, 340, 680, 0.014417623424397587], [8, "Root Biotech Ho", 2, 2, 12000, 20, 12020, 24040, 0.5097053928272324], [9, "Eclipta alba (Bhringraj) Leaf Oil (in Coconut Oil / Gingelly Oil)", 0.4, 0.4, 1300, 20, 1320, 528, 0.011194860541296951], [10, "<PERSON><PERSON><PERSON><PERSON> officinalis (Rosemary) Leaf Oil (in Coconut Oil / Gingelly Oil)", 0.4, 0.4, 1600, 20, 1620, 648, 0.013739147027955348], [11, "Citrus limon (Lemon) Peel Oil", 0.2, 0.2, 1600, 20, 1620, 324, 0.006869573513977674], [12, "Prunus amygdalus dulcis (Sweet Almond) Oil", 0.2, 0.2, 1300, 20, 1320, 264, 0.0055974302706484754], [13, "Murraya koenigii (Curry Leaf) Oil (in Coconut Oil / Gingelly Oil)", 0.2, 0.2, 1880, 20, 1900, 380, 0.008056907207751592], [14, "Pisum sativum (Green Pea) Seed Oil (in Coconut Oil / Gingelly Oil)", 0.2, 0.2, 1300, 20, 1320, 264, 0.0055974302706484754], [15, "<PERSON><PERSON> sativa (Black Cumin) Seed Oil", 0.2, 0.2, 980, 20, 1000, 200, 0.004240477477763996], [16, "Butylated Hydroxy Toluene (BHT) (Antioxidant)", 0.2, 0.2, 300, 20, 320, 64, 0.0013569527928844788], [17, "Fragrance: <PERSON><PERSON><PERSON>", 0.12, 0.12, 1200, 20, 1220, 146.4, 0.0031040295137232453], [18, "Wintergreen Oil (Natural source of Salicylic Acid)", 0.1, 0.1, 680, 20, 700, 70, 0.0014841671172173988], [19, "Tocopheryl Acetate (Vitamin E Acetate)", 0.1, 0.1, 2089, 20, 2109, 210.9, 0.004471583500302134], [null, "Testing Charges", null, null, null, null, null, 0, 0], [null, "Total", 100.00000000000003, 100.00000000000003, null, null, null, 47164.5, 1], [], ["Total Bulk Cost", null, 47164.5, null, null, null, "SKU", null, "100ml"], ["Total Bulk Cost with 95% recovery ", null, 49646.9, null, null, null, "RM cost Per Unit", null, 52.5], [], ["Packaging Material Cost"], ["Sr. No.", "Packaging Material", "Qty ", "Order Qty", "<PERSON><PERSON><PERSON>", "Basic Cost", "Freight", "Total price", "Per Unit Cost 100ml"], [1, "PET/HDPE Round Bottle 100ml", 1, 1000, 1000, 8, 0.4, 8.4, 8400], [2, "Flip Top cap", 1, 1000, 1000, 2, 0.1, 2.1, 2100], [3, "Label / Shrink Sleeves 100ml", 1, 1000, 1000, 2, 0.1, 2.1, 2100], [4, "Mono Carton 100ml", 1, 1000, 1000, 5.5, 0.275, 5.775, 5775], [5, "Plain Shrink Sleeves", 1, 1000, 1000, 1.5, 0.0750*************, 1.575, 1575], [6, "Outer Carton 100ml", 0.16666666666666666, 1000, 167, 15, 0.75, 15.75, 2630.25], [7, "5-<PERSON>ly Shipper Box 100ml", 0.018518518518518517, 1000, 19, 90, 4.5, 94.5, 1795.5], [8, "Plain Brown BOPP Tape 100ml", 0.05, 1000, 1, 50, 2.5, 52.5, 52.5], [], [null, null, null, null, "PM Cost/ Unit", null, null, null, 25.8], [null, null, 22.28, null, "PM Cost/ Unit with Wastage", null, null, 0.03, 26.6], [null, null, null, null, "RM Per Unit Cost", null, null, null, 52.5], [null, null, null, null, "CC/PC Charges", null, null, 0.25, 19.8], [null, null, null, null, "Product Cost (Ex-Works)", null, null, null, 98.9], [null, null, null, null, "Our Selling Price (INR/ Unit)", null, null, null, 123.69999999999999], [null, null, null, null, "% Margin", null, null, null, 0.2507583417593527], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []]}, "ingredients": [], "costStructure": {"totalCost": 49646.9}, "patterns": ["cost_analysis"]}, {"fileName": "Costing - Antioxidant Face Serum.xlsx", "sheets": {"Sheet1": [["Antioxidant serum for face", null, "Per kg RM Cost"], ["Vitamin C (3-O-Ethyl-L-Ascorbic acid)", 10, 12000, 120], ["Vitamin E (Tocopherol)", 0.1, 2500, 0.25], ["Ferulic Acid", 0.5, 13000, 6.5], ["CoQ10 (Ubiquinone)", 0.1, 15000, 1.5], ["Niacinamide", 4, 600, 2.4], ["4 butylresorcinol", 1, 15000, 15], ["Green Tea Extract", 0.5, 600, 0.3], ["<PERSON><PERSON>xant<PERSON>", 0.1, 25000, 2.5], ["hyaluronic acid", 0.3, 35000, 10.5], ["glutathione reductase", 0.5, 12500, 6.25], ["silymarin ", 0.25, 3000, 0.75], ["pentavitin", 1, 5500, 5.5], ["Glycerine", 2, 300, 0.6], ["Propylene Glycol", 3, 400, 1.2], ["<PERSON>.", 3, 550, 1.65], ["<PERSON><PERSON>.", 3, 550, 1.65], ["Phenoxyethanol + Ethylhexylglycerin", 0.9, 800, 0.72], [], ["1800/kg"]], "Costing": [["Product ", null, "Anti-Oxidant Face Serum", null, null, null, null, 45771], ["Client"], ["Manufacturer Name"], ["Account "], ["Pack Size", null, 30, "ml"], ["Pack Size with tolerance", null, 30.25, "ml"], ["<PERSON><PERSON> Si<PERSON> ", null, 100, "Kg"], ["Batch Quantity", null, 3306, "Nos"], ["Yield 95%", null, 3141, "Nos"], [], ["Raw Material Cost"], ["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost", "Freight", "Price in Rs. per Kg", "Value in Rs.", "% contri"], [1, "Purified Water", 69.75, 69.75, 2.5, null, 2.5, 174.375, 0.0009816787085921588], [2, "Vitamin C (3-O-Ethyl-L-Ascorbic Acid)", 10, 10, 12000, 20, 12020, 120200, 0.****************], [3, "Niacinamide", 4, 4, 600, 20, 620, 2480, 0.013961652744421814], [4, "Propylene Glycol", 3, 3, 300, 20, 320, 960, 0.005404510739776186], [5, "<PERSON>", 3, 3, 550, 20, 570, 1710, 0.*****************], [6, "<PERSON><PERSON>", 3, 3, 550, 20, 570, 1710, 0.*****************], [7, "Glycerine", 2, 2, 150, 20, 170, 340, 0.0019140975536707325], [8, "4-Butylresorcinol", 1, 1, 15000, 20, 15020, 15020, 0.*****************], [9, "<PERSON><PERSON><PERSON><PERSON>", 1, 1, 5500, 20, 5520, 5520, 0.03107593675371307], [10, "Phenoxyethanol + Ethylhexylglycerin", 0.9, 0.9, 1000, 20, 1020, 918, 0.005168063394910978], [11, "Ferulic Acid", 0.5, 0.5, 13000, 20, 13020, 6510, 0.03664933845410726], [12, "Green Tea Extract", 0.5, 0.5, 600, 20, 620, 310, 0.0017452065930527267], [13, "Glutathione reductase", 0.5, 0.5, 12500, 20, 12520, 6260, 0.035241913782290545], [14, "Hyaluronic Acid", 0.3, 0.3, 35000, 20, 35020, 10506, 0.05914561440842563], [15, "<PERSON><PERSON><PERSON><PERSON>", 0.25, 0.25, 3000, 20, 3020, 755, 0.004250422508886479], [16, "Vitamin E (Tocopherol)", 0.1, 0.1, 2500, 20, 2520, 252, 0.0014186840691912488], [17, "CoQ10 (Ubiquinone)", 0.1, 0.1, 15000, 20, 15020, 1502, 0.008455807428274825], [18, "<PERSON><PERSON>xant<PERSON>", 0.1, 0.1, 25000, 20, 25020, 2502, 0.014085506115541685], [null, "Testing Charges", null, null, null, null, null, 0, 0], [null, "Total", 99.99999999999999, 99.99999999999999, null, null, null, 177629.4, 1], [], ["Total Bulk Cost", null, 177629.4, null, null, null, "SKU", null, "30ml"], ["Total Bulk Cost with 95% recovery ", null, 186978.4, null, null, null, "RM cost Per Unit", null, 59.6], [], ["Packaging Material Cost"], ["Sr. No.", "Ingredients", "Qty ", "Order Qty", "<PERSON><PERSON><PERSON>", "Basic Cost", "Freight", "Total price", "Per Unit Cost 30ml"], [1, "Amber Frosted Glass bottle 30ml", 1, 3334, 3334, 6.5, 0.65, 7.15, 23838.1], [2, "18mm dropper set (Black sleeve, Black teat & clear tube)", 1, 3334, 3334, 7.5, 0.75, 8.25, 27505.5], [3, "Label Glass bottle 30ml", 1, 3334, 3334, 3, 0.3, 3.3, 11002.2], [4, "Mono Carton (with top and bottom holder) 30ml", 1, 3334, 3334, 9, 0.9, 9.9, 33006.6], [5, "Plain Shrink Sleeves", 1, 3334, 3334, 1.5, 0.15, 1.65, 5501.1], [6, "Outer Carton (16x30ml)", 0.0625, 3334, 209, 15, 1.5, 16.5, 3448.5], [7, "5-<PERSON>ly Shipper Box (128x30ml)", 0.0078125, 3334, 27, 60, 6, 66, 1782], [8, "Plain Brown BOPP Tape", 0.05, 3334, 2, 50, 5, 55, 110], [], [null, null, null, null, "PM Cost/ Unit", null, null, null, 33.9], [null, null, 22.28, null, "PM Cost/ Unit with Wastage", null, null, 0.03, 35], [null, null, null, null, "RM Per Unit Cost", null, null, null, 59.6], [null, null, null, null, "CC/PC Charges", null, null, 0.25, 23.700000000000003], [null, null, null, null, "Product Cost (Ex-Works)", null, null, null, 118.3], [null, null, null, null, "Our Selling Price (INR/ Unit)", null, null, null, 147.9], [null, null, null, null, "% Margin", null, null, null, 0.25021132713440414], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "Sheet2": [["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost"], [1, "Purified Water", 69.75, 69.75, 2.5], [2, "Vitamin C (3-O-Ethyl-L-Ascorbic Acid)", 10, 10, 12000], [6, "Niacinamide", 4, 4, 600], [15, "Propylene Glycol", 3, 3, 300], [16, "<PERSON>", 3, 3, 550], [17, "<PERSON><PERSON>", 3, 3, 550], [14, "Glycerine", 2, 2, 150], [7, "4-Butylresorcinol", 1, 1, 15000], [13, "<PERSON><PERSON><PERSON><PERSON>", 1, 1, 5500], [18, "Phenoxyethanol + Ethylhexylglycerin", 0.9, 0.9, 1000], [4, "Ferulic Acid", 0.5, 0.5, 13000], [8, "Green Tea Extract", 0.5, 0.5, 600], [11, "Glutathione reductase", 0.5, 0.5, 12500], [10, "Hyaluronic Acid", 0.3, 0.3, 35000], [12, "<PERSON><PERSON><PERSON><PERSON>", 0.25, 0.25, 3000], [3, "Vitamin E (Tocopherol)", 0.1, 0.1, 2500], [5, "CoQ10 (Ubiquinone)", 0.1, 0.1, 15000], [9, "<PERSON><PERSON>xant<PERSON>", 0.1, 0.1, 25000]]}, "ingredients": [{"name": "Purified Water", "percentage": 69.75, "cost": 2.5, "rowIndex": 1}, {"name": "Vitamin C (3-O-Ethyl-L-Ascorbic Acid)", "percentage": 10, "cost": 12000, "rowIndex": 2}, {"name": "Niacinamide", "percentage": 4, "cost": 600, "rowIndex": 3}, {"name": "Propylene Glycol", "percentage": 3, "cost": 300, "rowIndex": 4}, {"name": "<PERSON>", "percentage": 3, "cost": 550, "rowIndex": 5}, {"name": "<PERSON><PERSON>", "percentage": 3, "cost": 550, "rowIndex": 6}, {"name": "Glycerine", "percentage": 2, "cost": 150, "rowIndex": 7}, {"name": "4-Butylresorcinol", "percentage": 1, "cost": 15000, "rowIndex": 8}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "percentage": 1, "cost": 5500, "rowIndex": 9}, {"name": "Phenoxyethanol + Ethylhexylglycerin", "percentage": 0.9, "cost": 1000, "rowIndex": 10}, {"name": "Ferulic Acid", "percentage": 0.5, "cost": 13000, "rowIndex": 11}, {"name": "Green Tea Extract", "percentage": 0.5, "cost": 600, "rowIndex": 12}, {"name": "Glutathione reductase", "percentage": 0.5, "cost": 12500, "rowIndex": 13}, {"name": "Hyaluronic Acid", "percentage": 0.3, "cost": 35000, "rowIndex": 14}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "percentage": 0.25, "cost": 3000, "rowIndex": 15}, {"name": "Vitamin E (Tocopherol)", "percentage": 0.1, "cost": 2500, "rowIndex": 16}, {"name": "CoQ10 (Ubiquinone)", "percentage": 0.1, "cost": 15000, "rowIndex": 17}, {"name": "<PERSON><PERSON>xant<PERSON>", "percentage": 0.1, "cost": 25000, "rowIndex": 18}], "costStructure": {"totalCost": 186978.4}, "patterns": ["cost_analysis", "ingredient_list", "cost_analysis"]}, {"fileName": "Costing - Daily Pigmentation Defense SPF 50 PA++++ Lotion (Syscutis).xlsx", "sheets": {"Sheet1": [[null, "Daily Pigmentation Defense SPF 50 PA++++ Lotion"], [], [1, "Dm Water", 58.8], [null, "Simulgel INS 100", 2, 4000, 8], [null, "Solagum AX", 0.3, 5400, 1.62], [null, "STV 455", 2, 4000, 8], [null, "montanov 82 (Cetearyl Alcohol and Coco-glucoside)", 2, 2500, 5], [null, "C12-15 Alkyle benzoate", 4, 500, 2], [null, "LK1021", 5, 350, 1.75], [null, "PG", 2, 300, 0.6], [null, "Euxyle P<PERSON> 9010", 0.9, 800, 0.72], [null, "Perfume", 0.5, 3000, 1.5], [null, "<PERSON><PERSON><PERSON>", 10, 3000, 30], [null, "Parsol 1789", 3, 1800, 5.4], [null, "Parsol MCX", 7.5, 1800, 13.5], [null, "Tranxaminc acid", 1, 10500, 10.5], [null, "Niacinamide", 1, 550, 0.55], [null, null, null, null, "900/ kg"], [], [null, "200/- Bottle 50 ml"]], "Costing": [[null, "Product ", "Daily Pigmentation Defense SPF 50 PA++++ Lotion", null, null, null, 45785], [null, "Client", "Sys<PERSON>is"], [null, "Manufacturing Site"], [null, "Pack Size", 50, null, "ml"], [null, "Pack Size with tolerance", 50.5, null, "ml"], [null, "<PERSON><PERSON> Si<PERSON> ", 100, null, "Kg"], [null, "Batch Quantity", 1981, null, "Nos"], [null, "Yield 95%", 1882, null, "Nos"], [], ["Raw Material Cost"], ["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost", "Freight", "Price in Rs. per Kg", "Value in Rs.", "% contri"], [1, "DM Water", 58.8, 58.8, 2, null, 2, 117.6, 0.0010813832878465758], [2, "Simulgel INS 100", 2, 2, 4800, 20, 4820, 9640, 0.08864400420783157], [3, "Solagum AX", 0.3, 0.3, 6480, 20, 6500, 1950, 0.01793110043623149], [4, "STV 455", 2, 2, 4920, 20, 4940, 9880, 0.09085090887690621], [5, "Montanov 82 (Cetearyl Alcohol and Coco-glucoside)", 2, 2, 3000, 20, 3020, 6040, 0.055540434171711894], [6, "C12-15 Alkyl Benzoate", 4, 4, 600, 20, 620, 2480, 0.022804681580437997], [7, "LK1021", 5, 5, 420, 20, 440, 2200, 0.020229959466517577], [8, "PG (Propylene Glycol)", 2, 2, 360, 20, 380, 760, 0.006988531452069708], [9, "Euxyl PE 9010", 0.9, 0.9, 960, 20, 980, 882, 0.008110374658849319], [10, "Perfume", 0.5, 0.5, 3600, 20, 3620, 1810, 0.01664373937927128], [11, "Sunshield D", 10, 10, 3600, 20, 3620, 36200, 0.3328747875854256], [12, "Parsol 1789", 3, 3, 2160, 20, 2180, 6540, 0.06013815223228407], [13, "Parsol MCX", 7.5, 7.5, 2160, 20, 2180, 16350, 0.15034538058071018], [14, "Tranexamic Acid", 1, 1, 13200, 20, 13220, 13220, 0.12156366552152835], [15, "Niacinamide", 1, 1, 660, 20, 680, 680, 0.0062528965623781605], [null, "Total", 100, 100, null, null, null, 108749.6, 1], [], ["Total Bulk Cost", null, 108749.6, null, null, null, "SKU", null, "50g"], ["Total Bulk Cost with 95% recovery ", null, 114473.3, null, null, null, "RM cost Per Unit", null, 60.9], [], ["Packaging Material Cost"], ["Sr. No.", "Ingredients", "Qty ", "Order Qty", "<PERSON><PERSON><PERSON>", "Basic Cost", "Freight", "Total price", "Per Unit Cost 50g"], [1, "CoEx <PERSON>be with <PERSON><PERSON> 50g", 1, 2000, 2000, 8, 0.4, 8.4, 16800], [2, "Mono Carton 50g", 1, 2000, 2000, 4, 0.2, 4.2, 8400], [3, "Plain Shrink Sleeve for Mono Carton 50g", 1, 2000, 2000, 1.2, 0.06, 1.26, 2520], [4, "Outer Carton (12x50g)", 0.08333333333333333, 2000, 167, 15, 0.75, 15.75, 2630.2999999999997], [5, "5<PERSON><PERSON> (6x12x50g)", 0.013888888888888888, 2000, 28, 80, 4, 84, 2352], [6, "BOPP Tape Brown 72mm Width x 65 meter Length", 0.05, 2000, 2, 50, 2.5, 52.5, 105], [], [null, null, null, null, "PM Cost/ Unit", null, null, null, 17.5], [null, null, null, null, "PM Cost/ Unit with wastage", null, null, 0.03, 18.1], [null, null, null, null, "RM Per Unit Cost", null, null, null, 60.9], [null, null, null, null, "CC/PC Charges", null, null, 0.25, 19.75], [null, null, null, null, "Product Cost (Ex-Works)", null, null, null, 98.75], [null, null, null, null, "Our Selling Price (INR/ Unit)", null, null, null, 123.4375], [null, null, null, null, "% Margin", null, null, null, 25], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []]}, "ingredients": [], "costStructure": {"totalCost": 114473.3}, "patterns": []}, {"fileName": "Costing - Face wash  AHA BHA (Syscutis).xlsx", "sheets": {"Sheet1": [[null, "AHA BHA Face wash (Syscutis)"], [null, "Ingredients", null, "Per kg RM Cost"], [1, "DM Water", 62.03], [2, "Glycerine ", 3, 90, 0.27], [3, "Methyl Gluceth-20 (DOE 120 KC)", 2, 1400, 2.8], [4, "D Panthanol", 0.5, 500, 0.25], [5, "Glycolic acid", 0.5, 500, 0.25], [6, "Salicylic acid", 0.5, 400, 0.2], [7, "Propylene Glycol", 3, 300, 0.9], [8, "PEG-40 Hydrogenated Castor Oil", 1, 200, 0.2], [9, "Tween 20", 2, 300, 0.6], [10, "Vitamin E (Tocopheryl Acetate)", 0.01, 2100, 0.021], [11, "SLES (Sodium Laureth Sulfate)", 15, 67, 1.005], [12, "CAPB (Cocamidopropyl Betaine)", 6, 95, 0.57], [13, "CDEA (Cocamide DEA)", 2, 200, 0.4], [14, "Sodium Layryl Suecocinate", 2, 160, 0.32], [15, "Iscaguard", 0.15, 310, 0.0465], [16, "Perfume (Aloevera) -Creative", 0.3, 1000, 0.3], [17, "Color ( Blue) ", 0.01, 3000, 0.03], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "Costing": [[null, "Product ", "AHA BHA Face Wash", null, null, null, 45785], [null, "Client", "Sys<PERSON>is"], [null, "Manufacturing Site"], [null, "Pack Size", 100, null, "ml"], [null, "Pack Size with tolerance", 100.5, null, "ml"], [null, "<PERSON><PERSON> Si<PERSON> ", 100, null, "Kg"], [null, "Batch Quantity", 996, null, "Nos"], [null, "Yield 95%", 947, null, "Nos"], [], ["Raw Material Cost"], ["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost", "Freight", "Price in Rs. per Kg", "Value in Rs.", "% contri"], [1, "DM Water", 62.03000000000001, 62.03000000000001, 2, null, 2, 124.06000000000002, 0.013147520135650702], [2, "Glycerine", 3, 3, 150, 20, 170, 510, 0.05404832556167868], [3, "Methyl Gluceth-20 (DOE 120 KC)", 2, 2, 1400, 20, 1420, 2840, 0.3009749894022891], [4, "D-Panthenol", 0.5, 0.5, 500, 20, 520, 260, 0.02755404832556168], [5, "Glycolic Acid", 0.5, 0.5, 500, 20, 520, 260, 0.02755404832556168], [6, "Salicylic Acid", 0.5, 0.5, 400, 20, 420, 210, 0.02225519287833828], [7, "Propylene Glycol", 3, 3, 300, 20, 320, 960, 0.10173802458668928], [8, "PEG-40 Hydrogenated Castor Oil", 1, 1, 200, 20, 220, 220, 0.02331496396778296], [9, "Tween 20", 2, 2, 300, 20, 320, 640, 0.06782534972445951], [10, "Vitamin E (Tocopheryl Acetate)", 0.01, 0.01, 2100, 20, 2120, 21.2, 0.0022467147096227216], [11, "SLES (Sodium Laureth Sulfate)", 15, 15, 67, 20, 87, 1305, 0.13830012717253073], [12, "CAPB (Cocamidopropyl Betaine)", 6, 6, 130, 20, 150, 900, 0.0953793980500212], [13, "CDEA (Cocamide DEA)", 2, 2, 200, 20, 220, 440, 0.04662992793556592], [14, "Sodium Layryl Suecocinate", 2, 2, 160, 20, 180, 360, 0.03815175922000848], [15, "Iscaguard", 0.15, 0.15, 310, 20, 330, 49.5, 0.005245866892751166], [16, "Perfume (Aloevera) - Creative", 0.3, 0.3, 1000, 20, 1020, 306, 0.03242899533700721], [17, "Color (Blue) ", 0.01, 0.01, 3000, 20, 3020, 30.2, 0.0032005086901229335], [null, "Total", 100.0*************, 100.0*************, null, null, null, 9436, 1], [], ["Total Bulk Cost", null, 9436, null, null, null, "SKU", null, "100ml"], ["Total Bulk Cost with 95% recovery ", null, 9932.7, null, null, null, "RM cost Per Unit", null, 10.5], [], ["Packaging Material Cost"], ["Sr. No.", "Ingredients", "Qty ", "Order Qty", "<PERSON><PERSON><PERSON>", "Basic Cost", "Freight", "Total price", "Per Unit Cost 100ml"], [1, "CoEx Tube with Flip Top Cap 100ml", 1, 1000, 1000, 9.5, 0.47500000000000003, 9.975, 9975], [2, "Mono Carton 100ml", 1, 1000, 1000, 5, 0.25, 5.25, 5250], [3, "Plain Shrink Sleeve for Mono Carton 100ml", 1, 1000, 1000, 1.5, 0.0750*************, 1.575, 1575], [4, "Outer Carton (6x100ml)", 0.16666666666666666, 1000, 167, 15, 0.75, 15.75, 2630.2999999999997], [5, "5<PERSON><PERSON> (6x9x100ml)", 0.018518518518518517, 1000, 19, 60, 3, 63, 1197], [6, "BOPP Tape Brown 72mm Width x 65 meter Length", 0.05, 1000, 1, 50, 2.5, 52.5, 52.5], [], [null, null, null, null, "PM Cost/ Unit", null, null, null, 21.900000000000002], [null, null, null, null, "PM Cost/ Unit with wastage", null, null, 0.03, 22.6], [null, null, null, null, "RM Per Unit Cost", null, null, null, 10.5], [null, null, null, null, "CC/PC Charges", null, null, 0.25, 8.275], [null, null, null, null, "Product Cost (Ex-Works)", null, null, null, 41.375], [null, null, null, null, "Our Selling Price (INR/ Unit)", null, null, null, 51.71875], [null, null, null, null, "% Margin", null, null, null, 25], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []]}, "ingredients": [], "costStructure": {"totalCost": 9932.7}, "patterns": []}, {"fileName": "Costing - Face wash for normal to dry skin.xlsx", "sheets": {"Sheet1": [[null, "Face wash normal to dry ", "100g", "Per Kg Price", "Per Kg cost"], [1, "DM Water", null, null, 0], [2, "Di-Sodium EDTA IP", 0.05, 1462, 0.0731*************], [3, "Glycerine IP", 3, 185, 0.555], [4, "Carbopol Ultrez 20/C10-30 Alkyl Acrylate Crosspolymer ", 0.8, 3468, 2.7744], [5, "Sodium Benzoate IP", 0.1, 130, 0.013], [6, "Potassium sorbate / Potassium (E,E)-hexa-2,4-dienoate IP", 0.1, 660, 0.066], [7, "Sodium Metabisulphite IP", 0.1, 462, 0.046200000000000005], [8, "Sodium Hydroxide", 0.3, 75, 0.0225], [9, "Bronopol / 2 Bromo -2 Nitropropane 1,3 Diol IP", 0.2, 1100, 0.22], [10, "Lauryl Glu<PERSON>ide/Plantacare 1200 ", 12.5, 214, 2.675], [11, "Coco-glucoside / Plantacare 818 ", 20, 225, 4.5], [12, "Jaguar C-162/Hydroxypropyl Guar (and) Hydroxypropyl Guar Hydroxypropyltrimonium Chloride", 0.025, 6000, 0.15], [13, "PEG-7 Glyceryl Cocoate", 0.5, 450, 0.225], [14, "PEG-45 (M) / POLYOX™ WSR N-60K", 0.025, 4400, 0.11], [15, "Zemea", 1, 400, 0.4], [16, "Vitamin E Acetate / Tocopheryl Acetate", 0.1, 2089, 0.2089], [17, "Camelia <PERSON>ensis (Green Tea) leaf extract dry powder", 0.1, 963, 0.*****************], [18, "<PERSON><PERSON><PERSON> ( Apricot Shell powder 40-60#)", 0.02, 150, 0.003], [null, "Glycolic acid", 2, 300, 0.6], [21, "Tween 20/Polysorbate-20", 0.4, 200, 0.08], [22, "FRAG -GREEN APPLE -010323 W.S", 0.5, 820, 0.41], [23, "Walnut scrub/Juglans regia shell powder (60-80#)", 2, 60, 0.12]], "Costing": [["Product ", null, "Face wash Normal to Dry Skin ", null, null, null, 45791], ["Client", null, "Agrizy Wellness"], ["Manufacturer Name"], ["Account "], ["Pack Size", null, 100, null, "ml"], ["Pack Size with tolerance", null, 100.5, null, "ml"], ["<PERSON><PERSON> Si<PERSON> ", null, 100, null, "Kg"], ["Batch Quantity", null, 996, null, "Nos"], ["Yield 95%", null, 947, null, "Nos"], [], ["Raw Material Cost"], ["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost", "Freight", "Price in Rs. per Kg", "Value in Rs.", "% contri"], [1, "Purified Water", 55.68, 55.68, 2.5, 0, 2.5, 139.2, 0.008691091631078144], [2, "Coco-Glucoside / Plantacare 818", 20, 20, 225, 20, 245, 4900, 0.****************], [3, "Lauryl Glu<PERSON>ide / Plantacare 1200", 12.5, 12.5, 214, 20, 234, 2925, 0.*****************], [4, "Glycerine IP", 3, 3, 120, 20, 140, 420, 0.026223121300666816], [5, "Glycolic Acid", 2, 2, 500, 20, 520, 1040, 0.*****************], [6, "<PERSON><PERSON><PERSON> / Juglans Regia Shell Powder (60–80#)", 2, 2, 85, 20, 105, 210, 0.013111560650333408], [7, "Zemea", 1, 1, 400, 20, 420, 420, 0.026223121300666816], [8, "Carbopol Ultrez 20 / C10-30 Alkyl Acrylate Crosspolymer", 0.8, 0.8, 3468, 20, 3488, 2790.4, 0.1742214230413826], [9, "PEG-7 Glyceryl Cocoate", 0.5, 0.5, 450, 20, 470, 235, 0.014672460727754053], [10, "FRAG - Green Apple - 010323 W.S", 0.5, 0.5, 900, 20, 920, 460, 0.028720561424539846], [11, "Tween 20 / Polysorbate-20", 0.4, 0.4, 200, 20, 220, 88, 0.005494368272520666], [12, "Sodium Hydroxide", 0.3, 0.3, 160, 20, 180, 54, 0.003371544167228591], [13, "Glowspheres BD (0.84 to 1.19mm)", 0.25, 0.25, 2750, 20, 2770, 692.5, 0.043236932144551835], [14, "Glowspheres WD (0.84 to 1.19mm)", 0.25, 0.25, 2750, 20, 2770, 692.5, 0.043236932144551835], [15, "Bronopol / 2-Bromo-2-Nitropropane-1,3-Diol IP", 0.2, 0.2, 1100, 20, 1120, 224, 0.01398566469368897], [16, "Sodium Benzoate IP", 0.1, 0.1, 130, 20, 150, 15, 0.0009365400464523863], [17, "Potassium Sorbate / Potassium (E,E)-hexa-2,4-dienoate IP", 0.1, 0.1, 660, 20, 680, 68, 0.004245648210584151], [18, "Sodium Metabisulphite IP", 0.1, 0.1, 462, 20, 482, 48.2, 0.0030094153492670017], [19, "Vitamin E Acetate / Tocopheryl Acetate", 0.1, 0.1, 2089, 20, 2109, 210.9, 0.013167753053120552], [20, "Came<PERSON>a <PERSON> (Green Tea) Leaf Extract Dry Powder", 0.1, 0.1, 963, 20, 983, 98.3*************, 0.006137459104417972], [21, "Di-Sodium EDTA IP", 0.05, 0.05, 400, 20, 420, 21, 0.0013111560650333409], [22, "Jaguar C-162 / Hydroxypropyl Guar (and) Hydroxypropyl Guar Hydroxypropyltrimonium Chloride", 0.025, 0.025, 6000, 20, 6020, 150.5, 0.009396618466072277], [23, "PEG-45 (M) / POLYOX™ WSR N-60K", 0.025, 0.025, 4400, 20, 4420, 110.5, 0.006899178342199246], [24, "<PERSON><PERSON><PERSON> (Apricot Shell Powder 40-60#)", 0.02, 0.02, 150, 20, 170, 3.4, 0.00021228241052920755], [null, "Testing Charges", null, null, null, null, null, 0, 0], [null, "Total", 99.99999999999999, 99.99999999999999, null, null, null, 16016.4, 1], [], ["Total Bulk Cost", null, 16016.4, null, null, null, "SKU", null, "100 ml"], ["Total Bulk Cost with 95% recovery ", null, 16859.399999999998, null, null, null, "RM cost Per Unit", null, 17.900000000000002], [], ["Packaging Material Cost"], ["Sr. No.", "Ingredients", "Qty ", "Order Qty", "<PERSON><PERSON><PERSON>", "Basic Cost", "Freight", "Total price", "Per Unit Cost 100ml"], [1, "Seamless CoEx Tube 100ml", 1, 1000, 1000, 8, 0.8, 8.8, 8800], [2, "Mono Carton 100ml", 1, 1000, 1000, 5.5, 0.55, 6.05, 6050], [3, "Plain Shrink Sleeves", 1, 1000, 1000, 1.5, 0.15000000000000002, 1.65, 1650], [4, "Outer Carton 100ml", 0.08333333333333333, 1000, 84, 15, 1.5, 16.5, 1386], [5, "5 ply shipper 100ml", 0.013888888888888888, 1000, 14, 80, 8, 88, 1232], [6, "Brown BOPP Tape 100ml", 0.05, 1000, 10, 50, 5, 55, 550], [], [null, null, null, null, "PM Cost/ Unit", null, null, null, 20.8], [null, null, 22.28, null, "PM Cost/ Unit with Wastage", null, null, 0.03, 21.5], [null, null, null, null, "RM Per Unit Cost", null, null, null, 17.900000000000002], [null, null, null, null, "CC/PC Charges", null, null, 0.25, 9.9], [null, null, null, null, "Product Cost (Ex-Works)", null, null, null, 49.3], [null, null, null, null, "Our Selling Price (INR/ Unit)", null, null, null, 59.2], [null, null, null, null, "% Margin", null, null, null, 0.2008113590263693], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []]}, "ingredients": [], "costStructure": {"totalCost": 16859.399999999998}, "patterns": ["cost_analysis"]}, {"fileName": "Costing - Gold AHA+BHA based facewash for oily skin.xlsx", "sheets": {"Sheet1": [["Phase-A"], [null, "DM Water", "QS", "Per Kg Price ", "Per Kg Cost"], [null, "Methyl Gluceth DOE 120KC RSPO MB (PEG-120 Methyl Glucose Dioleate)-Berant", 2, 1450, 2.9], [null, "Glycerin ", 2.5, 120, 0.3], ["Phase-B", null, null, null, 0], [null, "Lactic acid ", 0.5, 300, 0.15], [null, "Glycolic acid", 0.5, 500, 0.25], [null, "Salicylic acid", 0.5, 350, 0.175], [null, "PEG-40 (Hydrogenated castor oil )", 1, 300, 0.3], ["Phase-C", null, null, null, 0], [null, "Sodium lauroyl sarcosinate", 10, 175, 1.75], [null, "CDEA (\r\nCOCODIETHANOLAMIDE )", 6, 350, 2.1], [null, "AOS ( Alpha Olefin Sulfonate)", 15, 75, 1.125], [null, "CAPB(Cocamidopropyl betaine )", 10, 120, 1.2], ["Phase-D", null, null, null, 0], [null, "ST 100 (Inovision)-Acrylate Copolymer", 2.7, 350, 0.945*************], [null, "Water", 2.7, null, 0], ["Phase-E", null, null, null, 0], [null, "Color  (KZ-7221)-Koel-Zaree Gold Lustre", 0.16, 1500, 0.24], [null, "ischgurd CMI (methylchloroisothiazolinone and methylisothiazolinone)", 0.15, 400, 0.06], [null, "Sodium lauroyl sarcosinate", 5, 350, 1.75]], "Costing": [["Product ", null, "Gold AHA-BHA Face Wash for Oily Skin", null, null, null, 45791], ["Client", null, "Agrizy Wellness"], ["Manufacturer Name"], ["Account "], ["Pack Size", null, 100, null, "ml"], ["Pack Size with tolerance", null, 100.5, null, "ml"], ["<PERSON><PERSON> Si<PERSON> ", null, 100, null, "Kg"], ["Batch Quantity", null, 996, null, "Nos"], ["Yield 95%", null, 947, null, "Nos"], [], ["Raw Material Cost"], ["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost", "Freight", "Price in Rs. per Kg", "Value in Rs.", "% contri"], [1, "Purified Water", 43.99, 43.99, 2.5, 0, 2.5, 109.**************, 0.007703920085182695], [2, "Sodium Lauroyl Sarcosinate", 15, 15, 220, 20, 240, 3600, 0.*****************], [3, "AOS (Alpha Olefin Sulfonate)", 15, 15, 75, 20, 95, 1425, 0.*****************], [4, "CAPB (Cocamidopropyl Betaine)", 10, 10, 120, 20, 140, 1400, 0.*****************], [5, "CDEA (Cocodiethanolamide)", 6, 6, 350, 20, 370, 2220, 0.****************], [6, "ST 100 (Inovision) – Acrylate Copolymer", 2.7, 2.7, 350, 20, 370, 999.*************, 0.*****************], [7, "Glycerin ", 2.5, 2.5, 120, 20, 140, 350, 0.*********281327054], [8, "Methyl Gluceth DOE 120KC RSPO MB (PEG-120 Methyl Glucose Dioleate) – Berant", 2, 2, 1450, 20, 1470, 2940, 0.20595158036314726], [9, "PEG-40 (Hydrogenated Castor Oil)", 1, 1, 300, 20, 320, 320, 0.022416498542927595], [10, "Lactic acid ", 0.5, 0.5, 300, 20, 320, 160, 0.011208249271463798], [11, "Glycolic acid", 0.5, 0.5, 500, 20, 520, 260, 0.01821340506612867], [12, "Salicylic acid", 0.5, 0.5, 350, 20, 370, 185, 0.012959538220130015], [13, "Color (KZ-7221) – Koel-Zaree Gold Lustre", 0.16, 0.16, 1500, 20, 1520, 243.20000000000002, 0.01703653889262497], [14, "Ischgurd CMI (Methylchloroisothiazolinone and Methylisothiazolinone)", 0.15, 0.15, 400, 20, 420, 63, 0.00441324815063887], [null, "Testing Charges", null, null, null, null, null, 0, 0], [null, "Total", 100.0*************, 100.0*************, null, null, null, 14275.2, 1], [], ["Total Bulk Cost", null, 14275.2, null, null, null, "SKU", null, "100 ml"], ["Total Bulk Cost with 95% recovery ", null, 15026.6, null, null, null, "RM cost Per Unit", null, 15.9], [], ["Packaging Material Cost"], ["Sr. No.", "Ingredients", "Qty ", "Order Qty", "<PERSON><PERSON><PERSON>", "Basic Cost", "Freight", "Total price", "Per Unit Cost 100ml"], [1, "Seamless CoEx Tube 100ml", 1, 1000, 1000, 8, 0.8, 8.8, 8800], [2, "Mono Carton 100ml", 1, 1000, 1000, 5.5, 0.55, 6.05, 6050], [3, "Plain Shrink Sleeves", 1, 1000, 1000, 1.5, 0.15000000000000002, 1.65, 1650], [4, "Outer Carton 100ml", 0.08333333333333333, 1000, 84, 15, 1.5, 16.5, 1386], [5, "5 ply shipper 100ml", 0.013888888888888888, 1000, 14, 80, 8, 88, 1232], [6, "Brown BOPP Tape 100ml", 0.05, 1000, 10, 50, 5, 55, 550], [], [null, null, null, null, "PM Cost/ Unit", null, null, null, 20.8], [null, null, 22.28, null, "PM Cost/ Unit with Wastage", null, null, 0.03, 21.5], [null, null, null, null, "RM Per Unit Cost", null, null, null, 15.9], [null, null, null, null, "CC/PC Charges", null, null, 0.25, 9.4], [null, null, null, null, "Product Cost (Ex-Works)", null, null, null, 46.8], [null, null, null, null, "Our Selling Price (INR/ Unit)", null, null, null, 58.5], [null, null, null, null, "% Margin", null, null, null, 0.25000000000000006], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []]}, "ingredients": [], "costStructure": {"totalCost": 15026.6}, "patterns": []}, {"fileName": "Costing - Pore Minimizer <PERSON> (Oliva).xlsx", "sheets": {"Pore Minimizer toner": [[null, "Pore minimizer toner"], [1, "DM Water", "QS"], [2, "Di-Sodium EDTA", 0.1, 400, 0.04], [3, "Propylene glycol", 4, 300, 1.2], [4, "Euxyle PE 9010", 0.9, 800, 0.72], [5, "zinc PCA", 1, 1200, 1.2], [6, "Niacinamide", 2, 600, 1.2], [7, "Polysorbate 20", 2, 380, 0.76], [8, "Aloe barbadensis juice", 5, 600, 3], [9, "Rosa centifolia", 0.3, 600, 0.18], [10, "Citrus lemon", 0.2, 600, 0.12], [11, "Citric acid", 0.1, 600, 0.06], [12, "Fragrance ", 0.1, 1500, 0.15], [13, "Salicylic acid ", 2, 400, 0.8], [14, "BS ceramix-V (DSM)", 0.1, 26000, 2.6], [15, "<PERSON><PERSON><PERSON>", 0.5, 500, 0.25], [null, null, null, null, "130/- kg cost"]], "Costing": [["Product ", null, "<PERSON>re Minimizer Toner", null, null, null, null, 45771], ["Client", null, "Sreyas Holistic Remedies Pvt Ltd"], ["Manufacturer Name"], ["Account "], ["Pack Size", null, 100, "ml"], ["Pack Size with tolerance", null, 100.5, "ml"], ["<PERSON><PERSON> Si<PERSON> ", null, 100, "Kg"], ["Batch Quantity", null, 996, "Nos"], ["Yield 95%", null, 947, "Nos"], [], ["Raw Material Cost"], ["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost", "Freight", "Price in Rs. per Kg", "Value in Rs.", "% contri"], [1, "Purified Water", 81.7, 81.7, 2.5, null, 2.5, 204.25, 0.****************], [2, "Di-Sodium EDTA", 0.1, 0.1, 400, 20, 420, 42, 0.*****************], [3, "Propylene Glycol", 4, 4, 300, 20, 320, 1280, 0.***************], [4, "Euxyl PE 9010", 0.9, 0.9, 1000, 20, 1020, 918, 0.****************], [5, "Zinc PCA", 1, 1, 1200, 20, 1220, 1220, 0.**************], [6, "Niacinamide", 2, 2, 600, 20, 620, 1240, 0.***************], [7, "Polysorbate 20", 2, 2, 380, 20, 400, 800, 0.****************], [8, "Aloe Barbad<PERSON> Juice (Aloe Vera)", 5, 5, 350, 20, 370, 1850, 0.***************], [9, "<PERSON> (Rose Extract)", 0.3, 0.3, 600, 20, 620, 186, 0.0157890715856133], [10, "Citrus Lemon (Lemon Extract)", 0.2, 0.2, 600, 20, 620, 124, 0.0***************], [11, "Citric Acid", 0.1, 0.1, 600, 20, 620, 62, 0.00526302386187109], [12, "Fragrance ", 0.1, 0.1, 1500, 20, 1520, 152, 0.0129028972097485], [13, "Salicylic Acid", 2, 2, 400, 20, 420, 840, 0.071305484580189], [14, "BS Ceramix-V (DSM)", 0.1, 0.1, 26000, 20, 26020, 2602, 0.2208772272353], [15, "<PERSON><PERSON><PERSON>", 0.5, 0.5, 500, 20, 520, 260, 0.0220707452272013], [null, "Testing Charges", null, null, null, null, null, 0, 0], [null, "Total", 100, 100, null, null, null, 11780.3, 1], [], ["Total Bulk Cost", null, 11780.3, null, null, null, "SKU", null, "100ml"], ["Total Bulk Cost with 95% recovery ", null, 12400.4, null, null, null, "RM cost Per Unit", null, 13.1], [], ["Packaging Material Cost"], ["Sr. No.", "Ingredients", "Qty ", "Order Qty", "<PERSON><PERSON><PERSON>", "Basic Cost", "Freight", "Total price", "Per Unit Cost 100ml"], [1, "120ml Transparent Milan PET bottle", 1, 1000, 1000, 6, 0.6, 6.6, 6600], [2, "24mm Mist Spray Pump", 1, 1000, 1000, 7.5, 0.75, 8.25, 8250], [3, "Over cap with silver foiling", 1, 1000, 1000, 2, 0.2, 2.2, 2200], [4, "Label sticker 100ml", 1, 1000, 1000, 2.5, 0.25, 2.75, 2750], [5, "Mono Carton 100ml", 1, 1000, 1000, 6, 0.6, 6.6, 6600], [6, "Plain Shrink sleeve for Mono Carton", 1, 1000, 1000, 1.8, 0.18, 1.98, 1980], [7, "Outer Carton (12x100ml)", 0.0833333333333333, 1000, 84, 15, 1.5, 16.5, 1386], [8, "5<PERSON><PERSON> (72x100ml)", 0.0138888888888889, 1000, 14, 65, 6.5, 71.5, 1001], [9, "BOPP Tape Brown", 0.05, 1000, 1, 40, 4, 44, 44], [], [null, null, null, null, "PM Cost/ Unit", null, null, null, 32.6], [null, null, 22.28, null, "PM Cost/ Unit with Wastage", null, null, 0.03, 33.6], [null, null, null, null, "RM Per Unit Cost", null, null, null, 13.1], [null, null, null, null, "CC/PC Charges", null, null, 0.25, 11.7], [null, null, null, null, "Product Cost (Ex-Works)", null, null, null, 58.4], [null, null, null, null, "Our Selling Price (INR/ Unit)", null, null, null, 73], [null, null, null, null, "% Margin", null, null, null, 25], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []]}, "ingredients": [], "costStructure": {"totalCost": 12400.4}, "patterns": []}, {"fileName": "Costing - Psoraxib Lotion (Labrutiz).xlsx", "sheets": {"Costing": [["Product ", null, "Psoraxib lotion", null, null, null, 45791], ["Client", null, "<PERSON><PERSON><PERSON><PERSON>"], ["Manufacturer Name"], ["Account "], ["Pack Size", null, 100, 20, "ml"], ["Pack Size with tolerance", null, 100.5, 20.25, "ml"], ["<PERSON><PERSON> Si<PERSON> ", null, 100, 100, "Kg"], ["Batch Quantity", null, 996, 4939, "Nos"], ["Yield 95%", null, 947, 4693, "Nos"], [], ["Raw Material Cost"], ["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost", "Freight", "Price in Rs. per Kg", "Value in Rs.", "% contri"], [1, "DM Water", 66.28, 66.28, 2.5, 0, 2.5, 165.7, 0.0042728761330084195], [2, "Propylene Glycol", 5, 5, 250, 20, 270, 1350, 0.034812207480756585], [3, "Urea", 5, 5, 500, 20, 520, 2600, 0.*****************], [4, "Simulgel INS 100", 3, 3, 2800, 20, 2820, 8460, 0.*****************], [5, "Zemea", 2.5, 2.5, 400, 20, 420, 1050, 0.027076161373921787], [6, "Cresmer EW", 2.3, 2.3, 450, 20, 470, 1081, 0.027875552804961384], [7, "Glycerin", 2, 2, 120, 20, 140, 280, 0.007220309699712477], [8, "Cetyl Alcohol", 2, 2, 350, 20, 370, 740, 0.*****************], [9, "Niacinamide", 2, 2, 600, 20, 620, 1240, 0.03197565724158383], [10, "<PERSON> Hazel", 2, 2, 1360, 20, 1380, 2760, 0.07117162418288013], [11, "Stearic Acid", 1, 1, 180, 20, 200, 200, 0.005157364071223198], [12, "<PERSON>", 1, 1, 585, 20, 605, 605, 0.015601026315450173], [13, "DUB CO", 1, 1, 985, 20, 1005, 1005, 0.02591575445789657], [14, "Salicylic Acid", 1, 1, 375, 20, 395, 395, 0.010185794040665816], [15, "Olivitis 15", 1, 1, 890, 20, 910, 910, 0.02346600652406555], [16, "Euxyl PE 9010", 0.8, 0.8, 1000, 20, 1020, 816, 0.021042045410590647], [17, "Heliofeel", 0.75, 0.75, 9000, 20, 9020, 6765, 0.17444783970912467], [18, "Boswellin Super", 0.5, 0.5, 14000, 20, 14020, 7010, 0.1807656106963731], [19, "Squalene", 0.25, 0.25, 2000, 20, 2020, 505, 0.013022344279838574], [20, "Tinogard TT", 0.2, 0.2, 1760, 20, 1780, 356, 0.009180108046777291], [21, "Vitamin E Acetate", 0.2, 0.2, 2070, 20, 2090, 418, 0.010778890908856484], [22, "Sodium Hydroxide Pellets", 0.12, 0.12, 195, 20, 215, 25.8, 0.0006652999651877925], [23, "Disodium EDTA", 0.1, 0.1, 400, 20, 420, 42, 0.0010830464549568715], [null, "Testing Charges", null, null, null, null, null, 0, 0], [null, "Total", 100, 100, null, null, null, 38779.5, 1], [], ["Total Bulk Cost", null, 38779.5, null, null, "SKU", null, "100 ml", "20 ml"], ["Total Bulk Cost with 95% recovery ", null, 40820.6, null, null, "RM cost Per Unit", null, 43.2, 8.7], [], ["Packaging Material Cost"], ["Sr. No.", "Ingredients", "Qty ", "Order Qty", "<PERSON><PERSON><PERSON>", "Basic Cost", "Freight", "Total price", "Per Unit Cost 100ml", "Per Unit Cost 20ml"], [1, "Seamless CoEx Tube 100ml", 1, 1000, 1000, 8, 0.8, 8.8, 8800], [2, "Seamless CoEx Tube 20ml", 1, 5000, 5000, 4.5, 0.45, 4.95, null, 24750], [3, "Mono Carton 100ml", 1, 1000, 1000, 5.5, 0.55, 6.05, 6050], [4, "Mono Carton 20ml", 1, 5000, 5000, 3.5, 0.35000000000000003, 3.85, null, 19250], [5, "Outer Carton 100ml", 0.08333333333333333, 1000, 84, 15, 1.5, 16.5, 1386], [6, "Outer Carton 20ml", 0.08333333333333333, 5000, 417, 12, 1.2000000000000002, 13.2, null, 5504.4], [7, "5 ply shipper 100ml", 0.013888888888888888, 1000, 14, 80, 8, 88, 1232], [8, "5 ply shipper 20ml", 0.013888888888888888, 5000, 70, 65, 6.5, 71.5, null, 5005], [9, "Brown BOPP Tape 100ml", 0.05, 1000, 10, 50, 5, 55, 550], [10, "Brown BOPP Tape 20ml", 0.05, 5000, 10, 50, 5, 55, null, 550], [], [null, null, null, null, "PM Cost/ Unit", null, null, null, 20, 12], [null, null, 22.28, null, "PM Cost/ Unit with Wastage", null, null, 0.03, 20.6, 12.4], [null, null, null, null, "RM Per Unit Cost", null, null, null, 43.2, 8.7], [null, null, null, null, "CC/PC Charges", null, null, 0.25, 16, 5.3], [null, null, null, null, "Product Cost (Ex-Works)", null, null, null, 79.8*************, 26.4], [null, null, null, null, "Our Selling Price (INR/ Unit)", null, null, null, 99.8, 33], [null, null, null, null, "% Margin", null, null, null, 0.25062656641603986, 0.25000000000000006], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "Sheet1": [["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost"], [1, "DM Water", 66.28, 66.28, 2.5], [13, "Propylene Glycol", 5, 5, 300], [16, "Urea", 5, 5, 490], [4, "Simulgel INS 100", 3, 3, 3210], [17, "Zemea", 2.5, 2.5, 400], [5, "Cresmer EW", 2.3, 2.3, 300], [3, "Glycerin", 2, 2, 170], [8, "Cetyl Alcohol", 2, 2, 380], [15, "Niacinamide", 2, 2, 530], [20, "<PERSON> Hazel", 2, 2, 1360], [6, "Stearic Acid", 1, 1, 280], [7, "<PERSON>", 1, 1, 585], [10, "DUB CO", 1, 1, 985], [18, "Salicylic Acid", 1, 1, 375], [21, "Olivitis 15", 1, 1, 890], [22, "Euxyl PE 9010", 0.8, 0.8, 1030], [9, "Heliofeel", 0.75, 0.75, 9000], [14, "Boswellin Super", 0.5, 0.5, 14000], [11, "Squalene", 0.25, 0.25, 2000], [12, "Tinogard TT", 0.2, 0.2, 1760], [19, "Vitamin E Acetate", 0.2, 0.2, 2070], [23, "Sodium Hydroxide Pellets", 0.12, 0.12, 195], [2, "Disodium EDTA", 0.1, 0.1, 520]]}, "ingredients": [{"name": "DM Water", "percentage": 66.28, "cost": 2.5, "rowIndex": 1}, {"name": "Propylene Glycol", "percentage": 5, "cost": 300, "rowIndex": 2}, {"name": "Urea", "percentage": 5, "cost": 490, "rowIndex": 3}, {"name": "Simulgel INS 100", "percentage": 3, "cost": 3210, "rowIndex": 4}, {"name": "Zemea", "percentage": 2.5, "cost": 400, "rowIndex": 5}, {"name": "Cresmer EW", "percentage": 2.3, "cost": 300, "rowIndex": 6}, {"name": "Glycerin", "percentage": 2, "cost": 170, "rowIndex": 7}, {"name": "Cetyl Alcohol", "percentage": 2, "cost": 380, "rowIndex": 8}, {"name": "Niacinamide", "percentage": 2, "cost": 530, "rowIndex": 9}, {"name": "<PERSON> Hazel", "percentage": 2, "cost": 1360, "rowIndex": 10}, {"name": "Stearic Acid", "percentage": 1, "cost": 280, "rowIndex": 11}, {"name": "<PERSON>", "percentage": 1, "cost": 585, "rowIndex": 12}, {"name": "DUB CO", "percentage": 1, "cost": 985, "rowIndex": 13}, {"name": "Salicylic Acid", "percentage": 1, "cost": 375, "rowIndex": 14}, {"name": "Olivitis 15", "percentage": 1, "cost": 890, "rowIndex": 15}, {"name": "Euxyl PE 9010", "percentage": 0.8, "cost": 1030, "rowIndex": 16}, {"name": "Heliofeel", "percentage": 0.75, "cost": 9000, "rowIndex": 17}, {"name": "Boswellin Super", "percentage": 0.5, "cost": 14000, "rowIndex": 18}, {"name": "Squalene", "percentage": 0.25, "cost": 2000, "rowIndex": 19}, {"name": "Tinogard TT", "percentage": 0.2, "cost": 1760, "rowIndex": 20}, {"name": "Vitamin E Acetate", "percentage": 0.2, "cost": 2070, "rowIndex": 21}, {"name": "Sodium Hydroxide Pellets", "percentage": 0.12, "cost": 195, "rowIndex": 22}, {"name": "Disodium EDTA", "percentage": 0.1, "cost": 520, "rowIndex": 23}], "costStructure": {"totalCost": 40820.6}, "patterns": ["ingredient_list", "cost_analysis"]}, {"fileName": "Costing - Skin Brightening Cream (Syscutis).xlsx", "sheets": {"Sheet1": [[], [null, "SKIN BRIGHTENING CREAM (Syscutis )"], [null, "Ingredients", null, "Per kg RM Cost"], [1, "DM Water", 54.3], [2, "Sepiplus 400 (Polyacrylate-13 (and) Polyisobutene (and) Polysorbate 20)", 1.5], [3, "Montanov -68 (Cetearyl Alcohol (and) Coco-Glucoside)", 0.8], [4, "Arlacel 165 (Glyceryl Stearate (and) PEG-100 Stearate)", 1.5, 650], [5, "Caprylic/capric triglyceride-CCTG", 4], [6, "LK 1021(Cyclopentasiloxane, Dimethiconol, Phenyl Trimethicone, C12-15 Alkyl Benzoate, Dimethicone Crosspolymer )", 4], [7, "Tranexamic acid (True Value)", 10], [8, "L GLUTHTHIONE REDUCED ( Vijay)", 2], [9, "3-O-Ethyl-L-ascorbic Acid ( Barents)", 2, 11000], [10, "4-N-BUTYLRESORCIBOL (Kumar Organic)", 2], [11, "KOJIC ACID DIPALMITATE (Silpa Chemicals)", 2, 4000], [12, "B WHITE (Water (and) Butylene Glycol (and) Hydrogenated Lecithin (and) Sodium Oleate (and) Oligopeptide-68 (and) Disodium EDTA)", 2], [13, "TYROSTAT (Water (and) Glycerin (and) Rumex Occidentalis Extract)", 2], [14, "ALPHA ARBUTIN", 1, 9000], [15, "HYLAUIRONIC ACID (ChemFast)", 1], [16, "SunshieldEX D(Aqua (and) Ethylhexyl Methoxycinnamate (and) Butyl Methoxydibenzoylmethane (and) Benzophenone-3 (and) Phospholipids (and) Butylene Glycol (and) Phenoxyethanol)", 6, 3500], [17, "STV-455 (Titanium Dioxide (and) Aluminum Hydroxide (and) Stearic Acid)", 0.5], [18, "Euxyl pe 9010 ", 0.9], [19, "Cetyl Alcohol", 0.5], [20, "Stearuc acud", 0.5], [21, "Shea butter", 1], [22, "<PERSON> vanila (Frageance Specilist)-**********", 0.5]], "Cost Sheet": [["Product ", null, "Skin Brightening Cream", null, null, null, 45749], ["Client", null, "Syscutis Healthcare"], ["Manufacturer Name"], ["Account "], ["Pack Size", null, 30, 5, "g"], ["Pack Size with tolerance", null, 30.5, 5.5, "g"], ["<PERSON><PERSON> Si<PERSON> ", null, 100, 100, "Kg"], ["Batch Quantity", null, 3279, 18182, "Nos"], ["Yield 95%", null, 3116, 17273, "Nos"], [], ["Raw Material Cost"], ["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost", "Freight", "Price in Rs. per Kg", "Value in Rs.", "% contri"], [1, "DM Water", 54.3, 54.3, 2.5, null, 2.5, 135.75, 0.000312755733970318], [2, "Sepiplus 400 (Polyacrylate-13 (and) Polyisobutene (and) Polysorbate 20)", 1.5, 1.5, 5500, 20, 5520, 8280, 0.****************], [3, "Montanov 68 (Cetearyl Alcohol (and) Coco-Glucoside)", 0.8, 0.8, 3450, 20, 3470, 2776, 0.*****************], [4, "Arlacel 165 (Glyceryl Stearate (and) PEG-100 Stearate)", 1.5, 1.5, 600, 20, 620, 930, 0.*****************], [5, "Caprylic/Capric Triglyceride (CCTG)", 4, 4, 450, 20, 470, 1880, 0.*****************], [6, "LK 1021(Cyclopentasiloxane, Dimethiconol, Phenyl Trimethicone, C12-15 Alkyl Benzoate, Dimethicone Crosspolymer )", 4, 4, 1000, 20, 1020, 4080, 0.00939995134142835], [7, "Tranexamic Acid", 10, 10, 11000, 20, 11020, 110200, 0.253890842604266], [8, "L-Glutathione Reduced", 2, 2, 13000, 20, 13020, 26040, 0.0599938070908809], [9, "3-O-Ethyl-L-ascorbic Acid", 2, 2, 15000, 20, 15020, 30040, 0.0692094456609087], [10, "4-N-Butylresorcinol", 2, 2, 15000, 20, 15020, 30040, 0.0692094456609087], [11, "Kojic Acid Dipalmitate", 2, 2, 4000, 20, 4020, 8040, 0.0185234335257559], [12, "B White (Water (and) Butylene Glycol (and) Hydrogenated Lecithin (and) Sodium Oleate (and) Oligopeptide-68 (and) Disodium EDTA)", 2, 2, 49000, 20, 49020, 98040, 0.225875301351381], [13, "Tyrostat (Water (and) Glycerin (and) Rumex Occidentalis Extract)", 2, 2, 22000, 20, 22020, 44040, 0.101464180656006], [14, "Alpha Arbutin", 1, 1, 8000, 20, 8020, 8020, 0.0184773553329057], [15, "Hyaluronic Acid", 1, 1, 35000, 20, 35020, 35020, 0.0806829156805933], [16, "SunshieldEX D(Aqua (and) Ethylhexyl Methoxycinnamate (and) Butyl Methoxydibenzoylmethane (and) Benzophenone-3 (and) Phospholipids (and) Butylene Glycol (and) Phenoxyethanol)", 6, 6, 3500, 20, 3520, 21120, 0.0486585716497468], [17, "STV-455 (Titanium Dioxide (and) Aluminum Hydroxide (and) Stearic Acid)", 0.5, 0.5, 5000, 20, 5020, 2510, 0.00578281320269244], [18, "Euxyl PE 9010", 0.9, 0.9, 800, 20, 820, 738, 0.00170028531617013], [19, "Cetyl Alcohol", 0.5, 0.5, 350, 20, 370, 185, 0.000426223283863785], [20, "Stearic acid", 0.5, 0.5, 180, 20, 200, 100, 0.000230390964250695], [21, "Shea butter", 1, 1, 550, 20, 570, 570, 0.00131322849622896], [22, "<PERSON> vanila (Frageance Specilist)-**********", 0.5, 0.5, 2500, 20, 2520, 1260, 0.00290292614955876], [null, "Testing", null, null, null, null, null, 0, 0], [null, "Total", 100, 100, null, null, null, 434044.8, 1], [], ["Total Bulk Cost", null, 434044.8, null, null, "SKU", null, "30g", "5g"], ["Total Bulk Cost with 95% recovery ", null, 456889.3, null, null, "RM cost Per Unit", null, 146.7, 26.5], [], ["Packaging Material Cost"], ["Sr. No.", "Ingredients", "Qty ", "Order Qty", "<PERSON><PERSON><PERSON>", "Basic Cost", "Freight", "Total price", "Per Unit Cost 30g", "Per Unit Cost 5g"], [1, "<PERSON><PERSON> 30g", 1, 3334, 3334, 3.2, 0.16, 3.36, 11202.24], [2, "<PERSON><PERSON> 5g", 1, 20000, 20000, 2.05, 0.1025, 2.1525, null, 43050], [3, "Mono Carton 30g", 1, 3334, 3334, 2.8, 0.14, 2.94, 9801.96], [4, "Mono Carton 5g", 1, 20000, 20000, 1.96, 0.098, 2.058, null, 41160], [5, "Outer Carton 30g", 0.0833333333333333, 3334, 278, 13, 0.65, 13.65, 3794.7], [6, "Outer Carton 5g", 0.0833333333333333, 20000, 1667, 9, 0.45, 9.45, null, 15753.15], [7, "5 Ply shipper 30g", 0.0104166666666667, 3334, 35, 60, 3, 63, 2205], [8, "5 Ply shipper 5g", 0.0104166666666667, 20000, 209, 50, 2.5, 52.5, null, 10972.5], [9, "BOPP Tape 30g", 0.05, 3334, 2, 50, 2.5, 52.5, 105], [10, "BOPP Tape 5g", 0.05, 20000, 11, 50, 2.5, 52.5, null, 577.5], [], [null, null, null, null, "PM Cost/ Unit", null, null, null, 8.7, 6.5], [null, null, 22.28, null, "PM Cost/ Unit with Wastage", null, null, 0.03, 9, 6.7], [null, null, null, null, "RM Per Unit Cost", null, null, null, 146.7, 26.5], [null, null, null, null, "CC/PC Charges", null, null, 0.25, 39, 8.3], [null, null, null, null, "Product Cost (Ex-Works)", null, null, null, 194.7, 41.5], [null, null, null, null, "Our Selling Price (INR/ Unit)", null, null, null, 253.11, 53.95], [null, null, null, null, "% Margin", null, null, null, 30, 30], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []]}, "ingredients": [], "costStructure": {"totalCost": 456889.3}, "patterns": []}, {"fileName": "Costing - Skin Lightening Cream (<PERSON><PERSON><PERSON>).xlsx", "sheets": {"Costing": [["Product ", null, "Skin Lightening Cream", null, null, null, null, 45790], ["Client", null, "<PERSON><PERSON><PERSON>"], ["Manufacturer Name"], ["Account "], ["Pack Size", null, 30, "g"], ["Pack Size with tolerance", null, 30.25, "g"], ["<PERSON><PERSON> Si<PERSON> ", null, 100, "Kg"], ["Batch Quantity", null, 3306, "Nos"], ["Yield 95%", null, 3141, "Nos"], [], ["Raw Material Cost"], ["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost", "Freight", "Price in Rs. per Kg", "Value in Rs.", "% contri"], [1, "PURIFIED WATER", 62.5, 62.5, 2.5, 0, 2.5, 156.25, 0.0013535544990852138], [2, "ISOPROPYL PALMITATE", 4, 4, 330, 20, 350, 1400, 0.012127848311803515], [3, "KOJIC ACID DIPALMITATE", 4, 4, 4500, 20, 4520, 18080, 0.*****************], [4, "PROPYLENE GLYCOL", 4, 4, 180, 20, 200, 800, 0.006930199035316294], [5, "NIACINAMIDE", 4, 4, 600, 20, 620, 2480, 0.*****************], [6, "CETYL ALCOHOL", 3.2, 3.2, 205, 20, 225, 720, 0.006237179131784665], [7, "TWEEN 80", 3.2, 3.2, 200, 20, 220, 704, 0.006098575151078338], [8, "FINESTER 1650", 2.5, 2.5, 350, 20, 370, 925, 0.008013042634584465], [9, "CRESMER EW", 2.2, 2.2, 300, 20, 320, 704, 0.006098575151078338], [10, "MANDELIC ACID", 2, 2, 2800, 20, 2820, 5640, 0.048857903198979875], [11, "TOSPEARL 3000A", 2, 2, 6300, 20, 6320, 12640, 0.10949714475799745], [12, "GLYCERIN", 1, 1, 130, 20, 150, 150, 0.0012994123191218052], [13, "VIVINOL", 1, 1, 14000, 20, 14020, 14020, 0.12145173809391806], [14, "DC 2051", 1, 1, 3250, 20, 3270, 3270, 0.028327188556855353], [15, "EUXYL PE 9010", 0.8, 0.8, 1200, 20, 1220, 976, 0.00845484282308588], [16, "CYBRIGHT", 0.5, 0.5, 15435, 20, 15455, 7727.5, 0.06694139130675833], [17, "SODIUM HYDROXIDE PELLETS", 0.45, 0.45, 160, 20, 180, 81, 0.0007016826523257748], [18, "PEMULEN TR1", 0.4, 0.4, 6000, 20, 6020, 2408, 0.020859899096302044], [19, "SABIWHITE", 0.25, 0.25, 23000, 20, 23020, 5755, 0.04985411931030659], [20, "SHEA BUTTER", 0.2, 0.2, 525, 20, 545, 109, 0.0009442396185618451], [21, "TINOGARD TT", 0.2, 0.2, 1600, 20, 1620, 324, 0.002806730609303099], [22, "LICORICE EXTRACT 40% CA", 0.2, 0.2, 180000, 20, 180020, 36004, 0.3118936075844098], [23, "FRAGRANCE: ETERNAL ZEST TSG15-00716", 0.2, 0.2, 1250, 20, 1270, 254, 0.0022003381937129233], [24, "DISODIUM EDTA", 0.1, 0.1, 450, 20, 470, 47, 0.00040714919332483226], [25, "SODIUM METABISULFITE", 0.1, 0.1, 600, 20, 620, 62, 0.0005370904252370128], [null, "Testing Charges", null, null, null, null, null, 0, 0], [null, "Total", 100.0*************, 100.0*************, null, null, null, 115436.8, 1], [], ["Total Bulk Cost", null, 115436.8, null, null, null, "SKU", null, "30g"], ["Total Bulk Cost with 95% recovery ", null, 121512.5, null, null, null, "RM cost Per Unit", null, 38.7], [], ["Packaging Material Cost"], ["Sr. No.", "Packaging Material", "Qty ", "Order Qty", "<PERSON><PERSON><PERSON>", "Basic Cost", "Freight", "Total price", "Per Unit Cost 30g"], [1, "Laminated Tube", 1, 3334, 3334, 3.5, 0.17500000000000002, 3.675, 12252.449999999999], [2, "Mono Carton", 1, 3334, 3334, 3, 0.15000000000000002, 3.15, 10502.1], [3, "Plain Shrink Sleeves", 1, 3334, 3334, 1.2, 0.06, 1.26, 4200.84], [4, "Outer Carton (4x3x1)", 0.08333333333333333, 3334, 278, 16, 0.8, 16.8, 4670.400000000001], [5, "5-<PERSON>ly Shipper Box (3x3x2)", 0.004629629629629629, 3334, 16, 90, 4.5, 94.5, 1512], [6, "Shipper Label", 0.004629629629629629, 3334, 16, 2, 0.1, 2.1, 33.6], [7, "Plain Brown BOPP Tape", 0.05, 3334, 1, 50, 2.5, 52.5, 52.5], [], [null, null, null, null, "PM Cost/ Unit", null, null, null, 10.6], [null, null, 22.28, null, "PM Cost/ Unit with Wastage", null, null, 0.03, 11], [null, null, null, null, "RM Per Unit Cost", null, null, null, 38.7], [null, null, null, null, "CC/PC Charges", null, null, 0.25, 12.5], [null, null, null, null, "Product Cost (Ex-Works)", null, null, null, 62.2], [null, null, null, null, "Our Selling Price (INR/ Unit)", null, null, null, 77.8], [null, null, null, null, "% Margin", null, null, null, 0.2508038585209002], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []]}, "ingredients": [], "costStructure": {"totalCost": 121512.5}, "patterns": []}, {"fileName": "Costing - Skin Lightening Lotion (Skiom).xlsx", "sheets": {"BoM": [["Moisturizing  <PERSON><PERSON> (Low cost)  Batch No: Skkiom -01"], ["DM water", null, null, 70], ["Glycerine ", null, null, 3, 190, 0.57], ["Stearic acid ", null, null, 3, 300, 0.9], ["Light liquid paraffin IP", null, null, 15, 80, 1.2], ["Propylene glycol  IP", null, null, 2, 300, 0.6], ["Cetyl Alcohol", null, null, 1.5, 230, 0.345], ["Innocare ST 100/acrylates copolymer", null, null, 1.2, 350, 0.42], ["Methyl <PERSON>", null, null, 0.2, 0, 0], [" Propyl <PERSON>ben", null, null, 0.02, 0, 0], ["Tween 20/Polysorbate-20", null, null, 1, 250, 0.25], ["PEG-40 Hydrogenated Castor Oil", null, null, 0.2, 200, 0.04], ["Sodium Hydroxide", null, null, 0.15, 60, 0.009], ["<PERSON>", null, null, 1, 550, 0.55], ["Bees Wax", null, null, 0.6, 350, 0.21], ["Arlacel 165", null, null, 1, 600, 0.6], ["Perfume (Olimpiana)", null, null, 0.5, 2500, 1.25], ["DS-CERAmix-V", null, null, 0.01, 26000, 0.26], ["Phenoxy ethanol", null, null, 0.5, 350, 0.175]], "Sheet2": [["Moisturizing Body lotion -Skioom -02"], ["Phase-1"], ["Cetyle Palmitate", 0.5], ["Arlacel 165", 1], ["Stearic acid ", 2], ["Cetosteryl alcohol ", 2], ["LLp", 15], ["C12/15 alkyl benzoate", 5], ["Phase-2"], ["Water", "qs"], ["EDTA", 0.1], ["Glycerine ", 2], ["Triethanolamine", 0.2], ["Phase -3"], ["Sepiplus400/ Viscocare 400", 0.2], ["Tween 20", 0.5], ["Phase-4"], ["Phenoxyethanol ", 0.5], ["Ceramide (Mix)", 0.1], ["Perfume (Seasons FF-all time beauty -IFF)", 0.5]], "Costing": [["Product ", null, "Skin Lightening Lotion with SPF", null, null, null, null, 45769], ["Client", null, "Ski<PERSON>"], ["Manufacturer Name"], ["Account "], ["Pack Size", null, 200, "ml"], ["Pack Size with tolerance", null, 200.5, "ml"], ["<PERSON><PERSON> Si<PERSON> ", null, 100, "Kg"], ["Batch Quantity", null, 499, "Nos"], ["Yield 95%", null, 475, "Nos"], [], ["Raw Material Cost"], ["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost", "Freight", "Price in Rs. per Kg", "Value in Rs.", "% contri"], [1, "DM water", 69.12, 69.12, 2.5, null, 2.5, 172.8, 0.****************], [2, "Glycerine ", 3, 3, 130, 20, 150, 450, 0.****************], [3, "Stearic acid ", 3, 3, 300, 20, 320, 960, 0.***************], [4, "Light liquid paraffin IP", 15, 15, 80, 20, 100, 1500, 0.***************], [5, "Propylene glycol IP", 2, 2, 300, 20, 320, 640, 0.****************], [6, "Cetyl Alcohol", 1.5, 1.5, 230, 20, 250, 375, 0.****************], [7, "Innocare ST 100/acrylates copolymer", 1.2, 1.2, 350, 20, 370, 444, 0.***************], [8, "Methyl <PERSON>ben", 0.2, 0.2, 1000, 20, 1020, 204, 0.****************], [9, "Propyl <PERSON>ben", 0.02, 0.02, 1000, 20, 1020, 20.4, 0.0****************], [10, "Tween 20/Polysorbate-20", 1, 1, 250, 20, 270, 270, 0.0326979436626541], [11, "PEG-40 Hydrogenated Castor Oil", 0.2, 0.2, 350, 20, 370, 74, 0.00896165863346816], [12, "Sodium Hydroxide", 0.15, 0.15, 180, 20, 200, 30, 0.00363310485140601], [13, "<PERSON>", 1, 1, 550, 20, 570, 570, 0.0690289921767142], [14, "Bees Wax", 0.6, 0.6, 350, 20, 370, 222, 0.0268849759004045], [15, "Arlacel 165", 1, 1, 600, 20, 620, 620, 0.0750841669290576], [16, "Perfume (Olimpiana)", 0.5, 0.5, 2500, 20, 2520, 1260, 0.152590403759053], [17, "DS-CERAmix-V", 0.01, 0.01, 26000, 20, 26020, 260.2, 0.0315111294111948], [18, "Phenoxy ethanol", 0.5, 0.5, 350, 20, 370, 185, 0.0224041465836704], [null, "Testing Charges", null, null, null, null, null, 0, 0], [null, "Total", 100, 100, null, null, null, 8257.4, 1], [], ["Total Bulk Cost", null, 8257.4, null, null, null, "SKU", null, "200ml"], ["Total Bulk Cost with 95% recovery ", null, 8692, null, null, null, "RM cost Per Unit", null, 18.3], [], ["Packaging Material Cost"], ["Sr. No.", "Ingredients", "Qty ", "Order Qty", "<PERSON><PERSON><PERSON>", "Basic Cost", "Freight", "Total price", "Per Unit Cost 200ml S-Shape"], [1, "200ml HDPE S-Shape <PERSON>", 1, 500, 500, 7.2, 0.72, 7.92, 3960], [2, "Lotion Pump", 1, 500, 500, 6, 0.6, 6.6, 3300], [3, "Front Label 200ml", 1, 500, 500, 2.5, 0.25, 2.75, 1375], [4, "Back Label 200ml", 1, 500, 500, 2.5, 0.25, 2.75, 1375], [5, "Plain Shrink Sleeves", 1, 500, 500, 3, 0.3, 3.3, 1650], [6, "5-<PERSON>ly Shipper Box 200ml", 0.0625, 500, 32, 60, 6, 66, 2112], [7, "Plain Brown BOPP Tape 200ml", 0.05, 500, 2, 50, 5, 55, 110], [], [null, null, null, null, "PM Cost/ Unit", null, null, null, 29.3], [null, null, 22.28, null, "PM Cost/ Unit with Wastage", null, null, 0.03, 30.2], [null, null, null, null, "RM Per Unit Cost", null, null, null, 18.3], [null, null, null, null, "CC/PC Charges", null, null, 0.25, 12.2], [null, null, null, null, "Product Cost (Ex-Works)", null, null, null, 60.7], [null, null, null, null, "Our Selling Price (INR/ Unit)", null, null, null, 75.875], [null, null, null, null, "% Margin", null, null, null, 25], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []]}, "ingredients": [], "costStructure": {"totalCost": 8692}, "patterns": ["cost_analysis"]}, {"fileName": "Costing - Tranexamic Acid Serum (Syscutis).xlsx", "sheets": {"Sheet1": [["Antioxidant serum for face", null, "Per kg RM Cost"], ["Vitamin C (3-O-Ethyl-L-Ascorbic acid)", 10, 12000, 120], ["Vitamin E (Tocopherol)", 0.1, 2500, 0.25], ["Ferulic Acid", 0.5, 13000, 6.5], ["CoQ10 (Ubiquinone)", 0.1, 15000, 1.5], ["Niacinamide", 4, 600, 2.4], ["4 butylresorcinol", 1, 15000, 15], ["Green Tea Extract", 0.5, 600, 0.3], ["<PERSON><PERSON>xant<PERSON>", 0.1, 25000, 2.5], ["hyaluronic acid", 0.3, 35000, 10.5], ["glutathione reductase", 0.5, 12500, 6.25], ["silymarin ", 0.25, 3000, 0.75], ["pentavitin", 1, 5500, 5.5], ["Glycerine", 2, 300, 0.6], ["Propylene Glycol", 3, 400, 1.2], ["<PERSON>.", 3, 550, 1.65], ["<PERSON><PERSON>.", 3, 550, 1.65], ["Phenoxyethanol + Ethylhexylglycerin", 0.9, 800, 0.72], [], ["1800/kg"]], "Costing": [["Product ", null, "10% Tranexamic Acid Serum", null, null, null, null, 45775], ["Client", null, "Sys<PERSON>is"], ["Manufacturer Name"], ["Account "], ["Pack Size", null, 30, "ml"], ["Pack Size with tolerance", null, 30.25, "ml"], ["<PERSON><PERSON> Si<PERSON> ", null, 100, "Kg"], ["Batch Quantity", null, 3306, "Nos"], ["Yield 95%", null, 3141, "Nos"], [], ["Raw Material Cost"], ["Sr. No.", "Ingredients", "%", "Qty / Batch in Kg", "Basic Cost", "Freight", "Price in Rs. per Kg", "Value in Rs.", "% contri"], [1, "Purified Water", 69.75, 69.75, 2.5, null, 2.5, 174.375, 0.0007983676900424148], [2, "Tranexamic Acid", 10, 10, 10500, 20, 10520, 105200, 0.****************], [3, "Kojic Acid ", 2, 2, 3000, 20, 3020, 6040, 0.027653854324623285], [4, "L-Gluthione Reduced", 0.25, 0.25, 12000, 20, 12020, 3005, 0.*****************], [5, "Vitamin C", 2, 2, 11000, 20, 11020, 22040, 0.*****************], [6, "B white", 0.5, 0.5, 49000, 20, 49020, 24510, 0.*****************], [7, "4-Butylresorcinol", 1, 1, 15000, 20, 15020, 15020, 0.*****************], [8, "Alpha Arbutin", 1, 1, 8000, 20, 8020, 8020, 0.*****************], [9, "Tyrostat", 0.5, 0.5, 22000, 20, 22020, 11010, 0.050408764257301716], [10, "Hyaluronic Acid", 0.5, 0.5, 35000, 20, 35020, 17510, 0.08016870682519102, null, null, 900], [11, "EDTA", 0.1, 0.1, 400, 20, 420, 42, 0.0001922950135155924, null, null, 150], [12, "<PERSON><PERSON><PERSON><PERSON>", 0, 0, 5500, 20, 5520, 0, 0], [13, "Glycerine", 2, 2, 200, 20, 220, 440, 0.002014519189210968], [14, "Propylene Glycol", 3, 3, 300, 20, 320, 960, 0.004395314594642112], [15, "<PERSON>", 3, 3, 550, 20, 570, 1710, 0.007829154121706261], [16, "<PERSON><PERSON>", 3, 3, 550, 20, 570, 1710, 0.007829154121706261], [17, "Phenoxyethanol + Ethylhexylglycerin", 0.9, 0.9, 800, 20, 820, 738, 0.0033788980946311232], [18, "<PERSON><PERSON><PERSON>", 0.5, 0.5, 550, 20, 570, 285, 0.0013048590202843768], [null, "Testing Charges", null, null, null, null, null, 0, 0], [null, "Total", 100, 100, null, null, null, 218414.4, 1], [], ["Total Bulk Cost", null, 218414.4, null, null, null, "SKU", null, "30ml"], ["Total Bulk Cost with 95% recovery ", null, 229909.9, null, null, null, "RM cost Per Unit", null, 73.19999999999999], [], ["Packaging Material Cost"], ["Sr. No.", "Ingredients", "Qty ", "Order Qty", "<PERSON><PERSON><PERSON>", "Basic Cost", "Freight", "Total price", "Per Unit Cost 30ml"], [1, "Amber Frosted Glass bottle 30ml", 1, 3334, 3334, 6.5, 0.65, 7.15, 23838.1], [2, "18mm dropper set (Black sleeve, Black teat & clear tube)", 1, 3334, 3334, 7.5, 0.75, 8.25, 27505.5], [3, "Label Glass bottle 30ml", 1, 3334, 3334, 3, 0.3, 3.3, 11002.2], [4, "Mono Carton (with top and bottom holder) 30ml", 1, 3334, 3334, 6, 0.600*************, 6.6, 22004.399999999998], [5, "Plain Shrink Sleeves", 1, 3334, 3334, 1.5, 0.15, 1.65, 5501.1], [6, "Outer Carton (16x30ml)", 0.0625, 3334, 209, 15, 1.5, 16.5, 3448.5], [7, "5-<PERSON>ly Shipper Box (128x30ml)", 0.0078125, 3334, 27, 60, 6, 66, 1782], [8, "Plain Brown BOPP Tape", 0.05, 3334, 2, 50, 5, 55, 110], [], [null, null, null, null, "PM Cost/ Unit", null, null, null, 30.400000000000002], [null, null, 22.28, null, "PM Cost/ Unit with Wastage", null, null, 0.03, 31.400000000000002], [null, null, null, null, "RM Per Unit Cost", null, null, null, 73.19999999999999], [null, null, null, null, "CC/PC Charges", null, null, 0.25, 26.200000000000003], [null, null, null, null, "Product Cost (Ex-Works)", null, null, null, 130.79999999999998], [null, null, null, null, "Our Selling Price (INR/ Unit)", null, null, null, 163.49999999999997], [null, null, null, null, "% Margin", null, null, null, 24.999999999999993], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []]}, "ingredients": [], "costStructure": {"totalCost": 229909.9}, "patterns": ["cost_analysis"]}]}