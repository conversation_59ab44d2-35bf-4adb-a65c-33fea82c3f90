# Agrizy Wellness Formulation Platform - Backend Implementation Plan

## Overview
Transform the current static React frontend into a full-stack authenticated platform with dynamic formulation capabilities, proper data persistence, and enterprise-grade architecture.

## Current State Analysis

### ✅ Completed Frontend Features
- React 19 + Vite 7 + Tailwind CSS 3 setup
- Modular component architecture (Logo, LandingPage, ProgressBar, InteractiveSmartMeter)
- Context-based state management (AppContext)
- Responsive design with green wellness theme
- SEO metadata and social optimization
- Asset integration (logo, hero image)

### ❌ Missing Backend Infrastructure
- No authentication system
- No data persistence (all state lost on refresh)
- Mock formulation generation
- No user management
- No project management
- No API integration

## Requirements

### Authentication System
- **Login Method**: Email + Password
- **Domain Restriction**: Only allow `@agrizy.in`, `@agrizywellness.com`, `@naturalzy.com`
- **Session Management**: JWT tokens with expiration
- **Security**: Password hashing, rate limiting, CORS protection

### Backend Technology Stack
- **Server**: Express.js with proper middleware
- **Database**: MySQL (localhost, root/asdram)
- **ORM**: Sequelize with migrations
- **Authentication**: JWT + bcrypt
- **Logging**: Winston with structured logging
- **Validation**: Joi or express-validator

### Database Schema
```sql
-- Database: agrizy_formulation_db
CREATE DATABASE agrizy_formulation_db;

-- Tables needed:
- users (id, email, password_hash, created_at, updated_at)
- projects (id, user_id, name, industry, product_type, status, created_at, updated_at)
- formulations (id, project_id, formulation_data, scores, created_at)
- sessions (id, user_id, token, expires_at)
- ingredients (id, name, category, cost_per_unit, compliance_info)
```

## Implementation Plan

### Phase 1: Backend Foundation (Days 1-2)

#### 1.1 Express Server Setup
- [ ] Create `backend/` directory structure
- [ ] Initialize npm project with dependencies
- [ ] Setup Express server with basic middleware
- [ ] Configure CORS for frontend communication
- [ ] Add helmet for security headers
- [ ] Setup environment configuration

#### 1.2 Database Configuration
- [ ] Install and configure Sequelize
- [ ] Create MySQL connection configuration
- [ ] Setup database migrations system
- [ ] Create initial database schema
- [ ] Add seed data for testing

#### 1.3 Authentication System
- [ ] Implement password hashing with bcrypt
- [ ] Create JWT token generation/validation
- [ ] Build email domain validation middleware
- [ ] Setup session management
- [ ] Add authentication middleware for protected routes

#### 1.4 Logging & Monitoring
- [ ] Configure Winston logger
- [ ] Add request/response logging
- [ ] Setup error handling middleware
- [ ] Add performance monitoring

### Phase 2: API Development (Days 3-4)

#### 2.1 Authentication Endpoints
```javascript
POST /api/auth/login
POST /api/auth/logout
GET /api/auth/me
POST /api/auth/refresh
```

#### 2.2 Project Management Endpoints
```javascript
GET /api/projects          // List user projects
POST /api/projects         // Create new project
GET /api/projects/:id      // Get project details
PUT /api/projects/:id      // Update project
DELETE /api/projects/:id   // Delete project
```

#### 2.3 Formulation Endpoints
```javascript
POST /api/formulations/generate     // Generate new formulation
GET /api/formulations/:projectId    // Get project formulations
PUT /api/formulations/:id          // Update formulation
POST /api/formulations/:id/export  // Export formulation (PDF/CSV)
```

#### 2.4 Data Endpoints
```javascript
GET /api/ingredients       // Get ingredient database
GET /api/compliance/:type  // Get compliance requirements
GET /api/costs/calculate   // Calculate formulation costs
```

### Phase 3: Frontend Integration (Days 5-6)

#### 3.1 Authentication Components
- [ ] Create `Login.jsx` component with email/password form
- [ ] Add domain validation on frontend
- [ ] Create `AuthContext` for user state management
- [ ] Implement protected route wrapper
- [ ] Add logout functionality
- [ ] Handle authentication errors and loading states

#### 3.2 API Integration Layer
- [ ] Create `api/` directory with service functions
- [ ] Replace mock formulation with real API calls
- [ ] Add error handling and retry logic
- [ ] Implement loading states throughout app
- [ ] Add optimistic updates where appropriate

#### 3.3 Dynamic Features
- [ ] Project management UI (save/load/delete projects)
- [ ] Real-time formulation generation
- [ ] Dynamic ingredient selection
- [ ] Cost calculation updates
- [ ] Export functionality
- [ ] User preferences and settings

#### 3.4 Enhanced User Experience
- [ ] Project dashboard page
- [ ] Formulation history and versions
- [ ] Search and filter capabilities
- [ ] Batch operations
- [ ] Advanced export options

### Phase 4: Testing & Deployment (Day 7)

#### 4.1 Testing
- [ ] Unit tests for backend API endpoints
- [ ] Integration tests for database operations
- [ ] Frontend component testing
- [ ] End-to-end authentication flow testing
- [ ] Load testing for formulation generation

#### 4.2 Security & Performance
- [ ] Security audit (SQL injection, XSS, CSRF)
- [ ] Rate limiting implementation
- [ ] Database query optimization
- [ ] Frontend bundle optimization
- [ ] Caching strategy implementation

#### 4.3 Documentation
- [ ] API documentation (Swagger/OpenAPI)
- [ ] Database schema documentation
- [ ] Deployment instructions
- [ ] User guide updates

## File Structure

### Backend Structure
```
backend/
├── src/
│   ├── controllers/
│   │   ├── authController.js
│   │   ├── projectController.js
│   │   └── formulationController.js
│   ├── models/
│   │   ├── User.js
│   │   ├── Project.js
│   │   ├── Formulation.js
│   │   └── Session.js
│   ├── middleware/
│   │   ├── auth.js
│   │   ├── validation.js
│   │   └── errorHandler.js
│   ├── routes/
│   │   ├── auth.js
│   │   ├── projects.js
│   │   └── formulations.js
│   ├── services/
│   │   ├── formulationService.js
│   │   ├── costCalculationService.js
│   │   └── complianceService.js
│   ├── config/
│   │   ├── database.js
│   │   └── environment.js
│   └── utils/
│       ├── logger.js
│       └── helpers.js
├── migrations/
├── seeders/
├── tests/
├── package.json
└── server.js
```

### Frontend Updates
```
src/
├── api/
│   ├── auth.js
│   ├── projects.js
│   └── formulations.js
├── components/
│   ├── Login.jsx
│   ├── ProtectedRoute.jsx
│   └── ProjectDashboard.jsx
├── context/
│   ├── AuthContext.jsx
│   └── AppContext.jsx (updated)
├── hooks/
│   ├── useAuth.js
│   └── useFormulation.js
└── pages/
    ├── LoginPage.jsx
    └── Dashboard.jsx
```

## Success Criteria

### Technical Milestones
1. **Authentication**: Users can login with domain-restricted emails
2. **Data Persistence**: All formulation data saved to database
3. **Dynamic Generation**: Real formulation algorithms replace mock data
4. **Project Management**: Users can save, load, and manage multiple projects
5. **Performance**: <2s response time for formulation generation
6. **Security**: All endpoints properly authenticated and validated

### Business Milestones
1. **User Experience**: Seamless transition from static to dynamic
2. **Data Integrity**: No data loss during user sessions
3. **Scalability**: Architecture supports multiple concurrent users
4. **Maintainability**: Clean, documented, and testable codebase

## Risk Mitigation

### Technical Risks
- **Database Connection Issues**: Test connection early and have fallback
- **Authentication Complexity**: Start with simple JWT, iterate
- **Performance**: Monitor formulation generation times, optimize algorithms
- **Data Migration**: Plan for existing mock data structure changes

### Timeline Risks
- **Scope Creep**: Focus on core authentication and persistence first
- **Integration Complexity**: Plan API contracts early
- **Testing Time**: Allocate sufficient time for end-to-end testing

## Next Steps

1. **Immediate**: Start with backend server setup and database configuration
2. **Day 1**: Complete authentication system and basic API structure
3. **Day 2**: Implement core formulation endpoints
4. **Day 3**: Begin frontend authentication integration
5. **Day 4**: Replace mock data with API calls
6. **Day 5**: Add project management features
7. **Day 6**: Testing and optimization
8. **Day 7**: Documentation and deployment preparation

---

*This document will be updated as implementation progresses. Each completed section should be marked and any changes to the plan should be documented.*