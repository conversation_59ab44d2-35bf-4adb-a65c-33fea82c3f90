const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// MongoDB connection string from the .env file
const MONGODB_URI = 'mongodb://localhost:27017/agrizy_formulation_dev';

async function checkUsers() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get the users collection
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');

    // Find all users
    const users = await usersCollection.find({}).toArray();

    console.log(`\nFound ${users.length} users in the database:\n`);
    console.log('='.repeat(80));

    users.forEach((user, index) => {
      console.log(`\nUser ${index + 1}:`);
      console.log('-'.repeat(40));
      console.log(`ID: ${user._id}`);
      console.log(`Email: ${user.email}`);
      console.log(`Name: ${user.first_name} ${user.last_name}`);
      console.log(`Company: ${user.company || 'N/A'}`);
      console.log(`Role: ${user.role || 'N/A'}`);
      console.log(`Active: ${user.is_active}`);
      console.log(`Created: ${user.created_at}`);
      console.log(`Password Hash: ${user.password_hash ? '[ENCRYPTED]' : '[NOT SET]'}`);
      
      // Note: We cannot retrieve the actual password as it's hashed
      // But we can show if common default passwords would work
      if (user.password_hash) {
        console.log('\nChecking common passwords:');
        const commonPasswords = ['password123', 'test123', 'admin123', 'Password123!', 'Test@123'];
        for (const pwd of commonPasswords) {
          const matches = bcrypt.compareSync(pwd, user.password_hash);
          if (matches) {
            console.log(`  ✓ Password might be: ${pwd}`);
          }
        }
      }
    });

    console.log('\n' + '='.repeat(80));
    console.log('\nNOTE: Passwords are encrypted with bcrypt and cannot be decrypted.');
    console.log('Only password hashes are stored in the database for security.');

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

checkUsers();