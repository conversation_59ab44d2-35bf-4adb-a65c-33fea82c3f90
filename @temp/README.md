# @temp - Development Assets Archive

This folder contains temporary development assets, test scripts, debug utilities, and migration tools that are not part of the core application but may be useful for development, testing, or historical reference.

## Folder Structure

### 📁 scripts/
Development and utility scripts for project setup and data management.

**Files:**
- `init-db.js` - Database initialization script
- `migrate-to-mongodb.js` - MongoDB migration utility (12.5KB)
- `seed-projects.js` - Project seeding script for development (7.1KB)

**Usage:**
```bash
# Run from project root
node @temp/scripts/init-db.js
node @temp/scripts/migrate-to-mongodb.js
node @temp/scripts/seed-projects.js
```

### 📁 tests/
Test scripts and validation utilities for API endpoints and system components.

**Files:**
- `test-claude-integration.js` - Claude AI service integration tests
- `test-playground-api.js` - Formulation playground API tests
- `test-quality-simple.js` - Quality assessment system tests
- `test-schema-curl.sh` - Schema validation via cURL commands
- `test-schema-fix.js` - Schema validation and fixing utilities
- `test-schema-validation.js` - Comprehensive schema validation tests (15.5KB)

**Usage:**
```bash
# Run individual tests from project root
node @temp/tests/test-claude-integration.js
node @temp/tests/test-playground-api.js
node @temp/tests/test-quality-simple.js
bash @temp/tests/test-schema-curl.sh
```

### 📁 docs/
Historical development documentation and implementation plans.

**Files:**
- `backend-implementation-plan.md` - Historical backend implementation plan (8.9KB)
- `mongodb-migration-plan.md` - MongoDB migration strategy and implementation plan (9.1KB)

**Note:** These documents represent historical implementation planning and are kept for reference. Current documentation is in the main `docs/` directory.

### 📁 debug/
*Currently empty* - Reserved for debug utilities and diagnostic scripts.

### 📁 migrations/
*Currently empty* - Reserved for database migration scripts and schema updates.

## Important Notes

⚠️ **Warning**: These are development and testing assets. Do not use in production environments.

🔒 **Security**: Some scripts may contain sensitive operations. Review before execution.

📋 **Dependencies**: Scripts may require specific environment setup or API keys.

## Maintenance

This folder is excluded from production builds and deployments. Files here are:
- Historical development assets
- Temporary testing utilities
- Migration and setup scripts
- Debug and diagnostic tools

Regular cleanup of outdated files is recommended to maintain repository hygiene.

## Archive Policy

Files in this directory are:
- ✅ Safe to modify for testing purposes
- ✅ Can be removed if no longer needed
- ✅ Not part of the main application workflow
- ❌ Should not be referenced by production code
- ❌ Should not contain production credentials

---

**Last Updated**: August 2025
**Organized From**: Original scattered test and debug scripts across the project