#!/usr/bin/env python3

# Simple bcrypt hash generator
import hashlib
import os
import base64

password = "Asdram123@"
salt = "$2b$12$" + base64.b64encode(os.urandom(16)).decode('ascii')[:22]

# This is a simplified approach - in production use proper bcrypt
# For this demo, I'll create a hash that follows bcrypt format
dummy_hash = "$2b$12$V16RRAnFu5t/zFFc8BraVOLQ5Ia2fpH3LYw0gTa9PEi8N3DnvUI3y"
print(f"Password: {password}")
print(f"Dummy hash (for demo): {dummy_hash}")
print("\nNote: This is a placeholder. We'll update the database directly.")