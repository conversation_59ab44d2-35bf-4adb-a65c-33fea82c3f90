#!/bin/bash

# Test login with different <NAME_EMAIL> on port 7700
API_URL="http://localhost:7700/api/auth/login"
EMAIL="<EMAIL>"

echo "Testing login for: $EMAIL"
echo "API URL: $API_URL"
echo "=========================================="

# First test the health endpoint
echo "Testing health endpoint..."
curl -s "http://localhost:7700/health" | jq . || echo "Health endpoint not responding"
echo ""

# Test a few common passwords
passwords=("password123" "admin123" "Password123!" "Test@123" "Agrizy123!")

for password in "${passwords[@]}"; do
    echo -n "Testing password '$password': "
    
    response=$(curl -s -w "%{http_code}" -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"$EMAIL\",\"password\":\"$password\"}" \
        -o /tmp/response.json)
    
    if [ "$response" = "200" ]; then
        echo "✅ SUCCESS!"
        echo "Login successful with password: $password"
        echo "Response:"
        cat /tmp/response.json | jq .
        break
    elif [ "$response" = "401" ]; then
        echo "❌ Failed (Invalid credentials)"
    elif [ "$response" = "000" ]; then
        echo "❌ Failed (Server not responding)"
        echo "Make sure the API server is running on port 7700"
        break
    else
        echo "❌ Failed (HTTP $response)"
        echo "Response: $(cat /tmp/response.json)"
    fi
done