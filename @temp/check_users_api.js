// Load environment from formulation-api
require('dotenv').config({ path: '../formulation-api/.env' });

// Use the existing models from the API
const { connect, disconnect } = require('../formulation-api/src/models');
const bcrypt = require('../formulation-api/node_modules/bcryptjs');
const mongoose = require('../formulation-api/node_modules/mongoose');

async function checkUsers() {
  try {
    // Connect using the existing connection function
    await connect(process.env.NODE_ENV || 'development');
    console.log('Connected to MongoDB');

    // Get the users collection directly
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');

    // Find all users
    const users = await usersCollection.find({}).toArray();

    console.log(`\nFound ${users.length} users in the database:\n`);
    console.log('='.repeat(80));

    if (users.length === 0) {
      console.log('No users found in the database.');
      console.log('\nTo create a test user, you can use the API endpoint:');
      console.log('POST http://localhost:7000/api/auth/register');
      console.log('Body: {"email": "<EMAIL>", "password": "Password123!", "first_name": "Test", "last_name": "User"}');
    } else {
      users.forEach((user, index) => {
        console.log(`\nUser ${index + 1}:`);
        console.log('-'.repeat(40));
        console.log(`ID: ${user._id}`);
        console.log(`Email: ${user.email}`);
        console.log(`Name: ${user.first_name} ${user.last_name}`);
        console.log(`Company: ${user.company || 'N/A'}`);
        console.log(`Role: ${user.role || 'N/A'}`);
        console.log(`Active: ${user.is_active}`);
        console.log(`Created: ${user.created_at}`);
        console.log(`Password Hash: ${user.password_hash ? '[ENCRYPTED]' : '[NOT SET]'}`);
        
        // Check common passwords
        if (user.password_hash) {
          console.log('\nTesting common passwords:');
          const commonPasswords = ['password123', 'test123', 'admin123', 'Password123!', 'Test@123'];
          for (const pwd of commonPasswords) {
            try {
              const matches = bcrypt.compareSync(pwd, user.password_hash);
              if (matches) {
                console.log(`  ✓ Password is: ${pwd}`);
              }
            } catch (e) {
              // Ignore bcrypt errors for invalid hashes
            }
          }
        }
      });
    }

    console.log('\n' + '='.repeat(80));
    console.log('\nLogin Information:');
    console.log('API Base URL: http://localhost:7000');
    console.log('Login Endpoint: POST /api/auth/login');
    console.log('Register Endpoint: POST /api/auth/register');

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

checkUsers();