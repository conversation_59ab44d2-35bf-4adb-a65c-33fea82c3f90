// Load environment from formulation-api
require('dotenv').config({ path: '../formulation-api/.env' });

// Use the existing models and bcrypt from the API
const { connect, disconnect } = require('../formulation-api/src/models');
const bcrypt = require('../formulation-api/node_modules/bcryptjs');
const mongoose = require('../formulation-api/node_modules/mongoose');

async function updatePassword() {
  try {
    // Connect using the existing connection function
    await connect(process.env.NODE_ENV || 'development');
    console.log('Connected to MongoDB');

    const email = '<EMAIL>';
    const newPassword = 'asdram';
    const saltRounds = 12;

    // Hash the new password
    console.log('Hashing new password...');
    const passwordHash = await bcrypt.hash(newPassword, saltRounds);
    console.log('Password hashed successfully');

    // Get the users collection directly
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');

    // Update the user's password
    console.log(`Updating password for user: ${email}`);
    const result = await usersCollection.updateOne(
      { email: email },
      { 
        $set: { 
          password_hash: passwordHash,
          updated_at: new Date()
        }
      }
    );

    if (result.matchedCount === 0) {
      console.log('❌ User not found');
    } else if (result.modifiedCount === 1) {
      console.log('✅ Password updated successfully');
      console.log(`User: ${email}`);
      console.log(`New password: ${newPassword}`);
      console.log('\nYou can now login with:');
      console.log(`Email: ${email}`);
      console.log(`Password: ${newPassword}`);
    } else {
      console.log('⚠️ User found but password was not updated');
    }

    // Verify the new password works
    console.log('\nTesting new password...');
    const user = await usersCollection.findOne({ email: email });
    if (user && user.password_hash) {
      const isValid = await bcrypt.compare(newPassword, user.password_hash);
      if (isValid) {
        console.log('✅ Password verification successful');
      } else {
        console.log('❌ Password verification failed');
      }
    }

  } catch (error) {
    console.error('❌ Error updating password:', error.message);
  } finally {
    await disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

updatePassword();