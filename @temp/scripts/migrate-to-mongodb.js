#!/usr/bin/env node

/**
 * Migration script from MySQL to MongoDB
 * 
 * This script migrates existing data from MySQL (Sequelize) to MongoDB (Mongoose)
 * Run with: npm run mongo:migrate
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const logger = require('../src/utils/logger');

// Import MySQL models (old)
const { sequelize } = require('../src/config/database');
const MySQLUser = require('../src/models/User');
const MySQLProject = require('../src/models/Project');
const MySQLFormulation = require('../src/models/Formulation');
const MySQLSession = require('../src/models/Session');

// Import MongoDB models (new)
const { User, Project, Session, connect, disconnect } = require('../src/models/mongo');

class DataMigration {
  constructor() {
    this.stats = {
      users: { migrated: 0, errors: 0 },
      projects: { migrated: 0, errors: 0 },
      formulations: { migrated: 0, errors: 0 },
      sessions: { migrated: 0, errors: 0 }
    };
  }

  async run() {
    try {
      logger.info('🚀 Starting MySQL to MongoDB migration...');
      
      // Connect to both databases
      await this.connectDatabases();
      
      // Run migrations in order (due to dependencies)
      await this.migrateUsers();
      await this.migrateProjects();
      await this.migrateSessions();
      
      // Print summary
      this.printSummary();
      
      logger.info('✅ Migration completed successfully!');
      
    } catch (error) {
      logger.error('❌ Migration failed:', error);
      throw error;
    } finally {
      await this.disconnectDatabases();
    }
  }

  async connectDatabases() {
    logger.info('🔌 Connecting to databases...');
    
    // Connect to MySQL
    await sequelize.authenticate();
    logger.info('✅ MySQL connected');
    
    // Connect to MongoDB
    await connect('development');
    logger.info('✅ MongoDB connected');
  }

  async disconnectDatabases() {
    logger.info('🔌 Disconnecting from databases...');
    
    try {
      await sequelize.close();
      logger.info('✅ MySQL disconnected');
    } catch (error) {
      logger.error('❌ Error disconnecting from MySQL:', error);
    }
    
    try {
      await disconnect();
      logger.info('✅ MongoDB disconnected');
    } catch (error) {
      logger.error('❌ Error disconnecting from MongoDB:', error);
    }
  }

  async migrateUsers() {
    logger.info('👥 Migrating users...');
    
    try {
      const mysqlUsers = await MySQLUser.findAll();
      logger.info(`Found ${mysqlUsers.length} users to migrate`);
      
      for (const mysqlUser of mysqlUsers) {
        try {
          // Check if user already exists in MongoDB
          const existingUser = await User.findOne({ email: mysqlUser.email });
          
          if (existingUser) {
            logger.warn(`User ${mysqlUser.email} already exists in MongoDB, skipping`);
            continue;
          }
          
          // Create new MongoDB user
          const mongoUser = new User({
            email: mysqlUser.email,
            password_hash: mysqlUser.password_hash,
            first_name: mysqlUser.first_name,
            last_name: mysqlUser.last_name,
            company: mysqlUser.company,
            role: mysqlUser.role || 'user',
            is_active: mysqlUser.is_active,
            email_verified: mysqlUser.email_verified || false,
            last_login: mysqlUser.last_login,
            created_at: mysqlUser.created_at,
            updated_at: mysqlUser.updated_at,
            preferences: {
              notification_settings: {
                email_notifications: true,
                formulation_updates: true,
                project_reminders: false
              },
              ui_preferences: {
                theme: 'light',
                dashboard_layout: 'grid'
              }
            }
          });
          
          await mongoUser.save();
          this.stats.users.migrated++;
          
          logger.debug(`✅ Migrated user: ${mysqlUser.email}`);
          
        } catch (error) {
          this.stats.users.errors++;
          logger.error(`❌ Error migrating user ${mysqlUser.email}:`, error.message);
        }
      }
      
      logger.info(`✅ Users migration completed: ${this.stats.users.migrated} migrated, ${this.stats.users.errors} errors`);
      
    } catch (error) {
      logger.error('❌ Error in user migration:', error);
      throw error;
    }
  }

  async migrateProjects() {
    logger.info('📂 Migrating projects and formulations...');
    
    try {
      const mysqlProjects = await MySQLProject.findAll({
        include: [
          {
            model: MySQLUser,
            as: 'user',
            attributes: ['id', 'email']
          }
        ]
      });
      
      logger.info(`Found ${mysqlProjects.length} projects to migrate`);
      
      for (const mysqlProject of mysqlProjects) {
        try {
          // Find corresponding MongoDB user
          const mongoUser = await User.findOne({ email: mysqlProject.user.email });
          
          if (!mongoUser) {
            logger.error(`❌ User ${mysqlProject.user.email} not found in MongoDB for project ${mysqlProject.name}`);
            this.stats.projects.errors++;
            continue;
          }
          
          // Get formulations for this project
          const formulations = await MySQLFormulation.findAll({
            where: { project_id: mysqlProject.id },
            order: [['version', 'DESC']]
          });
          
          // Create MongoDB project
          const mongoProject = new Project({
            user_id: mongoUser._id,
            name: mysqlProject.name,
            description: mysqlProject.description,
            industry: mysqlProject.industry,
            product_type: mysqlProject.product_type,
            custom_product_type: mysqlProject.custom_product_type,
            status: mysqlProject.status,
            goals: mysqlProject.goals || {},
            constraints: mysqlProject.constraints || {},
            branding: mysqlProject.branding || {},
            gtm_strategy: mysqlProject.gtm_strategy || {},
            is_active: mysqlProject.is_active,
            last_accessed: mysqlProject.last_accessed,
            created_at: mysqlProject.created_at,
            updated_at: mysqlProject.updated_at
          });
          
          // Migrate formulations
          if (formulations.length > 0) {
            const latestFormulation = formulations[0];
            
            // Transform formulation data to new schema
            const formulationData = this.transformFormulationData(latestFormulation);
            
            mongoProject.current_formulation = formulationData;
            
            // Add version history
            mongoProject.formulation_versions = formulations.map(f => ({
              version: f.version,
              formulation_data: this.transformFormulationData(f),
              created_at: f.created_at,
              changes_summary: `Migrated from MySQL version ${f.version}`
            }));
            
            this.stats.formulations.migrated += formulations.length;
          }
          
          await mongoProject.save();
          this.stats.projects.migrated++;
          
          logger.debug(`✅ Migrated project: ${mysqlProject.name} (${formulations.length} formulations)`);
          
        } catch (error) {
          this.stats.projects.errors++;
          logger.error(`❌ Error migrating project ${mysqlProject.name}:`, error.message);
        }
      }
      
      logger.info(`✅ Projects migration completed: ${this.stats.projects.migrated} projects, ${this.stats.formulations.migrated} formulations`);
      
    } catch (error) {
      logger.error('❌ Error in project migration:', error);
      throw error;
    }
  }

  async migrateSessions() {
    logger.info('🔐 Migrating sessions...');
    
    try {
      const mysqlSessions = await MySQLSession.findAll({
        where: {
          expires_at: {
            [require('sequelize').Op.gt]: new Date()
          }
        }
      });
      
      logger.info(`Found ${mysqlSessions.length} active sessions to migrate`);
      
      for (const mysqlSession of mysqlSessions) {
        try {
          // Find corresponding MongoDB user
          const mongoUser = await User.findOne({ 
            email: mysqlSession.data?.user?.email 
          });
          
          if (!mongoUser) {
            logger.warn(`User not found for session ${mysqlSession.session_id}, skipping`);
            continue;
          }
          
          // Create MongoDB session
          const mongoSession = new Session({
            session_id: mysqlSession.session_id,
            user_id: mongoUser._id,
            data: mysqlSession.data || {},
            expires_at: mysqlSession.expires_at,
            created_at: mysqlSession.created_at,
            updated_at: mysqlSession.updated_at
          });
          
          await mongoSession.save();
          this.stats.sessions.migrated++;
          
          logger.debug(`✅ Migrated session: ${mysqlSession.session_id}`);
          
        } catch (error) {
          this.stats.sessions.errors++;
          logger.error(`❌ Error migrating session ${mysqlSession.session_id}:`, error.message);
        }
      }
      
      logger.info(`✅ Sessions migration completed: ${this.stats.sessions.migrated} migrated, ${this.stats.sessions.errors} errors`);
      
    } catch (error) {
      logger.error('❌ Error in session migration:', error);
      throw error;
    }
  }

  transformFormulationData(formulation) {
    // Transform MySQL formulation data to MongoDB schema
    return {
      version: formulation.version,
      generated_at: formulation.created_at,
      quality_score: this.calculateQualityScore(formulation),
      quality_level: this.determineQualityLevel(formulation),
      ready_for_results: true, // Assume migrated formulations are complete
      
      recipes: [{
        id: 'recipe_1',
        name: formulation.name,
        description: formulation.description,
        ingredients: this.transformIngredients(formulation.ingredients),
        nutritional_profile: formulation.nutrition_facts || {},
        cost_analysis: {
          total_cogs: formulation.total_cost || 0,
          margin_percentage: 50 // Default margin
        },
        scores: formulation.scores || {
          nutrition: 75,
          sustainability: 70,
          cost_efficiency: 80,
          compliance: 85,
          market_appeal: 75
        }
      }],
      
      variations: [], // Will be empty for migrated data
      validation_errors: []
    };
  }

  transformIngredients(ingredients) {
    if (!Array.isArray(ingredients)) {
      return [];
    }
    
    return ingredients.map(ing => ({
      name: ing.name,
      percentage: ing.percentage || 0,
      function: ing.function || 'Not specified',
      cost_per_kg: ing.cost_per_unit || 100,
      supplier_suggestions: [],
      sourcing_region: 'Unknown',
      nutritional_contribution: 'Standard contribution'
    }));
  }

  calculateQualityScore(formulation) {
    if (formulation.scores) {
      const scores = formulation.scores;
      const average = (
        (scores.nutrition || 0) + 
        (scores.sustainability || 0) + 
        (scores.compliance || 0) + 
        (scores.cost_efficiency || 0)
      ) / 4;
      return Math.round(average * 10); // Convert to 0-100 scale
    }
    return 75; // Default score
  }

  determineQualityLevel(formulation) {
    const score = this.calculateQualityScore(formulation);
    if (score >= 85) return 'excellent';
    if (score >= 70) return 'good';
    if (score >= 50) return 'acceptable';
    return 'needs_optimization';
  }

  printSummary() {
    logger.info('\n📊 Migration Summary:');
    logger.info('==================');
    logger.info(`👥 Users: ${this.stats.users.migrated} migrated, ${this.stats.users.errors} errors`);
    logger.info(`📂 Projects: ${this.stats.projects.migrated} migrated, ${this.stats.projects.errors} errors`);
    logger.info(`🧪 Formulations: ${this.stats.formulations.migrated} migrated`);
    logger.info(`🔐 Sessions: ${this.stats.sessions.migrated} migrated, ${this.stats.sessions.errors} errors`);
    logger.info('==================\n');
  }
}

// Run migration if called directly
if (require.main === module) {
  const migration = new DataMigration();
  
  migration.run()
    .then(() => {
      logger.info('🎉 Migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Migration failed:', error);
      process.exit(1);
    });
}

module.exports = DataMigration;