const { User, Project, Formulation } = require('../models');
const logger = require('../utils/logger');

async function seedProjects() {
  try {
    // Find the test user (assuming email exists from previous setup)
    const testUser = await User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (!testUser) {
      console.log('Test user not found. Please create test user first.');
      return;
    }

    console.log(`Found test user: ${testUser.email} (ID: ${testUser.id})`);

    // Sample projects data
    const sampleProjects = [
      {
        name: 'VitaBoost Energy Drink',
        description: 'High-performance energy drink with natural caffeine and B-vitamins',
        industry: 'beverages',
        product_type: 'energy-drink',
        custom_product_type: null,
        goals: {
          budget: 75000,
          targetNutrition: 90,
          sustainability: 70,
          compliance: 'fda'
        },
        constraints: { compliance: 'fda' },
        status: 'in_progress',
        user_id: testUser.id
      },
      {
        name: 'GlowSerum Anti-Aging Formula',
        description: 'Premium anti-aging face serum with hyaluronic acid and retinol',
        industry: 'cosmetics',
        product_type: 'face-serum',
        custom_product_type: null,
        goals: {
          budget: 120000,
          targetNutrition: 85,
          sustainability: 90,
          compliance: 'organic'
        },
        constraints: { compliance: 'organic' },
        status: 'in_progress',
        user_id: testUser.id
      },
      {
        name: 'ImmunityMax Multivitamin',
        description: 'Complete daily multivitamin with immune system support',
        industry: 'nutraceuticals',
        product_type: 'multivitamin',
        custom_product_type: null,
        goals: {
          budget: 45000,
          targetNutrition: 95,
          sustainability: 65,
          compliance: 'fda'
        },
        constraints: { compliance: 'fda' },
        status: 'completed',
        user_id: testUser.id
      },
      {
        name: 'AcaiPower Wellness Shot',
        description: 'Concentrated antioxidant wellness shot with superfruits',
        industry: 'beverages',
        product_type: 'wellness-shot',
        custom_product_type: null,
        goals: {
          budget: 35000,
          targetNutrition: 88,
          sustainability: 85,
          compliance: 'organic'
        },
        constraints: { compliance: 'organic' },
        status: 'in_progress',
        user_id: testUser.id
      },
      {
        name: 'HerbalClean Natural Cleanser',
        description: 'Gentle herbal face cleanser with organic botanicals',
        industry: 'cosmetics',
        product_type: 'natural-cleanser',
        custom_product_type: null,
        goals: {
          budget: 55000,
          targetNutrition: 75,
          sustainability: 95,
          compliance: 'organic'
        },
        constraints: { compliance: 'organic' },
        status: 'draft',
        user_id: testUser.id
      }
    ];

    // Create projects
    console.log('Creating sample projects...');
    for (const projectData of sampleProjects) {
      const project = await Project.create(projectData);
      console.log(`Created project: ${project.name} (ID: ${project.id})`);
      
      // Create a sample formulation for each project
      const formulationData = {
        name: `${project.name} - Formula v1.0`,
        description: `AI-generated formulation for ${project.name}`,
        ingredients: generateSampleIngredients(project.industry, project.product_type),
        batch_size: 1000,
        optimization_target: 'balanced',
        project_id: project.id,
        status: project.status === 'completed' ? 'approved' : 'draft'
      };

      const formulation = await Formulation.create(formulationData);
      console.log(`  └─ Created formulation: ${formulation.name} (ID: ${formulation.id})`);
    }

    console.log('\n✅ Sample projects and formulations created successfully!');
    console.log(`Total projects created: ${sampleProjects.length}`);

  } catch (error) {
    console.error('Error seeding projects:', error);
    logger.error('Error seeding projects:', {
      error: error.message,
      stack: error.stack
    });
  }
}

function generateSampleIngredients(industry, productType) {
  const ingredientSets = {
    beverages: {
      'energy-drink': [
        { name: 'Caffeine Anhydrous', percentage: 25, cost: 300 },
        { name: 'Taurine', percentage: 20, cost: 170 },
        { name: 'B-Vitamin Complex', percentage: 15, cost: 225 },
        { name: 'Guarana Extract', percentage: 15, cost: 128 },
        { name: 'Natural Flavor', percentage: 12, cost: 96 },
        { name: 'Sucralose', percentage: 8, cost: 176 },
        { name: 'Citric Acid', percentage: 5, cost: 22 }
      ],
      'wellness-shot': [
        { name: 'Acai Berry Extract', percentage: 30, cost: 450 },
        { name: 'Ginger Root Extract', percentage: 20, cost: 240 },
        { name: 'Turmeric Extract', percentage: 18, cost: 324 },
        { name: 'Vitamin C', percentage: 15, cost: 135 },
        { name: 'Honey', percentage: 10, cost: 80 },
        { name: 'Lemon Juice', percentage: 7, cost: 35 }
      ]
    },
    cosmetics: {
      'face-serum': [
        { name: 'Hyaluronic Acid', percentage: 30, cost: 2550 },
        { name: 'Vitamin C (L-Ascorbic)', percentage: 20, cost: 1300 },
        { name: 'Niacinamide', percentage: 15, cost: 427 },
        { name: 'Retinol Palmitate', percentage: 12, cost: 1500 },
        { name: 'Aloe Vera Extract', percentage: 10, cost: 157 },
        { name: 'Jojoba Oil', percentage: 8, cost: 260 },
        { name: 'Preservative System', percentage: 5, cost: 225 }
      ],
      'natural-cleanser': [
        { name: 'Coconut Oil', percentage: 25, cost: 375 },
        { name: 'Chamomile Extract', percentage: 20, cost: 480 },
        { name: 'Aloe Vera Gel', percentage: 18, cost: 216 },
        { name: 'Green Tea Extract', percentage: 15, cost: 315 },
        { name: 'Glycerin', percentage: 12, cost: 96 },
        { name: 'Essential Oil Blend', percentage: 7, cost: 210 },
        { name: 'Natural Emulsifier', percentage: 3, cost: 90 }
      ]
    },
    nutraceuticals: {
      'multivitamin': [
        { name: 'Vitamin D3', percentage: 20, cost: 500 },
        { name: 'Vitamin B12', percentage: 18, cost: 810 },
        { name: 'Magnesium Oxide', percentage: 15, cost: 67 },
        { name: 'Iron Fumarate', percentage: 12, cost: 99 },
        { name: 'Zinc Gluconate', percentage: 10, cost: 125 },
        { name: 'Vitamin C', percentage: 10, cost: 67 },
        { name: 'Folic Acid', percentage: 8, cost: 280 },
        { name: 'Microcrystalline Cellulose', percentage: 7, cost: 15 }
      ]
    }
  };

  return ingredientSets[industry]?.[productType] || ingredientSets.beverages['energy-drink'];
}

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedProjects().then(() => {
    console.log('Seeding completed. Exiting...');
    process.exit(0);
  }).catch((error) => {
    console.error('Seeding failed:', error);
    process.exit(1);
  });
}

module.exports = { seedProjects };