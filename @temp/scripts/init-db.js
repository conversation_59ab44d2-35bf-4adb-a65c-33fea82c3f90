const { initializeDatabase } = require('./src/config/database');
const { User, Project, Formulation, Session } = require('./src/models');
const logger = require('./src/utils/logger');

async function initializeDB() {
  try {
    logger.info('🚀 Starting database initialization...');
    
    // Initialize database connection
    await initializeDatabase();
    
    // Sync models with database (create tables)
    await User.sync({ force: false });
    await Project.sync({ force: false });
    await Formulation.sync({ force: false });
    await Session.sync({ force: false });
    
    logger.info('✅ Database tables created successfully');
    
    // Create a test user
    const testUser = await User.findOrCreate({
      where: { email: '<EMAIL>' },
      defaults: {
        email: '<EMAIL>',
        password_hash: await User.hashPassword('AdminPassword123!'),
        first_name: 'Admin',
        last_name: 'User',
        is_active: true
      }
    });
    
    if (testUser[1]) {
      logger.info('✅ Test admin user created: <EMAIL> / AdminPassword123!');
    } else {
      logger.info('ℹ️ Test admin user already exists');
    }
    
    logger.info('🎉 Database initialization completed successfully!');
    process.exit(0);
    
  } catch (error) {
    logger.error('❌ Database initialization failed:', {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  }
}

initializeDB();