#!/bin/bash

API_BASE="http://localhost:7700"
EMAIL="<EMAIL>"
PASSWORD="Asdram123@"

echo "Testing Playground API..."
echo "========================"

# Step 1: Login to get fresh token
echo "1. Getting fresh login token..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/api/auth/login" \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$EMAIL\",\"password\":\"$PASSWORD\"}")

echo "Login response:"
echo "$LOGIN_RESPONSE" | jq .

# Extract token
TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token // empty')

if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
    echo "❌ Failed to get token. Lo<PERSON> failed."
    exit 1
fi

echo ""
echo "2. Testing playground/generate endpoint with fresh token..."
echo "Token: ${TOKEN:0:20}..."

# Step 2: Test playground endpoint
PLAYGROUND_RESPONSE=$(curl -s -w "%{http_code}" -X POST "$API_BASE/api/playground/generate" \
  -H "Accept: */*" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:7070" \
  -d '{
    "formData": {
      "industry": "cosmetics",
      "productType": "herbal-cream", 
      "productDescription": "A odourless cream that unclogs pores, and helps reduce blemishes and to increase the radiance of the skin. Suitable of all types of Indian Skin",
      "goals": {
        "budgetPerUnit": 200,
        "nutritionScore": 50,
        "sustainability": 80,
        "shelfLifeDays": 810,
        "veganFriendly": false,
        "organicCertified": false,
        "lowCalorie": false
      },
      "industryParams": {
        "absorption": 75
      }
    }
  }' \
  -o /tmp/playground_response.json)

HTTP_CODE="${PLAYGROUND_RESPONSE: -3}"
echo "HTTP Status Code: $HTTP_CODE"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ SUCCESS!"
    echo "Response:"
    cat /tmp/playground_response.json | jq .
elif [ "$HTTP_CODE" = "401" ]; then
    echo "❌ Authentication failed (401)"
    echo "Response:"
    cat /tmp/playground_response.json
elif [ "$HTTP_CODE" = "404" ]; then
    echo "❌ Endpoint not found (404)"
    echo "Response:"
    cat /tmp/playground_response.json
else
    echo "❌ Request failed (HTTP $HTTP_CODE)"
    echo "Response:"
    cat /tmp/playground_response.json
fi

echo ""
echo "3. Testing if playground route exists..."
curl -s -I "$API_BASE/api/playground" -H "Authorization: Bearer $TOKEN"