require('dotenv').config({ path: './formulation-api/.env' });
const mongoose = require('mongoose');
const { Session } = require('./formulation-api/src/models');

async function debugSessionLookup() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/agrizy_formulation_dev');
    console.log('Connected to MongoDB');

    // Test session lookup with the latest session ID
    const sessionId = '9103a032-039b-42b2-97e5-aece1a1ea300';
    
    console.log('Looking for session:', sessionId);
    const session = await Session.findBySessionId(sessionId);
    
    console.log('Session found:', !!session);
    if (session) {
      console.log('Session ID:', session.session_id);
      console.log('User ID (raw):', session.user_id);
      console.log('User ID type:', typeof session.user_id);
      console.log('User populated:', session.user_id && typeof session.user_id === 'object' && session.user_id.email);
      console.log('User email:', session.user_id?.email);
      console.log('User is_active:', session.user_id?.is_active);
      console.log('Expires at:', session.expires_at);
      console.log('Is expired:', session.expires_at <= new Date());
    } else {
      console.log('Session not found or expired');
    }

    await mongoose.disconnect();
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

debugSessionLookup();