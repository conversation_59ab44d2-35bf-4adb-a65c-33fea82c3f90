#!/bin/bash

echo "🧪 Testing schema validation fixes with curl..."
echo

# Login first to get auth token
echo "🔐 Logging in..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123@"
  }')

# Extract token from response
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
  echo "❌ Login failed. Response:"
  echo $LOGIN_RESPONSE
  exit 1
fi

echo "✅ Login successful"

# Test playground generation endpoint
echo "🎮 Testing Product Playground generation..."
FORMDATA='{
  "formData": {
    "industry": "beverages",
    "productType": "antioxidant-juice",
    "productDescription": "Premium antioxidant juice blend",
    "goals": {
      "budgetPerUnit": 2000,
      "nutritionScore": 85,
      "sustainability": 75,
      "shelfLifeDays": 365,
      "compliance": {
        "veganFriendly": true,
        "organicCertified": false,
        "lowCalorie": false
      }
    }
  }
}'

PLAYGROUND_RESPONSE=$(curl -s -X POST http://localhost:3001/api/playground/generate \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "$FORMDATA" \
  --max-time 60)

echo "📊 Playground Response:"
echo $PLAYGROUND_RESPONSE | jq . 2>/dev/null || echo $PLAYGROUND_RESPONSE

# Check if response contains success
if echo $PLAYGROUND_RESPONSE | grep -q '"success":true'; then
  echo
  echo "✅ Test successful! Schema validation fixes appear to be working."
  
  # Check for validation errors
  if echo $PLAYGROUND_RESPONSE | grep -q '"validationErrors":\[\]'; then
    echo "✅ No validation errors found!"
  else
    echo "⚠️  Some validation errors may still exist."
  fi
else
  echo
  echo "❌ Test failed. Check the response above for details."
fi