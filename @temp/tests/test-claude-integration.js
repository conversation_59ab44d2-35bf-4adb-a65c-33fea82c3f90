const claudeService = require('./src/services/claudeService');

// Test data that matches the frontend form structure
const testFormData = {
  industry: 'beverages',
  productType: 'energy-drink',
  productDescription: 'Premium Natural Energy Drink with Adaptogens',
  goals: {
    budgetPerUnit: 2500,
    nutritionScore: 85,
    sustainability: 75,
    shelfLifeDays: 365,
    veganFriendly: true,
    organicCertified: false,
    lowCalorie: true
  },
  industryParams: {
    oracValue: 8500,
    brixLevel: 12,
    phLevel: 3.8,
    caffeineContent: 80
  }
};

console.log('🧪 Testing Claude AI Integration with JSON Schema Validation...\n');
console.log('📋 Test Form Data:', JSON.stringify(testFormData, null, 2));
console.log('\n⏳ Generating formulation with Claude AI...\n');

claudeService.generateFormulation(testFormData)
  .then(result => {
    console.log('✅ SUCCESS: Claude AI formulation generated and validated!');
    console.log('\n📊 Response Structure:');
    console.log('- Main Recipes:', result.recipes?.length || 0);
    console.log('- Variations:', result.variations?.length || 0);
    console.log('- Total Recipes:', result.allRecipes?.length || 0);
    console.log('- Market Analysis:', result.market_analysis ? '✅' : '❌');
    console.log('- Regulatory Pathway:', result.regulatory_pathway ? '✅' : '❌');
    
    // Show first recipe summary
    if (result.recipes && result.recipes[0]) {
      const recipe = result.recipes[0];
      console.log('\n🍹 Main Recipe Summary:');
      console.log(`- Name: ${recipe.name}`);
      console.log(`- Ingredients: ${recipe.ingredients?.length || 0} components`);
      console.log(`- Raw Material Cost: ₹${recipe.cost_analysis?.raw_material_cost || 'N/A'}`);
      console.log(`- Suggested Retail: ₹${recipe.cost_analysis?.suggested_retail || 'N/A'}`);
      console.log(`- Nutrition Score: ${recipe.scores?.nutrition || 'N/A'}/100`);
    }
    
    // Show variations summary
    if (result.variations && result.variations.length > 0) {
      console.log('\n🔄 Recipe Variations:');
      result.variations.forEach((variation, index) => {
        console.log(`${index + 1}. ${variation.name} (${variation.variation_type}) - ₹${variation.cost_analysis?.suggested_retail || 'N/A'}`);
      });
    }
    
    console.log('\n🎉 Schema validation successful! Ready for frontend integration.');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ ERROR: Claude AI integration failed');
    console.error('Error:', error.message);
    console.error('\nThis could be due to:');
    console.error('- Invalid API key');
    console.error('- Network connectivity issues');
    console.error('- Claude API rate limits');
    console.error('- Schema validation failures');
    process.exit(1);
  });