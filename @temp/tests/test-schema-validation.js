#!/usr/bin/env node

const Ajv = require('ajv');
const formulationSchema = require('./src/schemas/formulationSchema.json');

// Initialize JSON schema validator
const ajv = new Ajv({ allErrors: true });
const validateSchema = ajv.compile(formulationSchema);

// Test data that would previously fail with old constraints
const testFormulation = {
  recipes: [
    {
      id: "recipe_1",
      name: "Premium Antioxidant Juice Blend",
      variation: "standard",
      description: "A scientifically-optimized antioxidant juice formulation with superior bioavailability",
      targetMarket: "Health-conscious professionals and wellness enthusiasts",
      ingredients: [
        {
          name: "Purified Water",
          percentage: 85.0, // This would fail with old 80% max constraint
          function: "Base ingredient for hydration and dilution",
          cost_per_kg: 2.50, // Now allowed with min 1
          supplier_suggestions: [
            {
              name: "AquaPure Systems",
              location: "Mumbai, Maharashtra, India",
              quality_grade: "Food Grade",
              certification: "ISO 22000",
              lead_time_days: 5
            }
          ],
          sourcing_region: "Maharashtra, India",
          alternative_names: ["H2O"],
          nutritional_contribution: "Hydration and mineral content"
        },
        {
          name: "Concentrated Pomegranate Extract",
          percentage: 8.0,
          function: "Primary antioxidant source",
          cost_per_kg: 1250.50,
          supplier_suggestions: [
            {
              name: "Fruit Extracts Co",
              location: "Pune, Maharashtra, India", 
              quality_grade: "Food Grade",
              certification: "HACCP",
              lead_time_days: 12
            }
          ],
          sourcing_region: "Maharashtra, India",
          nutritional_contribution: "Polyphenols and vitamin C"
        },
        {
          name: "Natural Stevia Extract",
          percentage: 4.0,
          function: "Natural sweetener",
          cost_per_kg: 850.00,
          supplier_suggestions: [
            {
              name: "SweetLeaf Naturals",
              location: "Bangalore, Karnataka, India",
              quality_grade: "Food Grade", 
              certification: "Organic Certified",
              lead_time_days: 10
            }
          ],
          sourcing_region: "Karnataka, India",
          nutritional_contribution: "Zero-calorie sweetness"
        },
        {
          name: "Citric Acid",
          percentage: 2.0,
          function: "pH adjustment and preservation",
          cost_per_kg: 75.00, // Now allowed with min 1
          supplier_suggestions: [
            {
              name: "ChemPure Industries",
              location: "Chennai, Tamil Nadu, India",
              quality_grade: "Food Grade",
              certification: "ISO 9001",
              lead_time_days: 7
            }
          ],
          sourcing_region: "Tamil Nadu, India",
          nutritional_contribution: "Vitamin C enhancement"
        },
        {
          name: "Natural Flavor Complex",
          percentage: 1.0,
          function: "Taste enhancement",
          cost_per_kg: 2500.00,
          supplier_suggestions: [
            {
              name: "FlavorCraft Ltd",
              location: "Hyderabad, Telangana, India",
              quality_grade: "Food Grade",
              certification: "FSSAI Approved",
              lead_time_days: 15
            }
          ],
          sourcing_region: "Telangana, India",
          nutritional_contribution: "Sensory enhancement"
        }
      ],
      nutritional_profile: {
        macronutrients: {
          protein_g: 1.2,
          carbohydrates_g: 8.5,
          fat_g: 0.1,
          fiber_g: 0.5,
          calories_per_serving: 35
        },
        micronutrients: {
          vitamin_c_mg: 120,
          vitamin_d_iu: 0,
          calcium_mg: 25,
          iron_mg: 1.5,
          magnesium_mg: 10
        },
        bioactive_compounds: {
          antioxidants_orac: 8500,
          polyphenols_mg: 150,
          omega3_mg: 0
        },
        daily_value_percentages: {
          vitamin_c: 133,
          vitamin_d: 0,
          calcium: 2,
          iron: 8
        }
      },
      production_specs: {
        batch_size: "1000L",
        shelf_life: "12 months",
        storage_conditions: "Cool, dry place, refrigerate after opening",
        manufacturing_process: "Cold extraction and sterile filtration"
      },
      compliance_details: {
        regulatory_status: {
          fda: "GRAS approved",
          fssai: "Approved for food use",
          eu: "Novel food approved",
          organic: "Pending certification"
        },
        certifications_needed: ["FSSAI", "ISO 22000"],
        labeling_requirements: "Standard beverage labeling with antioxidant claims"
      },
      cost_analysis: {
        raw_material_cost: 1200.50,
        processing_cost: 150.25,
        packaging_cost: 85.75,
        total_cogs: 1436.50,
        suggested_retail: 5746.00,
        margin_percentage: 75
      },
      scores: {
        nutrition: 88,
        sustainability: 82,
        cost_efficiency: 78,
        compliance: 95,
        market_appeal: 85
      }
    }
  ],
  variations: [
    {
      id: "recipe_2", 
      name: "Premium Enhanced Formula",
      variation_type: "premium",
      target_geography: "urban_premium",
      key_differences: "Enhanced bioactives with superior absorption technology",
      ingredients: [
        {
          name: "Premium Purified Water",
          percentage: 82.0,
          function: "Base ingredient",
          cost_per_kg: 5.00,
          supplier_suggestions: [
            {
              name: "Premium AquaPure",
              location: "Mumbai, Maharashtra, India",
              quality_grade: "Pharma Grade",
              certification: "USP Grade",
              lead_time_days: 7
            }
          ],
          sourcing_region: "Maharashtra, India"
        },
        {
          name: "Organic Pomegranate Extract",
          percentage: 12.0,
          function: "Enhanced antioxidant source",
          cost_per_kg: 2850.00,
          supplier_suggestions: [
            {
              name: "Organic Extracts Ltd",
              location: "Pune, Maharashtra, India",
              quality_grade: "Organic Certified",
              certification: "USDA Organic",
              lead_time_days: 15
            }
          ],
          sourcing_region: "Maharashtra, India"
        },
        {
          name: "Premium Stevia",
          percentage: 4.0,
          function: "Natural sweetener",
          cost_per_kg: 1500.00,
          supplier_suggestions: [
            {
              name: "Premium Naturals",
              location: "Bangalore, Karnataka, India",
              quality_grade: "Organic Certified",
              certification: "EU Organic",
              lead_time_days: 12
            }
          ],
          sourcing_region: "Karnataka, India"
        },
        {
          name: "Organic Citric Acid",
          percentage: 2.0,
          function: "pH adjustment",
          cost_per_kg: 125.00,
          supplier_suggestions: [
            {
              name: "Organic Chemicals Co",
              location: "Chennai, Tamil Nadu, India",
              quality_grade: "Organic Certified",
              certification: "USDA Organic",
              lead_time_days: 10
            }
          ],
          sourcing_region: "Tamil Nadu, India"
        }
      ],
      cost_analysis: {
        raw_material_cost: 1850.00,
        processing_cost: 225.00,
        packaging_cost: 125.00,
        total_cogs: 2200.00,
        suggested_retail: 8800.00,
        margin_percentage: 75
      },
      scores: {
        nutrition: 95,
        sustainability: 90,
        cost_efficiency: 65,
        compliance: 96,
        market_appeal: 92
      }
    },
    {
      id: "recipe_3",
      name: "Value Formula",
      variation_type: "value", 
      target_geography: "mass_market",
      key_differences: "Cost-optimized while maintaining quality",
      ingredients: [
        {
          name: "Standard Water",
          percentage: 88.0,
          function: "Base ingredient",
          cost_per_kg: 1.50,
          supplier_suggestions: [
            {
              name: "Budget Water Co",
              location: "Delhi, India",
              quality_grade: "Food Grade",
              certification: "BIS",
              lead_time_days: 5
            }
          ],
          sourcing_region: "Delhi, India"
        },
        {
          name: "Pomegranate Concentrate",
          percentage: 6.0,
          function: "Antioxidant source",
          cost_per_kg: 850.00,
          supplier_suggestions: [
            {
              name: "Fruit Concentrates Inc",
              location: "Nagpur, Maharashtra, India",
              quality_grade: "Food Grade",
              certification: "FSSAI",
              lead_time_days: 8
            }
          ],
          sourcing_region: "Maharashtra, India"
        },
        {
          name: "Sugar Substitute",
          percentage: 4.0,
          function: "Sweetener",
          cost_per_kg: 450.00,
          supplier_suggestions: [
            {
              name: "Sweet Solutions",
              location: "Gujarat, India",
              quality_grade: "Food Grade",
              certification: "ISO 22000",
              lead_time_days: 6
            }
          ],
          sourcing_region: "Gujarat, India"
        },
        {
          name: "Food Grade Citric Acid",
          percentage: 2.0,
          function: "Preservative",
          cost_per_kg: 45.00,
          supplier_suggestions: [
            {
              name: "Basic Chemicals Ltd",
              location: "Mumbai, Maharashtra, India",
              quality_grade: "Food Grade",
              certification: "FSSAI",
              lead_time_days: 5
            }
          ],
          sourcing_region: "Maharashtra, India"
        }
      ],
      cost_analysis: {
        raw_material_cost: 650.00,
        processing_cost: 85.00,
        packaging_cost: 55.00,
        total_cogs: 790.00,
        suggested_retail: 3160.00,
        margin_percentage: 75
      },
      scores: {
        nutrition: 75,
        sustainability: 70,
        cost_efficiency: 92,
        compliance: 94,
        market_appeal: 78
      }
    },
    {
      id: "recipe_4",
      name: "Regional Formula",
      variation_type: "regional",
      target_geography: "india_traditional", 
      key_differences: "Traditional Indian herbs integration",
      ingredients: [
        {
          name: "Purified Water",
          percentage: 80.0,
          function: "Base ingredient",
          cost_per_kg: 3.00,
          supplier_suggestions: [
            {
              name: "Pure Water Systems",
              location: "Jaipur, Rajasthan, India",
              quality_grade: "Food Grade",
              certification: "BIS",
              lead_time_days: 4
            }
          ],
          sourcing_region: "Rajasthan, India"
        },
        {
          name: "Amla Extract",
          percentage: 10.0,
          function: "Traditional antioxidant",
          cost_per_kg: 1200.00,
          supplier_suggestions: [
            {
              name: "Ayurvedic Extracts Co",
              location: "Kerala, India",
              quality_grade: "Food Grade",
              certification: "AYUSH Approved",
              lead_time_days: 14
            }
          ],
          sourcing_region: "Kerala, India"
        },
        {
          name: "Tulsi Extract",
          percentage: 5.0,
          function: "Adaptogenic herb",
          cost_per_kg: 950.00,
          supplier_suggestions: [
            {
              name: "Herbal India Ltd",
              location: "Uttarakhand, India",
              quality_grade: "Food Grade",
              certification: "Organic India",
              lead_time_days: 12
            }
          ],
          sourcing_region: "Uttarakhand, India"
        },
        {
          name: "Jaggery Extract",
          percentage: 3.0,
          function: "Traditional sweetener",
          cost_per_kg: 85.00,
          supplier_suggestions: [
            {
              name: "Traditional Sweets Co",
              location: "Punjab, India",
              quality_grade: "Food Grade",
              certification: "FSSAI",
              lead_time_days: 8
            }
          ],
          sourcing_region: "Punjab, India"
        },
        {
          name: "Black Salt",
          percentage: 2.0,
          function: "Traditional mineral enhancement",
          cost_per_kg: 25.00,
          supplier_suggestions: [
            {
              name: "Himalayan Minerals",
              location: "Himachal Pradesh, India",
              quality_grade: "Food Grade",
              certification: "BIS",
              lead_time_days: 6
            }
          ],
          sourcing_region: "Himachal Pradesh, India"
        }
      ],
      cost_analysis: {
        raw_material_cost: 980.00,
        processing_cost: 120.00,
        packaging_cost: 70.00,
        total_cogs: 1170.00,
        suggested_retail: 4680.00,
        margin_percentage: 75
      },
      scores: {
        nutrition: 82,
        sustainability: 88,
        cost_efficiency: 85,
        compliance: 92,
        market_appeal: 90
      }
    }
  ],
  market_analysis: {
    target_segments: [
      {
        segment: "Health-conscious professionals",
        size_percentage: 35,
        willingness_to_pay: "₹2000 - ₹3000",
        key_motivators: ["efficacy", "quality", "convenience"]
      }
    ],
    competitive_landscape: [
      {
        competitor: "TropicanaEssentials",
        price_point: "₹250-400",
        key_differentiator: "Superior antioxidant concentration"
      }
    ],
    market_opportunity: {
      tam_size_crores: 2500,
      growth_rate_cagr: 12.5,
      entry_barriers: "Brand building and distribution network"
    }
  },
  regulatory_pathway: {
    approval_timeline: "6-12 months",
    required_studies: ["Stability testing", "Safety assessment"],
    regulatory_costs: "₹3-6 lakhs",
    key_milestones: ["Formulation finalization", "FSSAI approval", "Market launch"]
  }
};

console.log('🧪 Testing schema validation with updated constraints...\n');

// Test the validation
const isValid = validateSchema(testFormulation);

if (isValid) {
  console.log('✅ Schema validation PASSED!');
  console.log('✅ Fixed issues:');
  console.log('  - Ingredients can now have up to 95% (was 80%)');
  console.log('  - Minimum cost reduced to ₹1/kg (was ₹100/kg)');
  console.log('  - Schema expects exactly 3 variations (corrected from 4)');
  console.log('\n🎉 All schema constraint fixes are working correctly!');
} else {
  console.log('❌ Schema validation FAILED:');
  const errors = validateSchema.errors;
  errors.forEach((error, index) => {
    console.log(`  ${index + 1}. ${error.instancePath || 'root'}: ${error.message}`);
    if (error.data !== undefined) {
      console.log(`     Value: ${JSON.stringify(error.data)}`);
    }
  });
  
  console.log('\n🔍 These remaining errors need to be addressed.');
}

console.log('\n📊 Test formulation details:');
console.log(`  - Main recipe ingredients: ${testFormulation.recipes[0].ingredients.length}`);
console.log(`  - Variations: ${testFormulation.variations.length}`);
console.log(`  - Highest percentage: ${Math.max(...testFormulation.recipes[0].ingredients.map(i => i.percentage))}%`);
console.log(`  - Lowest cost: ₹${Math.min(...testFormulation.recipes[0].ingredients.map(i => i.cost_per_kg))}/kg`);