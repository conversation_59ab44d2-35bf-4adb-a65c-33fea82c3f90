#!/usr/bin/env node

const axios = require('axios');

async function testSchemaFix() {
  console.log('🧪 Testing schema validation fixes...\n');

  // Test data with formulation request
  const testFormData = {
    industry: 'beverages',
    productType: 'antioxidant-juice',
    productDescription: 'Premium antioxidant juice blend',
    goals: {
      budgetPerUnit: 2000,
      nutritionScore: 85,
      sustainability: 75,
      shelfLifeDays: 365,
      compliance: {
        veganFriendly: true,
        organicCertified: false,
        lowCalorie: false
      }
    }
  };

  // Login first to get auth token
  console.log('🔐 Logging in...');
  try {
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.token;
    console.log('✅ Login successful');

    // Test playground generation endpoint
    console.log('🎮 Testing Product Playground generation...');
    const playgroundResponse = await axios.post(
      'http://localhost:3001/api/playground/generate',
      { formData: testFormData },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 60000 // 60 second timeout for Claude AI
      }
    );

    console.log('✅ Playground generation successful!');
    console.log('📊 Response summary:');
    console.log(`  - Success: ${playgroundResponse.data.success}`);
    console.log(`  - Project ID: ${playgroundResponse.data.projectId}`);
    console.log(`  - Formulation ID: ${playgroundResponse.data.formulationId}`);
    console.log(`  - Has Errors: ${playgroundResponse.data.data.hasErrors}`);
    console.log(`  - Validation Errors: ${playgroundResponse.data.data.validationErrors.length}`);
    
    if (playgroundResponse.data.data.validationErrors.length > 0) {
      console.log('\n❌ Validation errors found:');
      playgroundResponse.data.data.validationErrors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error.field}: ${error.message}`);
      });
    } else {
      console.log('\n✅ No validation errors! Schema fix successful!');
    }

    // Check component status
    const components = playgroundResponse.data.data.components;
    console.log('\n📋 Component status:');
    console.log(`  - Main Recipe: ${components.mainRecipe ? '✅ Loaded' : '❌ Failed'}`);
    console.log(`  - Variations: ${components.variations.length} loaded`);
    console.log(`  - Nutritional Profile: ${components.nutritionalProfile ? '✅ Loaded' : '❌ Failed'}`);
    console.log(`  - Cost Analysis: ${components.costAnalysis ? '✅ Loaded' : '❌ Failed'}`);

    if (Object.keys(components.errors).length > 0) {
      console.log('\n⚠️  Component errors:');
      Object.entries(components.errors).forEach(([component, error]) => {
        console.log(`  - ${component}: ${error}`);
      });
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.data?.details && error.response.data.details.includes('validation failed')) {
      console.log('\n🔍 This looks like a schema validation error. The fix may need refinement.');
    }
  }
}

// Run the test
testSchemaFix().catch(console.error);