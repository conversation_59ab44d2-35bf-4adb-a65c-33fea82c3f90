# AgriZy Formulation Platform

An enterprise-grade platform for AI-powered formulation development in the health and wellness industry, serving functional beverages, nutraceuticals, and herbal cosmetics markets.

## Repository Structure

This project has been split into two separate repositories:

### Frontend Repository: `formulation-web/`
- **Technology**: React 19.1.0 + Vite 7.0.4
- **Styling**: Tailwind CSS 3.4.17
- **Features**: Multi-step wizard, AI formulation generation, unified playground interface
- **Recent Update**: Component consolidation completed (Dec 2024)
- **Repository**: Separate Git repository for frontend development

### Backend Repository: `formulation-api/`
- **Technology**: Node.js + Express 5.1.0
- **Database**: MongoDB with Mongoose 8.16.4
- **AI Integration**: Anthropic Claude API
- **Repository**: Separate Git repository for backend development

## Quick Start

### Frontend Development
```bash
cd formulation-web/
pnpm install
pnpm run dev
```

### Backend Development
```bash
cd formulation-api/
npm install
npm run dev
```

## Documentation

- **Frontend README**: `formulation-web/README.md`
- **Backend README**: `formulation-api/README.md`
- **Architecture Documentation**: `CLAUDE.md`
- **API Documentation**: `docs/`
- **Migration History**: `docs/COMPONENT_MIGRATION_2024.md`
- **Project Rules**: `.trae/rules/project_rules.md`

## Key Features

- **AI-Powered Formulation**: Claude AI integration for intelligent formulation generation
- **Multi-Industry Support**: Beverages, nutraceuticals, and cosmetics
- **Interactive Playground**: Real-time formulation optimization
- **Enterprise Security**: JWT authentication, rate limiting, input validation
- **Quality Scoring**: Multi-dimensional formulation assessment
- **Version Control**: Complete formulation history and rollback

## Development Setup

1. **Prerequisites**: Node.js 18+, MongoDB 6.0+, Claude API key
2. **Frontend**: React development server on port 5173
3. **Backend**: Express API server on port 3001
4. **Database**: MongoDB for data persistence

See individual repository READMEs for detailed setup instructions.
