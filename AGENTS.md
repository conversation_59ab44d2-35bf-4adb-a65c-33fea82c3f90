# Repository Guidelines

## Project Structure & Module Organization
- `formulation-api/`: Express + MongoDB backend (`src/{routes,models,services,utils}`; entry: `server.js`).
- `formulation-web/`: React (Vite + Tailwind) frontend (`src/{pages,components,context,styles,utils}`).
- `frontend/`: Experimental UI stub; avoid duplicating components with `formulation-web/`.
- `docs/`, `logs/`, `@temp/`: Documentation, runtime logs, and temporary assets.

## Build, Test, and Development Commands
- Backend (from `formulation-api/`):
  - `pnpm dev` (or `npm run dev`): start API with Nodemon on `PORT` (default 7700).
  - `pnpm start` (or `npm start`): production-style launch.
  - `pnpm run mongo:seed` / `mongo:migrate`: seed or migrate MongoDB.
- Frontend (from `formulation-web/`):
  - `pnpm dev`: Vite dev server.
  - `pnpm build`: production build to `dist/`.
  - `pnpm preview`: serve built app locally.

## Coding Style & Naming Conventions
- Indentation: 2 spaces; keep lines focused and readable.
- Backend: CommonJS, file names kebab-case (e.g., `error-handler.js`), classes PascalCase.
- Frontend: React components PascalCase in `src/components`, pages in `src/pages`.
- Linting: ESLint (`formulation-web/eslint.config.js`); run `pnpm lint` before PRs.
- Styling: Tailwind CSS; prefer design tokens/util classes over ad-hoc CSS. Maintain consistent fonts, colors, and spacing across views.

## Testing Guidelines
- Current: minimal automated tests. Add unit tests alongside code:
  - API: Jest in `formulation-api/src/**/__tests__`.
  - Web: Vitest + React Testing Library in `formulation-web/src/**/__tests__`.
- Name tests `*.test.{js,jsx}`. Target critical services, routes, and UI flows.

## Commit & Pull Request Guidelines
- Use Conventional Commits (e.g., `feat: add project routes`, `fix: handle CORS error`).
- PRs must include: clear description, linked issue, setup/run notes, and screenshots for UI.
- Keep changes small, focused, and free of duplication. Update docs when behavior changes.

## Security & Configuration Tips
- Env: copy `.env.example` → `.env` in each app; never commit secrets. Configure `ALLOWED_ORIGINS` for CORS.
- Rate limiting and Helmet are enabled server-side—do not remove; extend thoughtfully.
- Prefer shared utilities to avoid duplication across `formulation-api` and `formulation-web`.
