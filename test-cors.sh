#!/bin/bash

echo "🧪 Testing CORS Configuration for AgriZy Formulation Platform"
echo "============================================================="
echo

echo "1. Testing if backend server is running on port 5000..."
echo "GET /health endpoint:"
curl -s -w "\n➜ HTTP Status: %{http_code}\n➜ Time: %{time_total}s\n" \
  http://localhost:5000/health || echo "❌ Backend server not running on port 5000"
echo

echo "2. Testing CORS preflight (OPTIONS) request..."
echo "OPTIONS /api/auth/login with Origin: http://localhost:5050"
curl -s -w "\n➜ HTTP Status: %{http_code}\n➜ Time: %{time_total}s\n" \
  -X OPTIONS \
  -H "Origin: http://localhost:5050" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: content-type" \
  http://localhost:5000/api/auth/login
echo

echo "3. Testing direct auth endpoint access..."
echo "GET /api/auth (should return 404 but with CORS headers)"
curl -s -w "\n➜ HTTP Status: %{http_code}\n➜ Time: %{time_total}s\n" \
  -H "Origin: http://localhost:5050" \
  http://localhost:5000/api/auth
echo

echo "4. Testing any endpoint to check server response..."
curl -s -v http://localhost:5000/api/auth/login 2>&1 | head -20
echo

echo "✅ Test completed!"
echo
echo "Expected results:"
echo "- Health endpoint should return 200"
echo "- OPTIONS request should return 200 with CORS headers"
echo "- Server should be accessible and responding"
echo
echo "If any test fails:"
echo "1. Make sure backend server is running: cd formulation-api && npm run dev"
echo "2. Check if port 5000 is available: lsof -i :5000"
echo "3. Verify .env file has PORT=5000"