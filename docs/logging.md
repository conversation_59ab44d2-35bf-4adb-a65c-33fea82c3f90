# Logging Configuration

The AgriZy Formulation Platform implements a comprehensive logging system with both frontend and backend logging capabilities, centralized in a shared `logs/` directory.

## Directory Structure

```
project-root/
├── logs/                           # Shared logging directory
│   ├── frontend.log               # Frontend application logs
│   ├── backend.log                # Backend application logs
│   ├── backend-error.log          # Backend error logs
│   ├── backend-exceptions.log     # Uncaught exceptions
│   └── backend-rejections.log     # Unhandled promise rejections
├── formulation-web/
│   ├── .env                       # Frontend environment config
│   └── src/utils/logger.js        # Frontend logging utility
└── formulation-api/
    ├── .env                       # Backend environment config
    ├── src/utils/logger.js        # Backend logging utility
    └── src/routes/logs.js         # Log management API
```

## Frontend Logging

### Configuration

Configure frontend logging in `formulation-web/.env`:

```env
# Logging Configuration
VITE_LOG_LEVEL=info
VITE_LOG_TO_FILE=true
VITE_LOG_FILE_PATH=../logs/frontend.log
VITE_ENABLE_DEBUG_MODE=true
```

### Usage

```javascript
import logger from './utils/logger';

// Basic logging
logger.info('User logged in successfully');
logger.error('API request failed');
logger.debug('Debug information');
logger.warn('Warning message');

// Application-specific logging
logger.logUserAction('formulation_generated', { industry: 'beverages' });
logger.logApiCall('POST', '/api/formulations', 200, 1250);
logger.logFormulationEvent('optimization_completed', { version: 2 });
logger.logAuthEvent('login', true, { email: '<EMAIL>' });
```

### Features

- **Console Output**: Colored console logging for development
- **File Logging**: Sends logs to backend for file storage
- **Log Levels**: error, warn, info, debug
- **Context Enrichment**: Automatic URL, timestamp, and user agent capture
- **Error Handling**: Global error and unhandled rejection handlers
- **Categorized Logging**: User actions, API calls, formulation events, auth events

## Backend Logging

### Configuration

Configure backend logging in `formulation-api/.env`:

```env
# Logging Configuration
LOG_LEVEL=info
LOG_TO_FILE=true
LOG_FILE_PATH=../logs/backend.log
LOG_ERROR_FILE_PATH=../logs/backend-error.log
LOG_MAX_FILE_SIZE=10485760  # 10MB
LOG_MAX_FILES=5
FRONTEND_LOG_PATH=../logs/frontend.log
```

### Usage

```javascript
const logger = require('./utils/logger');

// Basic logging
logger.info('Server started successfully');
logger.error('Database connection failed');
logger.debug('Debug information');
logger.warn('Warning message');

// Application-specific logging
logger.logAuth('login', 'user123', '<EMAIL>', true);
logger.logDatabase('findUser', 'users', true, 150);
logger.logError(error, { userId: 'user123' });
```

### Features

- **Winston-based**: Professional logging with Winston
- **File Rotation**: Automatic log rotation based on size
- **Error Separation**: Separate error log files
- **Exception Handling**: Uncaught exception and rejection logging
- **Structured Logging**: JSON-formatted log entries
- **Context Enrichment**: Service, environment, and metadata

## Log Management API

### Endpoints

#### Frontend Log Management

```http
# Send frontend log entry
POST /api/logs/frontend
Content-Type: application/json

{
  \"level\": \"info\",
  \"message\": \"User action completed\",
  \"category\": \"user-action\",
  \"url\": \"https://app.agrizy.com/dashboard\"
}

# Get frontend logs
GET /api/logs/frontend?limit=100&level=error&category=api-call

# Clear frontend logs
DELETE /api/logs/frontend
```

### Log Levels

| Level | Priority | Usage |
|-------|----------|-------|
| `error` | 0 | Errors, exceptions, failed operations |
| `warn` | 1 | Warnings, deprecated features, potential issues |
| `info` | 2 | General information, user actions, API calls |
| `debug` | 3 | Detailed debugging information |

### Log Categories

#### Frontend Categories
- `user-action`: User interactions and behaviors
- `api-call`: HTTP requests to backend
- `formulation`: Formulation-related events
- `authentication`: Login, logout, auth events
- `application-error`: Application errors and exceptions
- `unhandled-rejection`: Unhandled promise rejections

#### Backend Categories
- `http-request`: HTTP request/response logging
- `database`: Database operations
- `authentication`: Auth events
- `formulation`: AI formulation generation
- `claude-api`: Claude AI service calls
- `application-error`: Application errors

## Log Format

### Frontend Log Entry
```json
{
  \"timestamp\": \"2024-01-15T10:30:00.000Z\",
  \"level\": \"info\",
  \"message\": \"User generated formulation\",
  \"service\": \"agrizy-formulation-web\",
  \"environment\": \"development\",
  \"url\": \"http://localhost:5173/dashboard\",
  \"userAgent\": \"Mozilla/5.0...\",
  \"category\": \"formulation\",
  \"industry\": \"beverages\",
  \"projectId\": \"64f7b1a2c3d4e5f6789012\"
}
```

### Backend Log Entry
```json
{
  \"timestamp\": \"2024-01-15T10:30:00.000Z\",
  \"level\": \"info\",
  \"message\": \"Formulation generated successfully\",
  \"service\": \"agrizy-formulation-api\",
  \"environment\": \"development\",
  \"userId\": \"64f7b1a2c3d4e5f6789012\",
  \"projectId\": \"64f7b1a2c3d4e5f6789013\",
  \"duration\": \"2500ms\",
  \"qualityScore\": 85
}
```

## Development Setup

1. **Create logs directory**:
   ```bash
   mkdir -p logs
   ```

2. **Configure environment files**:
   ```bash
   # Frontend
   cp formulation-web/.env.example formulation-web/.env
   
   # Backend
   cp formulation-api/.env.example formulation-api/.env
   ```

3. **Start applications**:
   ```bash
   # Frontend
   cd formulation-web && npm run dev
   
   # Backend
   cd formulation-api && npm run dev
   ```

## Production Considerations

### Log Rotation
- **Max File Size**: 10MB per log file
- **Max Files**: 5 rotated files
- **Compression**: Enable gzip compression for archived logs

### Security
- **Sensitive Data**: Never log passwords, API keys, or PII
- **Access Control**: Restrict log file access in production
- **Retention**: Implement log retention policies

### Performance
- **Async Logging**: Use async logging to avoid blocking
- **Log Level**: Set appropriate log levels for production
- **File I/O**: Monitor disk usage and I/O performance

### Monitoring
- **Log Aggregation**: Consider ELK stack or similar
- **Alerting**: Set up alerts for error spikes
- **Metrics**: Monitor log volume and patterns

## Troubleshooting

### Common Issues

1. **Permission Errors**:
   ```bash
   chmod 755 logs/
   chmod 644 logs/*.log
   ```

2. **Log Directory Not Found**:
   - Ensure `logs/` directory exists at project root
   - Check environment variable paths

3. **Frontend Logs Not Appearing**:
   - Verify `VITE_LOG_TO_FILE=true`
   - Check network connectivity to backend
   - Ensure logs API endpoint is accessible

4. **Large Log Files**:
   - Reduce log level in production
   - Implement log rotation
   - Monitor disk space

### Log Analysis

```bash
# View recent logs
tail -f logs/backend.log
tail -f logs/frontend.log

# Search for errors
grep \"ERROR\" logs/backend.log
grep \"error\" logs/frontend.log

# Count log entries by level
grep -c \"INFO\" logs/backend.log
grep -c \"ERROR\" logs/backend.log
```

## Best Practices

1. **Meaningful Messages**: Write clear, actionable log messages
2. **Structured Data**: Include relevant context and metadata
3. **Consistent Format**: Use consistent log formats across services
4. **Appropriate Levels**: Use correct log levels for different scenarios
5. **Performance**: Avoid excessive logging in hot paths
6. **Security**: Sanitize sensitive data before logging
7. **Monitoring**: Regularly review and analyze logs
8. **Retention**: Implement appropriate log retention policies