# Technical Architecture
## AgriZy Wellness Formulation Platform

**Document Version:** 2.0  
**Date:** August 2025  
**Status:** Production Ready  
**Architecture Type:** Distributed Full-Stack Application

---

## 📋 Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Repository Structure](#repository-structure)
3. [Technology Stack](#technology-stack)
4. [System Components](#system-components)
5. [Data Architecture](#data-architecture)
6. [AI Integration](#ai-integration)
7. [Security Architecture](#security-architecture)
8. [Performance & Scalability](#performance--scalability)
9. [Development Workflow](#development-workflow)
10. [Deployment Architecture](#deployment-architecture)

---

## 1. Architecture Overview

### 1.1 System Architecture Pattern

The AgriZy Wellness Formulation Platform follows a **modern distributed architecture** with clear separation of concerns across frontend, backend, and AI services.

```mermaid
graph TB
    subgraph "Client Tier"
        FE[formulation-web/]
        FE_TECH[React 19.1.0 + Vite 7.0.4]
        FE_STYLE[Tailwind CSS 3.4.17]
    end
    
    subgraph "Application Tier"
        BE[formulation-api/]
        BE_TECH[Express.js 5.1.0]
        AUTH[JWT Authentication]
        API[RESTful API]
    end
    
    subgraph "Data Tier"
        DB[(MongoDB 8.16.4)]
        LOGS[Shared Logs Directory]
    end
    
    subgraph "AI Services"
        CLAUDE[Claude 3.5 Sonnet]
        VALIDATION[JSON Schema Validation]
    end
    
    FE --> BE
    BE --> DB
    BE --> CLAUDE
    BE --> AUTH
    FE -.-> LOGS
    BE --> LOGS
```

### 1.2 Key Architectural Decisions

#### ✅ **Independent Repository Structure**
- **Benefit:** Independent development, deployment, and scaling
- **Implementation:** Separate Git repositories with shared resources
- **Status:** Fully implemented and operational

#### ✅ **Document-Based Data Storage**
- **Benefit:** Flexible schema for complex formulation data
- **Implementation:** MongoDB with embedded documents
- **Status:** Optimized and production-ready

#### ✅ **AI-First Design**
- **Benefit:** Claude AI seamlessly integrated into core workflows
- **Implementation:** Anthropic SDK with custom prompt engineering
- **Status:** Production-ready with comprehensive error handling

#### ✅ **Centralized Logging**
- **Benefit:** Unified monitoring and debugging across repositories
- **Implementation:** Shared logs directory with structured logging
- **Status:** Complete with Winston and custom loggers

---

## 2. Repository Structure

### 2.1 Project Organization ✅ **IMPLEMENTED**

```
agrizy-formulation-platform/
├── formulation-web/              # Frontend Repository (Independent Git)
│   ├── .git/                     # Independent version control
│   ├── src/
│   │   ├── components/           # Reusable UI components
│   │   ├── pages/               # Route-based page components
│   │   ├── context/             # React Context providers
│   │   ├── hooks/               # Custom React hooks
│   │   ├── utils/               # Utility functions
│   │   └── api/                 # API service functions
│   ├── public/                  # Static assets
│   ├── .env                     # Frontend environment variables
│   ├── package.json             # Frontend dependencies
│   └── README.md                # Frontend documentation
│
├── formulation-api/              # Backend Repository (Independent Git)
│   ├── .git/                    # Independent version control
│   ├── src/
│   │   ├── controllers/         # Request handlers
│   │   ├── models/              # Database models (Mongoose)
│   │   ├── routes/              # API route definitions
│   │   ├── middleware/          # Authentication & validation
│   │   ├── services/            # Business logic services
│   │   ├── utils/               # Utility functions
│   │   └── config/              # Configuration files
│   ├── .env                     # Backend environment variables
│   ├── package.json             # Backend dependencies
│   └── README.md                # Backend documentation
│
├── logs/                         # Shared Logging Directory
│   ├── frontend.log             # Frontend application logs
│   ├── backend.log              # Backend application logs
│   ├── backend-error.log        # Backend error logs
│   └── .gitkeep                 # Track directory in Git
│
├── @temp/                        # Development Assets Archive
│   ├── scripts/                 # Database and utility scripts
│   ├── tests/                   # Test scripts and validation
│   ├── docs/                    # Historical documentation
│   └── README.md                # Development assets guide
│
├── docs/                         # Project Documentation
│   ├── README.md                # Documentation index
│   ├── business-requirements.md  # Business requirements
│   ├── technical-architecture.md # This document
│   ├── product-features.md      # Feature specifications
│   └── logging.md               # Logging configuration
│
├── .gitignore                   # Git ignore patterns
├── README.md                    # Main project overview
└── CLAUDE.md                    # AI assistant guidance
```

### 2.2 Independent Git Management ✅ **IMPLEMENTED**

#### Frontend Repository (formulation-web/)
- **Git Repository:** Independent version control
- **Deployment:** Can be deployed independently to any static hosting
- **Dependencies:** Node.js project with React-specific packages
- **Configuration:** Environment variables for API endpoints and features

#### Backend Repository (formulation-api/)
- **Git Repository:** Independent version control
- **Deployment:** Can be deployed independently to any Node.js hosting
- **Dependencies:** Express.js with MongoDB and AI service integrations
- **Configuration:** Environment variables for database, AI APIs, and security

---

## 3. Technology Stack

### 3.1 Frontend Stack ✅ **IMPLEMENTED**

#### Core Technologies
```json
{
  "framework": "React 19.1.0",
  "buildTool": "Vite 7.0.4",
  "styling": "Tailwind CSS 3.4.17",
  "routing": "React Router DOM 7.7.0",
  "stateManagement": "React Context API",
  "icons": "Lucide React 0.525.0",
  "language": "JavaScript (ES2022)"
}
```

#### Development Tools
- **Development Server:** Vite dev server with HMR
- **Build System:** Vite with automatic optimization
- **Linting:** ESLint with React hooks rules
- **Package Manager:** npm with package-lock.json

#### Key Features
- **Component Architecture:** Functional components with hooks
- **Responsive Design:** Mobile-first with Tailwind breakpoints
- **Performance:** Code splitting and lazy loading ready
- **Accessibility:** WCAG compliance considerations

### 3.2 Backend Stack ✅ **IMPLEMENTED**

#### Core Technologies
```json
{
  "runtime": "Node.js 18+",
  "framework": "Express.js 5.1.0",
  "database": "MongoDB 8.16.4",
  "orm": "Mongoose ODM",
  "authentication": "JWT + BCrypt",
  "validation": "AJV + Joi",
  "logging": "Winston"
}
```

#### Middleware Stack
- **Security:** Helmet.js for security headers
- **CORS:** Configurable cross-origin resource sharing
- **Compression:** Response compression for performance
- **Rate Limiting:** API request throttling
- **Error Handling:** Comprehensive error middleware

#### Database Configuration
- **Connection:** Mongoose with connection pooling
- **Schema Design:** Embedded documents for complex data
- **Validation:** Schema-level and application-level validation
- **Indexing:** Strategic indexes for query optimization

### 3.3 AI Integration Stack ✅ **IMPLEMENTED**

#### Claude AI Integration
```json
{
  "provider": "Anthropic",
  "model": "Claude 3.5 Sonnet",
  "sdk": "Anthropic SDK 0.57.0",
  "maxTokens": 4000,
  "temperature": 0.3
}
```

#### Integration Features
- **Prompt Engineering:** Industry-specific templates
- **Response Validation:** JSON schema validation with AJV
- **Error Handling:** Graceful degradation and retry logic
- **Performance:** Response caching and optimization

---

## 4. System Components

### 4.1 Frontend Components ✅ **IMPLEMENTED**

#### Core Components Architecture
```
src/components/
├── ui/                          # Basic UI components
│   ├── Logo.jsx                 # Brand logo component
│   ├── ProgressBar.jsx          # Step progress indicator
│   └── InteractiveSmartMeter.jsx # Animated metric displays
├── forms/                       # Form components
│   ├── Login.jsx                # Authentication form
│   └── FormulationWizard.jsx    # Multi-step wizard
├── navigation/                  # Navigation components
│   ├── Header.jsx               # Application header
│   └── ProtectedRoute.jsx       # Route protection
└── playground/                  # Playground-specific components
    ├── ChatInterface.jsx        # AI chat interface
    ├── ComponentEditor.jsx      # Formulation editing
    └── QualityMeter.jsx         # Quality assessment
```

#### Page Components
```
src/pages/
├── LandingPage.jsx              # Marketing landing page
├── Dashboard.jsx                # Project dashboard
├── IndustrySelection.jsx        # Industry picker
├── ProductTypeSelection.jsx     # Product type selector
├── GoalSetup.jsx               # Goals and constraints
├── FormulationInterface.jsx     # AI generation interface
├── ProductPlayground.jsx        # Interactive modification
└── ResultsDashboard.jsx         # Final results and export
```

#### State Management
```
src/context/
├── AppContext.jsx               # Global application state
├── AuthContext.jsx              # Authentication state
└── ProjectContext.jsx           # Project-specific state
```

### 4.2 Backend Components ✅ **IMPLEMENTED**

#### API Layer
```
src/routes/
├── auth.js                      # Authentication endpoints
├── projects.js                  # Project management
├── formulations.js              # Formulation CRUD
├── playground.js                # Interactive features
└── logs.js                      # Log management
```

#### Business Logic
```
src/controllers/
├── authController.js            # Authentication logic
├── projectController.js         # Project operations
├── formulationController.js     # Formulation management
└── playgroundController.js      # Interactive features
```

#### Data Models
```
src/models/
├── User.js                      # User account schema
├── Project.js                   # Project schema with embedded formulations
├── Session.js                   # JWT session management
└── schemas/                     # JSON schemas for validation
```

#### Services
```
src/services/
├── claudeService.js             # Claude AI integration
├── qualityService.js            # Quality assessment
├── validationService.js         # Data validation
└── exportService.js             # Export functionality
```

---

## 5. Data Architecture

### 5.1 Database Design ✅ **IMPLEMENTED**

#### MongoDB Schema Strategy

```javascript
// User Collection
{
  _id: ObjectId,
  email: String,
  password_hash: String,
  profile: {
    first_name: String,
    last_name: String,
    company: String,
    role: String
  },
  preferences: {
    ui_settings: Object,
    notification_settings: Object
  },
  created_at: Date,
  updated_at: Date
}

// Project Collection (with Embedded Formulations)
{
  _id: ObjectId,
  user_id: ObjectId,
  name: String,
  description: String,
  industry: String,
  product_type: String,
  status: String,
  
  // Current active formulation (embedded)
  current_formulation: {
    version: Number,
    quality_score: Number,
    ready_for_results: Boolean,
    
    recipes: [RecipeSchema],
    variations: [VariationSchema],
    nutritional_profile: NutritionalSchema,
    cost_analysis: CostSchema,
    manufacturing_specs: ManufacturingSchema
  },
  
  // Conversation history for playground
  conversation_history: [{
    role: String,
    message: String,
    action: String,
    changes: [String],
    timestamp: Date,
    version: Number
  }],
  
  // Version control
  formulation_versions: [{
    version: Number,
    formulation_data: Mixed,
    created_at: Date,
    changes_summary: String
  }],
  
  created_at: Date,
  updated_at: Date
}
```

#### Indexing Strategy ✅ **IMPLEMENTED**

```javascript
// Performance-optimized indexes
db.projects.createIndex({ user_id: 1, created_at: -1 })
db.projects.createIndex({ status: 1 })
db.projects.createIndex({ industry: 1 })
db.projects.createIndex({ "current_formulation.quality_score": -1 })
db.projects.createIndex({ "current_formulation.ready_for_results": 1 })
db.sessions.createIndex({ expires_at: 1 }, { expireAfterSeconds: 0 })
```

### 5.2 Data Flow Architecture ✅ **IMPLEMENTED**

#### Request-Response Flow
```
1. Frontend Request → API Gateway → Authentication Middleware
2. Route Handler → Controller → Service Layer
3. Service → Database/AI Service → Response Processing
4. Response → Frontend → State Update → UI Render
```

#### Real-Time Features
- **WebSocket Ready:** Architecture supports real-time updates
- **Optimistic Updates:** Frontend updates before server confirmation
- **Conflict Resolution:** Version-based conflict management
- **Event Sourcing:** Complete change history preservation

---

## 6. AI Integration

### 6.1 Claude AI Architecture ✅ **IMPLEMENTED**

#### Service Integration
```javascript
class ClaudeService {
  constructor() {
    this.client = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY
    });
    this.model = 'claude-3-5-sonnet-20241022';
  }

  async generateFormulation(formData) {
    // Industry-specific prompt engineering
    // JSON schema validation
    // Error handling and retry logic
  }

  async processChatRequest(context, userMessage) {
    // Context-aware conversation
    // Change tracking and validation
    // Quality assessment integration
  }
}
```

#### Prompt Engineering Strategy
- **Industry Templates:** Specialized prompts for each industry
- **Context Injection:** Historical formulation data integration
- **Constraint Handling:** Budget, regulatory, and preference integration
- **Quality Control:** Multi-layer validation and scoring

#### Response Processing Pipeline
1. **Raw Response:** Claude AI text response
2. **JSON Extraction:** Parse structured formulation data
3. **Schema Validation:** AJV-based validation against business rules
4. **Quality Assessment:** Automated scoring algorithm
5. **Error Handling:** Partial response recovery and user feedback

### 6.2 Quality Assessment System ✅ **IMPLEMENTED**

#### Scoring Algorithm
```javascript
function calculateQualityScore(formulation, validationErrors) {
  let score = 0;
  
  // Component completeness (70 points total)
  if (formulation.recipes?.length > 0) score += 20;
  if (formulation.ingredients?.every(i => i.percentage > 0)) score += 10;
  if (formulation.variations?.length === 3) score += 30;
  if (formulation.cost_analysis?.total_cost > 0) score += 10;
  
  // Quality factors (30 points total)
  if (formulation.nutritional_profile) score += 15;
  if (formulation.manufacturing_specs) score += 15;
  
  // Error penalties
  const errorPenalty = Math.min(validationErrors.length * 5, 20);
  score = Math.max(0, score - errorPenalty);
  
  return score;
}
```

#### Quality Levels
- **Excellent (85-100):** Production-ready formulations
- **Good (70-84):** Minor optimizations recommended
- **Acceptable (50-69):** Playground refinement suggested
- **Needs Optimization (<50):** Significant improvements required

---

## 7. Security Architecture

### 7.1 Authentication System ✅ **IMPLEMENTED**

#### JWT Implementation
```javascript
// Token generation with comprehensive payload
const tokenPayload = {
  userId: user._id,
  email: user.email,
  role: user.role,
  company: user.company,
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
};

const token = jwt.sign(tokenPayload, process.env.JWT_SECRET, {
  algorithm: 'HS256',
  issuer: 'agrizy-formulation-platform'
});
```

#### Session Management
- **Storage:** MongoDB with TTL indexes for automatic cleanup
- **Refresh Tokens:** Automatic token renewal mechanism
- **Blacklisting:** Revoked token tracking for security
- **Multi-device:** Support for multiple concurrent sessions

### 7.2 Data Security ✅ **IMPLEMENTED**

#### Encryption & Hashing
- **Passwords:** BCrypt with 12 salt rounds
- **Sensitive Data:** AES-256 encryption for PII
- **API Keys:** Environment variable storage
- **Database:** MongoDB encryption at rest capabilities

#### Input Validation
```javascript
// Comprehensive validation pipeline
const validation = {
  authentication: joi.object({
    email: joi.string().email().required(),
    password: joi.string().min(8).required()
  }),
  formulation: ajv.compile(formulationSchema),
  projects: joi.object({
    name: joi.string().min(1).max(100).required(),
    industry: joi.string().valid('beverages', 'nutraceuticals', 'cosmetics')
  })
};
```

### 7.3 API Security ✅ **IMPLEMENTED**

#### Security Middleware Stack
```javascript
// Express security configuration
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"]
    }
  }
}));

app.use(cors({
  origin: process.env.FRONTEND_URL,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
}));
```

---

## 8. Performance & Scalability

### 8.1 Performance Optimization ✅ **IMPLEMENTED**

#### Frontend Performance
```javascript
// Code splitting and lazy loading
const Dashboard = lazy(() => import('./pages/Dashboard'));
const ProductPlayground = lazy(() => import('./pages/ProductPlayground'));

// Component optimization
const FormulationCard = memo(({ formulation, onSelect }) => {
  return (
    <div onClick={() => onSelect(formulation.id)}>
      {formulation.name}
    </div>
  );
});

// API call optimization
const debouncedSearch = useMemo(
  () => debounce(searchProjects, 300),
  []
);
```

#### Backend Performance
```javascript
// Database query optimization
const getProjectsOptimized = async (userId, page = 1) => {
  return await Project.aggregate([
    { $match: { user_id: userId } },
    { $project: { 
      name: 1, 
      status: 1, 
      'current_formulation.quality_score': 1,
      updated_at: 1 
    }},
    { $sort: { updated_at: -1 } },
    { $skip: (page - 1) * 20 },
    { $limit: 20 }
  ]);
};

// Response compression
app.use(compression({
  filter: (req, res) => {
    return compression.filter(req, res);
  },
  threshold: 0
}));
```

### 8.2 Scalability Architecture ✅ **READY**

#### Horizontal Scaling Readiness
- **Stateless Design:** All state stored in database or client
- **Load Balancer Ready:** No server-side session dependencies
- **Database Scaling:** MongoDB sharding support
- **Microservices Ready:** Clear service boundaries

#### Performance Metrics
| Metric | Current | Target | Status |
|--------|---------|--------|---------|
| **API Response Time** | <200ms avg | <500ms | ✅ Achieved |
| **Database Query Time** | <100ms avg | <300ms | ✅ Achieved |
| **Formulation Generation** | <30s avg | <60s | ✅ Achieved |
| **Page Load Time** | <1s | <2s | ✅ Achieved |
| **Concurrent Users** | Tested 100+ | 1000+ | ✅ Architecture supports |

---

## 9. Development Workflow

### 9.1 Development Environment ✅ **CONFIGURED**

#### Prerequisites
```bash
# Required software
Node.js 18+
MongoDB 6.0+
Git 2.30+
npm 8+

# Optional tools
MongoDB Compass (GUI)
Postman (API testing)
VS Code (recommended editor)
```

#### Environment Setup
```bash
# Frontend development
cd formulation-web/
npm install
cp .env.example .env
npm run dev  # Starts Vite dev server on :5050

# Backend development
cd formulation-api/
npm install
cp .env.example .env
npm run dev  # Starts Express server on :5000

# Database
# Ensure MongoDB is running locally or configure connection
```

### 9.2 Development Scripts ✅ **ORGANIZED**

#### Available Commands
```json
// Frontend (formulation-web/package.json)
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext js,jsx"
  }
}

// Backend (formulation-api/package.json)
{
  "scripts": {
    "dev": "nodemon server.js",
    "start": "node server.js",
    "test": "jest",
    "lint": "eslint src --ext js"
  }
}
```

#### Development Assets (@temp/)
- **Scripts:** Database initialization, migration utilities
- **Tests:** API testing, integration validation
- **Documentation:** Historical implementation plans
- **Organization:** Clean separation of development tools

### 9.3 Code Quality ✅ **ENFORCED**

#### Linting Configuration
```javascript
// ESLint configuration
module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    '@eslint/js/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended'
  ],
  rules: {
    'react/prop-types': 'warn',
    'no-unused-vars': 'warn',
    'no-console': 'warn'
  }
};
```

#### Code Standards
- **JavaScript:** ES2022+ features, async/await patterns
- **React:** Functional components with hooks
- **Database:** Mongoose schema validation
- **API:** RESTful design with consistent response formats
- **Documentation:** JSDoc comments for complex functions

---

## 10. Deployment Architecture

### 10.1 Deployment Strategy ✅ **READY**

#### Production Environment Architecture
```
Load Balancer (Nginx/AWS ALB)
    ↓
Frontend (Static Hosting)     Backend (Node.js Servers)
    ↓                              ↓
CDN (CloudFront)              Database Cluster (MongoDB Atlas)
    ↓                              ↓
Static Assets                 AI Services (Claude API)
```

#### Deployment Options

##### Option 1: Cloud Native (Recommended)
```yaml
Frontend:
  - Vercel/Netlify for React app
  - CloudFront CDN for global delivery
  - Environment variables for API endpoints

Backend:
  - AWS ECS/Fargate for containerized deployment
  - Application Load Balancer for traffic distribution
  - MongoDB Atlas for managed database
  - CloudWatch for monitoring and logging

Monitoring:
  - DataDog/New Relic for APM
  - CloudWatch for infrastructure metrics
  - Sentry for error tracking
```

##### Option 2: Traditional VPS
```yaml
Frontend:
  - Nginx serving built React assets
  - PM2 for process management
  - SSL certificate (Let's Encrypt)

Backend:
  - PM2 for Node.js process management
  - Nginx reverse proxy
  - MongoDB replica set
  - Logrotate for log management
```

### 10.2 Environment Configuration ✅ **READY**

#### Environment Variables
```bash
# Frontend (.env)
VITE_API_BASE_URL=https://api.agrizy.com
VITE_APP_VERSION=1.0.0
VITE_LOG_LEVEL=info
VITE_ENABLE_DEBUG_MODE=false

# Backend (.env)
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb+srv://cluster.mongodb.net/agrizy_prod
JWT_SECRET=your-super-secure-secret
ANTHROPIC_API_KEY=your-claude-api-key
LOG_LEVEL=info
CORS_ORIGIN=https://app.agrizy.com
```

#### Security Configuration
- **HTTPS:** SSL/TLS certificates for all endpoints
- **CORS:** Restricted to production domain
- **Rate Limiting:** Production-appropriate limits
- **Environment Secrets:** Secure key management
- **Database:** Connection encryption and authentication

### 10.3 Monitoring & Maintenance ✅ **READY**

#### Logging Infrastructure
- **Centralized Logging:** All services log to shared infrastructure
- **Log Aggregation:** ELK stack or cloud-native solutions
- **Error Tracking:** Sentry integration for error monitoring
- **Performance Monitoring:** APM tools for performance insights

#### Health Checks
```javascript
// API health check endpoint
app.get('/health', async (req, res) => {
  const health = {
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    database: await checkDatabaseConnection(),
    ai_service: await checkClaudeAPI(),
    memory: process.memoryUsage(),
    version: process.env.APP_VERSION
  };
  
  res.status(200).json(health);
});
```

#### Backup Strategy
- **Database Backups:** Automated daily backups with point-in-time recovery
- **Code Backups:** Git repository with multiple remotes
- **Configuration Backups:** Environment variable documentation
- **Disaster Recovery:** Documented recovery procedures

---

## 📋 Document Information

| Attribute | Value |
|-----------|-------|
| **Version** | 2.0 |
| **Status** | Production Ready |
| **Last Updated** | August 2025 |
| **Architecture Review** | Quarterly |
| **Technical Lead** | Mark |
| **Document Owner** | Technical Team |

### Change History
- **v2.0 (Aug 2025):** Updated for split repository architecture and current implementation
- **v1.0 (Jan 2025):** Initial technical architecture documentation

### Related Documents
- [Business Requirements](./business-requirements.md)
- [Product Features](./product-features.md)
- [Logging Configuration](./logging.md)
- [Main Project README](../README.md)

---

**Note:** This technical architecture document reflects the current production-ready state of the platform with independent repositories, comprehensive logging, and enterprise-grade AI integration.