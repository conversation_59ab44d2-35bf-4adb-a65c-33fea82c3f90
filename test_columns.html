<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3-Column Layout Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .poi-container {
            /* Standard CSS Columns */
            column-count: 3;
            column-gap: 15px;
            column-fill: balance;
            
            /* Webkit prefix for Safari */
            -webkit-column-count: 3;
            -webkit-column-gap: 15px;
            -webkit-column-fill: balance;
            
            /* Mozilla prefix for Firefox */
            -moz-column-count: 3;
            -moz-column-gap: 15px;
            -moz-column-fill: balance;
            
            /* Visual styling */
            border: 2px dashed #3498db;
            padding: 15px;
            margin: 20px 0;
            background: #f8f9fa;
            
            /* Ensure proper display */
            width: 100%;
            display: block;
            overflow: visible;
        }
        
        .poi-section h3 {
            color: #2c3e50;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        .poi-section ul {
            line-height: 1.3;
            margin-bottom: 10px;
            /* Allow content to break across columns */
            -webkit-column-break-inside: auto;
            break-inside: auto;
            column-break-inside: auto;
        }
        
        .poi-section li {
            margin-bottom: 5px;
            /* Allow items to break across columns when needed */
            -webkit-column-break-inside: auto;
            break-inside: auto;
            column-break-inside: auto;
        }
        
        .poi-section ul ul {
            margin-top: 3px;
            margin-bottom: 5px;
        }
        
        .poi-section ul ul li {
            margin-bottom: 2px;
        }
        
        .column-indicator {
            position: absolute;
            top: 5px;
            left: 5px;
            background: #e74c3c;
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 3px;
        }
        
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>3-Column Layout Test</h1>
        
        <div class="debug-info">
            <strong>🔍 How to check if 3-column layout is working:</strong>
            <ul>
                <li>Look for content flowing into 3 vertical columns</li>
                <li>Text should wrap within each column</li>
                <li>Content should be balanced across columns</li>
                <li>Blue dashed border shows the container boundaries</li>
            </ul>
        </div>
        
        <!-- Test: India POI in 3 columns -->
        <div class="poi-container">
            <div class="poi-section">
                <h3>🇮🇳 India - Landform Points of Interest (3-Column Test)</h3>
                <ul>
                    <li><strong>Highest Peak:</strong> Mount Everest (Himalayas, shared with Nepal)</li>
                    <li><strong>Longest Mountain Range:</strong> Himalayas</li>
                    <li><strong>Largest Plateau:</strong> Deccan Plateau</li>
                    <li><strong>Largest Plain:</strong> Indo-Gangetic Plain</li>
                    <li><strong>Highest Plateau:</strong> Tibetan Plateau (partially in India)</li>
                    <li><strong>Unique Geological Formations:</strong>
                        <ul>
                            <li>Western Ghats (Volcanic origin)</li>
                            <li>Aravalli Range (Oldest mountain range)</li>
                            <li>Gangetic Delta (Largest delta in the world)</li>
                        </ul>
                    </li>
                    <li><strong>Island Territories:</strong>
                        <ul>
                            <li>Andaman and Nicobar Islands</li>
                            <li>Lakshadweep Islands</li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Test: World POI in 3 columns -->
        <div class="poi-container">
            <div class="poi-section">
                <h3>🌍 World - Landform Points of Interest (3-Column Test)</h3>
                <ul>
                    <li><strong>Mountain Ranges:</strong>
                        <ul>
                            <li>Himalayas (Highest - Asia)</li>
                            <li>Andes (Longest - South America)</li>
                            <li>Rocky Mountains (North America)</li>
                            <li>Alps (Europe)</li>
                            <li>Great Dividing Range (Australia)</li>
                        </ul>
                    </li>
                    <li><strong>Plateaus:</strong>
                        <ul>
                            <li>Tibetan Plateau - "Roof of the World"</li>
                            <li>Brazilian Highlands</li>
                            <li>Colorado Plateau</li>
                            <li>Ethiopian Highlands</li>
                            <li>Altiplano (South America)</li>
                        </ul>
                    </li>
                    <li><strong>Plains:</strong>
                        <ul>
                            <li>Indo-Gangetic Plain (India)</li>
                            <li>Amazon Basin (South America)</li>
                            <li>North European Plain</li>
                            <li>Great Plains (North America)</li>
                            <li>Eurasian Steppe</li>
                        </ul>
                    </li>
                    <li><strong>Unique Geological Formations:</strong>
                        <ul>
                            <li>Grand Canyon (USA) - Carved by Colorado River</li>
                            <li>Great Barrier Reef (Australia) - Largest coral reef system</li>
                            <li>Mariana Trench (Pacific Ocean) - Deepest oceanic trench</li>
                            <li>Rift Valley (East Africa) - Geological fault system</li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="debug-info">
            <strong>📋 Expected Behavior:</strong>
            <ul>
                <li>Each container should show content in 3 vertical columns</li>
                <li>Content flows from top to bottom within each column</li>
                <li>When one column fills up, content continues in the next column</li>
                <li>All three columns should be roughly balanced in height</li>
            </ul>
        </div>
    </div>
</body>
</html>