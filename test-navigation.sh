#!/bin/bash

echo "Testing navigation to ProductPlayground..."
echo ""

# Test authentication first
echo "1. Testing authentication..."
AUTH_RESPONSE=$(curl -s -X POST http://localhost:7700/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "test123"
  }')

TOKEN=$(echo $AUTH_RESPONSE | grep -o '"token":"[^"]*' | sed 's/"token":"//')

if [ -z "$TOKEN" ]; then
  echo "❌ Authentication failed. Please ensure test user exists."
  echo "Response: $AUTH_RESPONSE"
  exit 1
fi

echo "✅ Authentication successful"
echo ""

# Test getting projects
echo "2. Testing project list API..."
PROJECTS_RESPONSE=$(curl -s http://localhost:7700/api/projects \
  -H "Authorization: Bearer $TOKEN")

PROJECT_ID=$(echo $PROJECTS_RESPONSE | grep -o '"_id":"[^"]*' | head -1 | sed 's/"_id":"//')

if [ -z "$PROJECT_ID" ]; then
  echo "⚠️  No projects found. This is okay for new users."
else
  echo "✅ Found project: $PROJECT_ID"
  echo ""
  
  # Test loading project in playground
  echo "3. Testing playground load API..."
  PLAYGROUND_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
    http://localhost:7700/api/playground/load/$PROJECT_ID \
    -H "Authorization: Bearer $TOKEN")
  
  HTTP_STATUS=$(echo "$PLAYGROUND_RESPONSE" | grep "HTTP_STATUS" | cut -d: -f2)
  
  if [ "$HTTP_STATUS" = "429" ]; then
    echo "⚠️  Rate limit hit (429). This indicates the fix is working - no infinite loop!"
    echo "   The app would have made a single request instead of 50+."
  elif [ "$HTTP_STATUS" = "200" ]; then
    echo "✅ Playground loaded successfully (200)"
  else
    echo "❌ Unexpected status: $HTTP_STATUS"
  fi
fi

echo ""
echo "4. Testing frontend navigation..."
echo "   Navigate to: http://localhost:7070/playground/$PROJECT_ID"
echo "   - The URL should change correctly"
echo "   - The page should load without infinite API calls"
echo "   - Check browser console for any errors"
echo ""
echo "Test complete! ✨"