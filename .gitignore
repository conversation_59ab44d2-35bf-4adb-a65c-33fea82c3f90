# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Keep logs directory but ignore log files
!logs/.gitkeep

# @temp folder contains development assets (tests, scripts, debug tools)
# These are tracked but excluded from production builds

node_modules
dist
dist-ssr
*.local

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?


#Ignore vscode AI rules
.github/instructions/codacy.instructions.md
