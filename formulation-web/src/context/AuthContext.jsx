import React, { createContext, useContext, useReducer, useEffect } from 'react';

// Auth action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  RESTORE_SESSION: 'RESTORE_SESSION',
  CLEAR_ERROR: 'CLEAR_ERROR',
  REFRESH_TOKEN: 'REFRESH_TOKEN'
};

// Initial auth state
const initialAuthState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: true, // Start with loading true to check for stored session
  error: null,
  session: null
};

// Auth reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return {
        ...state,
        isLoading: true,
        error: null
      };
    
    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        refreshToken: action.payload.refresh_token,
        session: action.payload.session,
        isAuthenticated: true,
        isLoading: false,
        error: null
      };
    
    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        user: null,
        token: null,
        refreshToken: null,
        session: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload.error
      };
    
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...initialAuthState,
        isLoading: false // Ensure loading is false after logout
      };
    
    case AUTH_ACTIONS.RESTORE_SESSION:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
        session: action.payload.session,
        isAuthenticated: true,
        isLoading: false
      };
    
    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };
    
    case AUTH_ACTIONS.REFRESH_TOKEN:
      return {
        ...state,
        token: action.payload.token,
        refreshToken: action.payload.refresh_token
      };
    
    default:
      return state;
  }
};

// Create auth context
const AuthContext = createContext();

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialAuthState);

  // API base URL
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:7700/api';

  // Validate email domain
  const validateEmailDomain = (email) => {
    const allowedDomains = ['agrizy.in', 'agrizywellness.com', 'naturalzy.com'];
    const domain = email.split('@')[1]?.toLowerCase();
    return allowedDomains.includes(domain);
  };

  // API request helper
  const apiRequest = async (endpoint, options = {}) => {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    if (state.token && !config.headers.Authorization) {
      config.headers.Authorization = `Bearer ${state.token}`;
    }

    try {
      console.log('🔍 Making API request:', { url, method: config.method, headers: config.headers });
      const response = await fetch(url, config);
      console.log('📡 Response status:', response.status, response.statusText);
      
      const data = await response.json();
      console.log('📥 Response data:', data);

      if (!response.ok) {
        console.error('❌ API Error Response:', { status: response.status, data });
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('💥 API request failed:', error);
      throw error;
    }
  };

  // Login function
  const login = async (email, password) => {
    dispatch({ type: AUTH_ACTIONS.LOGIN_START });

    try {
      // Validate email domain
      if (!validateEmailDomain(email)) {
        throw new Error('Email domain not allowed. Only agrizy.in, agrizywellness.com, and naturalzy.com domains are permitted.');
      }

      const response = await apiRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ email, password })
      });

      if (response.success) {
        const { user, token, refresh_token, expires_at } = response.data;
        
        // Store tokens in localStorage
        localStorage.setItem('auth_token', token);
        localStorage.setItem('refresh_token', refresh_token);
        localStorage.setItem('user_data', JSON.stringify(user));
        localStorage.setItem('token_expires_at', expires_at);

        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: {
            user,
            token,
            refresh_token,
            session: { expires_at }
          }
        });

        return { success: true, user };
      } else {
        throw new Error(response.error || 'Login failed');
      }
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: { error: error.message }
      });
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      if (state.token) {
        await apiRequest('/auth/logout', {
          method: 'POST'
        });
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      // Clear localStorage regardless of API call result
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user_data');
      localStorage.removeItem('token_expires_at');

      dispatch({ type: AUTH_ACTIONS.LOGOUT });
    }
  };

  // Refresh token function
  const refreshToken = async () => {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await apiRequest('/auth/refresh', {
        method: 'POST',
        body: JSON.stringify({ refresh_token: refreshToken })
      });

      if (response.success) {
        const { token, refresh_token, expires_at } = response.data;
        
        localStorage.setItem('auth_token', token);
        localStorage.setItem('refresh_token', refresh_token);
        localStorage.setItem('token_expires_at', expires_at);

        dispatch({
          type: AUTH_ACTIONS.REFRESH_TOKEN,
          payload: { token, refresh_token }
        });

        return token;
      } else {
        throw new Error(response.error || 'Token refresh failed');
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      await logout();
      throw error;
    }
  };

  // Get current user info
  const getCurrentUser = async () => {
    try {
      const response = await apiRequest('/auth/me');
      if (response.success) {
        return response.data.user;
      }
      throw new Error(response.error || 'Failed to get user info');
    } catch (error) {
      console.error('Get current user failed:', error);
      throw error;
    }
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  // Restore session from localStorage on app start
  useEffect(() => {
    const restoreSession = async () => {
      const token = localStorage.getItem('auth_token');
      const refreshTokenValue = localStorage.getItem('refresh_token');
      const userData = localStorage.getItem('user_data');
      const expiresAt = localStorage.getItem('token_expires_at');

      if (token && refreshTokenValue && userData) {
        try {
          const user = JSON.parse(userData);
          const session = { expires_at: expiresAt };

          // Check if token is expired
          if (expiresAt && new Date(expiresAt) > new Date()) {
            dispatch({
              type: AUTH_ACTIONS.RESTORE_SESSION,
              payload: { user, token, refreshToken: refreshTokenValue, session }
            });
          } else {
            // Token expired, try to refresh
            try {
              await refreshToken();
            } catch (error) {
              // Refresh failed, clear session
              console.error('Token refresh failed during restore:', error);
              localStorage.clear();
              dispatch({ type: AUTH_ACTIONS.LOGOUT });
            }
          }
        } catch (error) {
          console.error('Session restore failed:', error);
          localStorage.clear();
          dispatch({ type: AUTH_ACTIONS.LOGOUT });
        }
      } else {
        // No stored session, set loading to false
        dispatch({ type: AUTH_ACTIONS.LOGOUT });
      }
    };

    restoreSession();
  }, []);

  // Auto token refresh before expiration
  useEffect(() => {
    if (!state.isAuthenticated || !state.token) return;

    const expiresAt = localStorage.getItem('token_expires_at');
    if (!expiresAt) return;

    const expirationTime = new Date(expiresAt).getTime();
    const currentTime = new Date().getTime();
    const timeUntilExpiry = expirationTime - currentTime;

    // Refresh token 5 minutes before expiration
    const refreshTime = timeUntilExpiry - (5 * 60 * 1000);

    if (refreshTime > 0) {
      const timeout = setTimeout(() => {
        refreshToken().catch(console.error);
      }, refreshTime);

      return () => clearTimeout(timeout);
    }
  }, [state.isAuthenticated, state.token]);

  const value = {
    ...state,
    login,
    logout,
    refreshToken,
    getCurrentUser,
    clearError,
    apiRequest,
    validateEmailDomain
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;