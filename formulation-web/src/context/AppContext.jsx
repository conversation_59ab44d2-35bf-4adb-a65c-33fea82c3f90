import React, { useState, createContext, useContext } from 'react';

// Context for app state
const AppContext = createContext();

export const AppProvider = ({ children }) => {
  const [currentStep, setCurrentStep] = useState('landing');
  const [formData, setFormData] = useState({
    industry: '',
    productType: '',
    productDescription: '',
    goals: {},
    constraints: {},
    formulation: null,
    branding: {},
    gtm: {}
  });

  const updateFormData = (key, value) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };

  return (
    <AppContext.Provider value={{ currentStep, setCurrentStep, formData, updateFormData }}>
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) throw new Error('useApp must be used within AppProvider');
  return context;
};