# Frontend Component Architecture Diagram

## Complete React Component Hierarchy

```mermaid
graph TB
    subgraph "Application Root"
        App[App.jsx]
        App --> AuthProvider[AuthContext Provider]
        App --> AppProvider[AppContext Provider]
    end
    
    subgraph "Context Layer"
        AuthProvider --> AuthContext[AuthContext.jsx]
        AppProvider --> AppContext[AppContext.jsx]
        AuthContext --> |provides| AuthState[user, token, isAuthenticated, login, logout]
        AppContext --> |provides| AppState[currentStep, projectData, formulation]
    end
    
    subgraph "Routing & Protection"
        App --> Router[React Router DOM]
        Router --> ProtectedRoute[ProtectedRoute.jsx]
        ProtectedRoute --> |guards| ProtectedPages[Protected Pages]
    end
    
    subgraph "Layout Components"
        ProtectedPages --> Layout[Layout.jsx]
        Layout --> Logo[Logo.jsx]
        Layout --> Sidebar[Sidebar Navigation]
        Layout --> MobileMenu[Mobile Menu]
    end
    
    subgraph "Page Components - Public"
        Router --> LandingPage[LandingPage.jsx]
        Router --> Login[Login.jsx]
    end
    
    subgraph "Page Components - Protected"
        ProtectedPages --> Dashboard[Dashboard.jsx]
        ProtectedPages --> Projects[Projects.jsx]
        ProtectedPages --> ProductPlayground[ProductPlayground.jsx]
        ProtectedPages --> IndustrySelection[IndustrySelection.jsx]
        ProtectedPages --> ProductTypeSelection[ProductTypeSelection.jsx]
        ProtectedPages --> SubCategorySelection[SubCategorySelection.jsx]
        ProtectedPages --> GoalSetup[GoalSetup.jsx]
        ProtectedPages --> BrandingStep[BrandingStep.jsx]
        ProtectedPages --> GTMStep[GTMStep.jsx]
        ProtectedPages --> ResultsDashboard[ResultsDashboard.jsx]
    end
    
    subgraph "Admin Pages"
        ProtectedPages --> AdminConfig[AdminConfig.jsx]
        ProtectedPages --> ParametersAdmin[ParametersAdmin.jsx]
        ProtectedPages --> FormulationBlocks[FormulationBlocks.jsx]
    end
    
    subgraph "Modal Components"
        Projects --> NewProjectWizardModal[NewProjectWizardModal.jsx]
        NewProjectWizardModal --> IndustrySelectionModal[IndustrySelectionModal.jsx]
        NewProjectWizardModal --> FormulationWizardModal[FormulationWizardModal.jsx]
        ProductPlayground --> ProcessingModal[ProcessingModal.jsx]
        ProductPlayground --> GenerationOverlay[GenerationOverlay.jsx]
    end
    
    subgraph "UI Components Library"
        Layout --> Modal[Modal.jsx]
        Layout --> OptionCard[OptionCard.jsx]
        Layout --> ProgressBar[ProgressBar.jsx]
        Layout --> InteractiveSmartMeter[InteractiveSmartMeter.jsx]
        Layout --> Breadcrumbs[Breadcrumbs.jsx]
        Layout --> PageHeader[PageHeader.jsx]
        Layout --> ContextToolbar[ContextToolbar.jsx]
    end
    
    subgraph "Form Components"
        GoalSetup --> DynamicFormField[DynamicFormField.jsx]
        GoalSetup --> FormSection[FormSection.jsx]
        ParametersAdmin --> ParamCard[ParamCard.jsx]
        AdminConfig --> TaxonomyTree[TaxonomyTree.jsx]
    end
    
    subgraph "Animation Components"
        GenerationOverlay --> LabAnimation[LabAnimation.jsx]
        GenerationOverlay --> FailedAnimation[FailedAnimation.jsx]
        ProductPlayground --> AuraOverlay[AuraOverlay.jsx]
    end
    
    subgraph "Embedded Components"
        NewProjectWizardModal --> GoalsEmbedded[GoalsEmbedded.jsx]
        NewProjectWizardModal --> SummaryEmbedded[SummaryEmbedded.jsx]
    end
    
    subgraph "Service Layer"
        App --> ConfigApi[configApi.js]
        ConfigApi --> TaxonomyApi[taxonomyApi]
        ConfigApi --> ParameterApi[parameterApi]
        ConfigApi --> GuardrailsApi[guardrailsApi]
        ConfigApi --> UiFlowApi[uiFlowApi]
    end
    
    subgraph "Utility Layer"
        App --> ApiUtils[apiUtils.js]
        App --> Logger[logger.js]
        App --> DateUtils[dateUtils.js]
        App --> ColorUtils[colorUtils.js]
        App --> AnswersCache[answersCache.js]
        App --> ParamMapping[paramMapping.js]
        App --> TaxonomyTree[taxonomyTree.js]
        App --> SectionIconMap[sectionIconMap.js]
    end
    
    subgraph "Hooks"
        NewProjectWizardModal --> UseWizardHotkeys[useWizardHotkeys.js]
    end
    
    subgraph "Styling"
        ProductPlayground --> PlaygroundColors[PlaygroundColors.css]
        App --> IndexCSS[index.css]
        App --> AppCSS[App.css]
    end
    
    classDef pageComponent fill:#e1f5fe
    classDef uiComponent fill:#f3e5f5
    classDef contextComponent fill:#e8f5e8
    classDef serviceComponent fill:#fff3e0
    classDef utilComponent fill:#fce4ec
    
    class Dashboard,Projects,ProductPlayground,IndustrySelection,ProductTypeSelection,SubCategorySelection,GoalSetup,BrandingStep,GTMStep,ResultsDashboard,AdminConfig,ParametersAdmin,FormulationBlocks pageComponent
    class Modal,OptionCard,ProgressBar,InteractiveSmartMeter,Breadcrumbs,PageHeader,ContextToolbar,DynamicFormField,FormSection,ParamCard,TaxonomyTree uiComponent
    class AuthContext,AppContext contextComponent
    class ConfigApi,TaxonomyApi,ParameterApi,GuardrailsApi,UiFlowApi serviceComponent
    class ApiUtils,Logger,DateUtils,ColorUtils,AnswersCache,ParamMapping utilComponent
```

## Component Data Flow

```mermaid
sequenceDiagram
    participant User
    participant App
    participant AuthContext
    participant ProtectedRoute
    participant Layout
    participant Page
    participant API
    
    User->>App: Navigate to protected route
    App->>AuthContext: Check authentication
    AuthContext->>AuthContext: Validate token
    AuthContext-->>App: Authentication status
    App->>ProtectedRoute: Route with auth status
    ProtectedRoute->>Layout: Render if authenticated
    Layout->>Page: Render page component
    Page->>API: Fetch data via apiRequest
    API-->>Page: Return data
    Page-->>Layout: Render with data
    Layout-->>User: Display page
```
