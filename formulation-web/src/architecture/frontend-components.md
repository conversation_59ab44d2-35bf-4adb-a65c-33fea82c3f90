# Frontend Component Architecture Documentation

## Core Application Components

### App.jsx
- **Purpose**: Main application router and context provider wrapper
- **Dependencies**: React Router DOM 7.7.0, Context providers
- **State**: Routes configuration, authentication state
- **Key Features**: Protected routing, context orchestration, route guards
- **Routes**: 15+ protected and public routes with conditional layouts

### Context Providers

#### AuthContext.jsx
- **Purpose**: Authentication state management and API request handling
- **State**: `user`, `token`, `isAuthenticated`, `loading`
- **Methods**: `login()`, `logout()`, `validateToken()`, `apiRequest()`
- **Storage**: localStorage for token persistence
- **Features**: Automatic token validation, request interceptors, logout on token expiry

#### AppContext.jsx  
- **Purpose**: Global application state management
- **State**: `currentStep`, `projectData`, `formulation`, `wizardState`
- **Methods**: `updateStep()`, `saveProject()`, `resetState()`, `updateWizardData()`
- **Features**: Wizard flow management, project data persistence

## Page Components

### LandingPage.jsx
- **Route**: `/`
- **Purpose**: Marketing entry point and user onboarding
- **Components**: Hero section, feature highlights, CTA buttons
- **Navigation**: Routes to industry selection or login
- **Features**: Responsive design, animated elements

### Dashboard.jsx
- **Route**: `/dashboard`
- **Purpose**: Project management interface and user home
- **Components**: Project cards, create new project button, recent activity
- **State**: Projects list, loading states, project counts
- **Features**: Project filtering, quick actions, statistics display

### Projects.jsx
- **Route**: `/projects`
- **Purpose**: Comprehensive project management interface
- **Components**: Project grid/list, search, filters, bulk actions
- **State**: Projects array, search query, filter states
- **Features**: Advanced filtering, sorting, project status management

### ProductPlayground.jsx
- **Route**: `/playground/:projectId`
- **Purpose**: Main formulation interface (unified component)
- **Components**: Chat interface, component editor, quality meter
- **State**: Formulation data, conversation history, quality scores
- **Features**: Real-time AI interaction, ingredient modification, version control
- **Layout**: Full-screen without sidebar for focused work

### Wizard Pages

#### IndustrySelection.jsx
- **Route**: `/industry-selection`
- **Purpose**: Industry taxonomy selection step
- **Components**: Industry cards, search functionality
- **State**: Selected industry, available industries
- **Features**: Dynamic loading from taxonomy API

#### ProductTypeSelection.jsx
- **Route**: `/product-type-selection`
- **Purpose**: Product category selection step
- **Components**: Category cards with descriptions
- **State**: Selected category, filtered categories
- **Features**: Hierarchical selection, parent-child relationships

#### SubCategorySelection.jsx
- **Route**: `/sub-category-selection`
- **Purpose**: Product sub-category selection step
- **Components**: Sub-category grid, breadcrumb navigation
- **State**: Selected sub-category, available options
- **Features**: Context-aware filtering, validation

#### GoalSetup.jsx
- **Route**: `/goal-setup`
- **Purpose**: Project goals and parameters configuration
- **Components**: Dynamic form fields, parameter cards
- **State**: Goals object, parameter answers, validation errors
- **Features**: Dynamic form generation, real-time validation

#### BrandingStep.jsx
- **Route**: `/branding`
- **Purpose**: Brand identity and positioning setup
- **Components**: Color picker, persona cards, values selection
- **State**: Branding configuration, selected options
- **Features**: Visual brand builder, persona management

#### GTMStep.jsx
- **Route**: `/gtm`
- **Purpose**: Go-to-market strategy configuration
- **Components**: Channel selection, timeline builder
- **State**: GTM strategy object, selected channels
- **Features**: Strategy visualization, timeline planning

### Admin Pages

#### AdminConfig.jsx
- **Route**: `/admin/config`
- **Purpose**: System configuration management
- **Components**: Taxonomy tree, configuration forms
- **State**: Configuration data, edit modes
- **Features**: CRUD operations, validation, bulk updates
- **Access**: Admin role required

#### ParametersAdmin.jsx
- **Route**: `/admin/parameters`
- **Purpose**: Parameter and binding management
- **Components**: Parameter cards, binding editor
- **State**: Parameters, bindings, answer sets
- **Features**: Parameter CRUD, binding management, validation
- **Access**: Admin role required

#### FormulationBlocks.jsx
- **Route**: `/admin/formulation-blocks`
- **Purpose**: Formulation block configuration
- **Components**: Block editor, schema builder
- **State**: Formulation blocks, schemas
- **Features**: Block CRUD, schema validation, dependency management
- **Access**: Admin role required

## Modal Components

### NewProjectWizardModal.jsx
- **Purpose**: Unified project creation wizard
- **Components**: Multi-step wizard, embedded pages
- **State**: Wizard step, project data, validation
- **Features**: Step navigation, data persistence, hotkey support
- **Steps**: Industry → Product Type → Sub-category → Goals → Summary

### FormulationWizardModal.jsx
- **Purpose**: Advanced formulation configuration
- **Components**: Complex form sections, validation
- **State**: Formulation parameters, compliance settings
- **Features**: Multi-section forms, conditional fields, validation

### GenerationOverlay.jsx
- **Purpose**: Formulation generation progress display
- **Components**: Progress indicators, animations, error handling
- **State**: Generation status, progress phases, error states
- **Features**: Real-time updates, cancellation, retry logic

### ProcessingModal.jsx
- **Purpose**: Generic processing status display
- **Components**: Loading animations, status messages
- **State**: Processing status, progress percentage
- **Features**: Customizable messages, timeout handling

## UI Component Library

### Logo.jsx
- **Purpose**: Brand identity component
- **Props**: `size`, `variant`, `className`
- **Variants**: `full`, `icon-only`, `white`, `dark`
- **Features**: Responsive sizing, theme variants

### InteractiveSmartMeter.jsx
- **Purpose**: Animated metric displays and quality indicators
- **Props**: `value`, `max`, `label`, `color`, `animation`
- **Features**: Smooth animations, customizable thresholds, color coding
- **Use Cases**: Quality scores, progress indicators, metrics

### ProgressBar.jsx
- **Purpose**: Multi-step workflow indicator
- **Props**: `currentStep`, `totalSteps`, `stepLabels`
- **Features**: Step validation, navigation controls, progress visualization

### Modal.jsx
- **Purpose**: Generic modal dialog component
- **Props**: `open`, `title`, `children`, `onClose`, `onSubmit`
- **Features**: Backdrop click handling, keyboard navigation, customizable actions

### OptionCard.jsx
- **Purpose**: Selectable card component for choices
- **Props**: `title`, `description`, `selected`, `onClick`, `badge`, `icon`
- **Variants**: `category`, `subcategory`, `default`
- **Features**: Hover effects, selection states, badge support

### Breadcrumbs.jsx
- **Purpose**: Navigation breadcrumb component
- **Props**: `items`, `separator`, `maxItems`
- **Features**: Overflow handling, click navigation, customizable separators

### PageHeader.jsx
- **Purpose**: Consistent page header component
- **Props**: `title`, `subtitle`, `actions`, `breadcrumbs`
- **Features**: Action buttons, responsive layout, breadcrumb integration

### ContextToolbar.jsx
- **Purpose**: Context-sensitive toolbar component
- **Props**: `actions`, `context`, `position`
- **Features**: Dynamic actions, positioning, context awareness

## Form Components

### DynamicFormField.jsx
- **Purpose**: Dynamic form field renderer
- **Props**: `field`, `value`, `onChange`, `validation`
- **Types**: Text, select, checkbox, radio, range, textarea
- **Features**: Type-specific rendering, validation, error display

### FormSection.jsx
- **Purpose**: Form section wrapper with validation
- **Props**: `title`, `description`, `children`, `validation`
- **Features**: Section validation, collapsible sections, error aggregation

### ParamCard.jsx
- **Purpose**: Parameter configuration card
- **Props**: `parameter`, `value`, `onChange`, `validation`
- **Features**: Parameter-specific UI, validation, help text

### TaxonomyTree.jsx
- **Purpose**: Hierarchical taxonomy display and selection
- **Props**: `data`, `selected`, `onSelect`, `expandable`
- **Features**: Tree navigation, search, selection, lazy loading

## Animation Components

### LabAnimation.jsx
- **Purpose**: Laboratory-themed loading animation
- **Props**: `phase`, `message`, `duration`
- **Features**: Multi-phase animation, customizable messages

### FailedAnimation.jsx
- **Purpose**: Error state animation
- **Props**: `error`, `retry`, `onRetry`
- **Features**: Error visualization, retry functionality

### AuraOverlay.jsx
- **Purpose**: Ambient visual effects for playground
- **Props**: `intensity`, `color`, `pattern`
- **Features**: Dynamic effects, performance optimization

## Embedded Components

### GoalsEmbedded.jsx
- **Purpose**: Embedded goals configuration for wizard
- **Props**: `data`, `onChange`, `validation`
- **Features**: Simplified UI, wizard integration

### SummaryEmbedded.jsx
- **Purpose**: Project summary display for wizard
- **Props**: `projectData`, `editable`, `onEdit`
- **Features**: Data visualization, edit capabilities

## Utility Components

### KeyboardTestComponent.jsx
- **Purpose**: Keyboard interaction testing
- **Props**: `onKeyPress`, `captureKeys`
- **Features**: Key capture, event logging, testing utilities

## Component Dependencies

### External Dependencies
- **React**: 19.1.0 (Core framework)
- **React Router DOM**: 7.7.0 (Routing)
- **Lucide React**: 0.525.0 (Icons)
- **Tailwind CSS**: 3.4.17 (Styling)

### Internal Dependencies
- **Context**: AuthContext, AppContext for state management
- **Services**: configApi for backend communication
- **Utils**: apiUtils, logger, dateUtils for common functionality
- **Hooks**: useWizardHotkeys for keyboard interactions

## Performance Considerations

### Code Splitting
- Lazy loading for admin components
- Route-based code splitting
- Dynamic imports for heavy components

### State Management
- Context providers for global state
- Local state for component-specific data
- Memoization for expensive computations

### Rendering Optimization
- React.memo for pure components
- useMemo and useCallback for optimization
- Virtual scrolling for large lists

## Testing Strategy

### Component Testing
- Unit tests for individual components
- Integration tests for component interactions
- Snapshot tests for UI consistency

### User Flow Testing
- End-to-end tests for critical paths
- Accessibility testing
- Cross-browser compatibility testing
