import React, { useState } from 'react';

const KeyboardTestComponent = () => {
  const [testText, setTestText] = useState('This is a test text that should be selectable and copyable with Cmd+C');
  const [logMessages, setLogMessages] = useState([]);

  const addLog = (message) => {
    setLogMessages(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const handleKeyDown = (e) => {
    addLog(`KeyDown: ${e.key} (${e.code}) - metaKey: ${e.metaKey}, ctrlKey: ${e.ctrlKey}`);
    
    // Check for copy/paste shortcuts
    if ((e.metaKey || e.ctrlKey) && e.key === 'c') {
      addLog('Copy shortcut detected!');
    }
    if ((e.metaKey || e.ctrlKey) && e.key === 'v') {
      addLog('Paste shortcut detected!');
    }
    if ((e.metaKey || e.ctrlKey) && e.key === 'a') {
      addLog('Select All shortcut detected!');
      // Only handle select all for div elements, let input/textarea use native behavior
      if (e.target.tagName === 'DIV') {
        e.preventDefault();
        const range = document.createRange();
        range.selectNodeContents(e.target);
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
  };

  // Separate handler for input/textarea that only logs, doesn't interfere with shortcuts
  const handleInputKeyDown = (e) => {
    addLog(`KeyDown: ${e.key} (${e.code}) - metaKey: ${e.metaKey}, ctrlKey: ${e.ctrlKey}`);
    
    // Only log shortcuts for input/textarea, don't interfere
    if ((e.metaKey || e.ctrlKey) && e.key === 'c') {
      addLog('Copy shortcut detected!');
    }
    if ((e.metaKey || e.ctrlKey) && e.key === 'v') {
      addLog('Paste shortcut detected!');
    }
    if ((e.metaKey || e.ctrlKey) && e.key === 'a') {
      addLog('Select All shortcut detected!');
      // Don't prevent default or do anything else - let browser handle it
    }
  };

  const handleKeyUp = (e) => {
    addLog(`KeyUp: ${e.key} (${e.code})`);
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h2 className="text-xl font-bold mb-4">Keyboard Shortcut Test Component</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Test Text (should be selectable):</label>
          <div 
            className="p-3 border border-gray-300 rounded bg-gray-50 select-text"
            onKeyDown={handleKeyDown}
            onKeyUp={handleKeyUp}
            tabIndex={0}
          >
            {testText}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Test Input:</label>
          <input
            type="text"
            value={testText}
            onChange={(e) => setTestText(e.target.value)}
            onKeyDown={handleInputKeyDown}
            onKeyUp={handleKeyUp}
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="Type here and test copy/paste"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Test Textarea:</label>
          <textarea
            value={testText}
            onChange={(e) => setTestText(e.target.value)}
            onKeyDown={handleInputKeyDown}
            onKeyUp={handleKeyUp}
            className="w-full p-2 border border-gray-300 rounded h-20"
            placeholder="Type here and test copy/paste"
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Keyboard Event Log:</h3>
          <div className="h-40 overflow-y-auto border border-gray-300 rounded p-2 bg-gray-50 text-sm font-mono">
            {logMessages.length === 0 ? (
              <div className="text-gray-500">No keyboard events logged yet. Try typing or using shortcuts...</div>
            ) : (
              logMessages.map((msg, index) => (
                <div key={index} className="mb-1">{msg}</div>
              ))
            )}
          </div>
        </div>

        <button
          onClick={() => setLogMessages([])}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Clear Log
        </button>
      </div>
    </div>
  );
};

export default KeyboardTestComponent;