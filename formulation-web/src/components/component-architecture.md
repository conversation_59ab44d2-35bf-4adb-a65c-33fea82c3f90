# Frontend Component Architecture

## Core Application Components

### App.jsx
- **Purpose**: Main application router and context provider wrapper
- **Dependencies**: React Router DOM 7.7.0, Context providers
- **State**: Routes configuration, authentication state
- **Key Features**: Protected routing, context orchestration

### Context Providers

#### AuthContext.jsx
- **Purpose**: Authentication state management
- **State**: user, token, isAuthenticated, loading
- **Methods**: login(), logout(), validateToken()
- **Storage**: localStorage for token persistence

#### AppContext.jsx  
- **Purpose**: Global application state
- **State**: currentStep, projectData, formulation
- **Methods**: updateStep(), saveProject(), resetState()

#### ProjectContext.jsx
- **Purpose**: Project-specific state management
- **State**: activeProject, formulations, versions
- **Methods**: createProject(), updateFormulation(), saveVersion()

## Page Components

### LandingPage.jsx
- **Route**: `/`
- **Purpose**: Marketing entry point
- **Components**: Hero section, feature highlights, CTA buttons
- **Navigation**: Routes to industry selection or login

### Dashboard.jsx
- **Route**: `/dashboard`
- **Purpose**: Project management interface
- **Components**: Project cards, create new project, recent activity
- **State**: projects list, loading states

### ProductPlayground.jsx
- **Route**: `/playground/:projectId`
- **Purpose**: Main formulation interface (unified component)
- **Components**: ChatInterface, ComponentEditor, QualityMeter
- **State**: formulation data, conversation history, quality scores
- **Features**: Real-time AI interaction, ingredient modification

## UI Component Library

### Logo.jsx
- **Purpose**: Brand identity component
- **Props**: size, variant, className
- **Variants**: full, icon-only, white, dark

### InteractiveSmartMeter.jsx
- **Purpose**: Animated metric displays
- **Props**: value, max, label, color, animation
- **Features**: Smooth animations, customizable thresholds

### ProgressBar.jsx
- **Purpose**: Multi-step workflow indicator
- **Props**: currentStep, totalSteps, stepLabels
- **Features**: Step validation, navigation controls