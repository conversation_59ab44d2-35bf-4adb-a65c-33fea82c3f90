import React from 'react';
import { ChevronRight } from 'lucide-react';

// Simple, subtle breadcrumbs: small, sharp, rich black
const Breadcrumbs = ({ items = [], className = '', size = 'sm' }) => {
  if (!items || items.length === 0) return null;
  const sizeClass = size === 'base' ? 'text-base' : size === 'xs' ? 'text-xs' : 'text-sm';
  return (
    <nav aria-label="Breadcrumb" className={`mb-2 ${className}`}>
      <ol className={`flex items-center gap-1 ${sizeClass} text-slate-900 font-medium`}>
        {items.map((item, idx) => (
          <li key={idx} className="flex items-center gap-1">
            {item.to ? (
              <a href={item.to} className="hover:underline">
                {item.label}
              </a>
            ) : (
              <span>{item.label}</span>
            )}
            {idx < items.length - 1 && (
              <ChevronRight className="w-3.5 h-3.5" aria-hidden="true" />
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumbs;
