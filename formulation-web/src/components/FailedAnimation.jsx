import React from 'react';

const FailedAnimation = ({ size = 64, className = '' }) => {
  return (
    <div className={`relative ${className}`} style={{ width: size, height: size }}>
      {/* Cracked Beaker/Flask - Laying Down */}
      <div className="absolute inset-0 flex items-center justify-center">
        <svg
          width={size * 0.8}
          height={size * 0.6}
          viewBox="0 0 40 24"
          className="text-red-500"
        >
          {/* Flask body (conical shape laying down) */}
          <path
            d="M5 8 L22 8 L25 12 L25 16 L22 20 L5 20 L3 16 L3 12 Z"
            fill="currentColor"
            fillOpacity="0.2"
            stroke="currentColor"
            strokeWidth="1.5"
          />
          
          {/* Flask neck (cylindrical) */}
          <rect
            x="22"
            y="11"
            width="6"
            height="6"
            fill="currentColor"
            fillOpacity="0.2"
            stroke="currentColor"
            strokeWidth="1.5"
          />
          
          {/* Flask opening (rim) */}
          <ellipse
            cx="28"
            cy="14"
            rx="0.5"
            ry="3"
            fill="currentColor"
            fillOpacity="0.8"
          />
          
          {/* Crack lines on the flask body */}
          <path
            d="M8 10 L12 16 M15 9 L18 18 M20 11 L21 17"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            className="animate-pulse"
          />
          
          {/* Spilled liquid pool */}
          <ellipse
            cx="3"
            cy="22"
            rx="4"
            ry="1.5"
            fill="currentColor"
            fillOpacity="0.3"
            className="animate-pulse"
          />
          
          {/* Additional spilled drops */}
          <circle
            cx="9"
            cy="22"
            r="1"
            fill="currentColor"
            fillOpacity="0.25"
            className="animate-pulse"
          />
          <circle
            cx="13"
            cy="23"
            r="0.8"
            fill="currentColor"
            fillOpacity="0.2"
            className="animate-pulse"
          />
        </svg>
      </div>

      {/* Error bubbles - floating upward from flask mouth */}
      <div className="absolute inset-0">
        {[...Array(4)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-red-400 rounded-full opacity-60"
            style={{
              left: `${68 + i * 8}%`,
              top: `${45 + i * 5}%`,
              animation: `failedBubble ${2 + i * 0.5}s infinite ease-in-out`,
              animationDelay: `${i * 0.3}s`,
            }}
          />
        ))}
      </div>

      {/* Smoke/vapor effect */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2">
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-gray-400 rounded-full opacity-40"
            style={{
              left: `${-5 + i * 5}px`,
              animation: `failedSmoke ${3 + i * 0.5}s infinite ease-out`,
              animationDelay: `${i * 0.4}s`,
            }}
          />
        ))}
      </div>

      <style>{`
        @keyframes failedBubble {
          0% {
            transform: translateY(0) scale(1);
            opacity: 0.6;
          }
          50% {
            transform: translateY(-20px) scale(1.2);
            opacity: 0.8;
          }
          100% {
            transform: translateY(-40px) scale(0.8);
            opacity: 0;
          }
        }

        @keyframes failedSmoke {
          0% {
            transform: translateY(0) translateX(0) scale(0.5);
            opacity: 0.4;
          }
          50% {
            transform: translateY(-15px) translateX(5px) scale(1);
            opacity: 0.6;
          }
          100% {
            transform: translateY(-30px) translateX(10px) scale(1.5);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
};

export default FailedAnimation;