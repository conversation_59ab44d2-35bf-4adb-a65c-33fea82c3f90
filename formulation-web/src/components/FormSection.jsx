import React from 'react';
import { pickIconFromTitle } from '../utils/sectionIconMap';

const FormSection = ({ title, meta = null, icon: IconProp = null, children, className = '' }) => {
  const Icon = IconProp || pickIconFromTitle(title);
  return (
    <section className={`bg-white rounded-xl border border-slate-200 p-5 md:p-6 ${className}`}>
      {(title || meta) && (
        <div className="grid grid-cols-1 md:grid-cols-2 items-center gap-3 mb-4 pb-2 border-b border-slate-100">
          <div className="flex items-center gap-2">
            <span className="inline-flex items-center justify-center h-7 w-7 rounded-md bg-emerald-50 text-emerald-700 border border-emerald-100">
              <Icon className="w-4 h-4" />
            </span>
            <h2 className="text-base font-semibold text-slate-900 capitalize">{title}</h2>
          </div>
          {meta ? (
            <div className="justify-self-end text-xs md:text-sm text-slate-600">{meta}</div>
          ) : <div />}
        </div>
      )}
      {children}
    </section>
  );
};

export default FormSection;
