import React, { useEffect, useState } from 'react';
import { <PERSON>, Sparkles, Zap } from 'lucide-react';

const ProcessingModal = ({ isVisible, message = "Processing your request..." }) => {
  const [dots, setDots] = useState('');
  const [pulseIndex, setPulseIndex] = useState(0);

  useEffect(() => {
    if (!isVisible) {
      setDots('');
      setPulseIndex(0);
      return;
    }

    // Animated dots
    const dotsInterval = setInterval(() => {
      setDots(prev => prev.length >= 3 ? '' : prev + '.');
    }, 500);

    // Pulse animation for icons
    const pulseInterval = setInterval(() => {
      setPulseIndex(prev => (prev + 1) % 3);
    }, 800);

    return () => {
      clearInterval(dotsInterval);
      clearInterval(pulseInterval);
    };
  }, [isVisible]);

  if (!isVisible) return null;

  const icons = [Brain, Spark<PERSON>, Zap];

  return (
    <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-white/95 backdrop-blur-md rounded-3xl shadow-2xl border border-white/30 px-8 py-6 max-w-sm mx-4">
        {/* Animated icons */}
        <div className="flex justify-center space-x-4 mb-6">
          {icons.map((Icon, index) => (
            <div
              key={index}
              className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${
                pulseIndex === index
                  ? 'bg-emerald-500 text-white scale-110 shadow-lg'
                  : 'bg-emerald-100 text-emerald-600 scale-100'
              }`}
            >
              <Icon className="w-6 h-6" />
            </div>
          ))}
        </div>

        {/* Message */}
        <div className="text-center">
          <p className="text-lg font-medium text-slate-800 mb-2">
            Optimizing formulation{dots}
          </p>
          <p className="text-sm text-slate-500">
            AI is working its magic ✨
          </p>
        </div>

        {/* Simple animated progress line */}
        <div className="mt-6">
          <div className="w-full h-1 bg-slate-200 rounded-full overflow-hidden">
            <div className="h-full bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full animate-pulse origin-left transform">
              <div className="w-full h-full bg-gradient-to-r from-emerald-500 to-teal-500 animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProcessingModal;