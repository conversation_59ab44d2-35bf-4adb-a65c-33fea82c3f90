import React from 'react';
import { Check } from 'lucide-react';

const ProgressBar = ({ steps, currentStep }) => {
  const currentStepIndex = steps.indexOf(currentStep);

  const getStepClass = (index) => {
    if (index < currentStepIndex) {
      return 'completed';
    }
    if (index === currentStepIndex) {
      return 'current';
    }
    return 'upcoming';
  };

  return (
    <div className="w-full my-8">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const status = getStepClass(index);
          return (
            <React.Fragment key={step}>
              <div className="flex flex-col items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    status === 'completed' ? 'bg-green-500 text-white' : ''
                  } ${
                    status === 'current' ? 'bg-green-200 text-green-700 border-2 border-green-500' : ''
                  } ${
                    status === 'upcoming' ? 'bg-gray-200 text-gray-500' : ''
                  }`}
                >
                  {status === 'completed' ? <Check size={16} /> : index + 1}
                </div>
                <p
                  className={`mt-2 text-xs text-center ${
                    status === 'current' ? 'font-bold text-green-600' : 'text-gray-500'
                  }`}
                >
                  {step.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </p>
              </div>
              {index < steps.length - 1 && (
                <div
                  className={`flex-1 h-1 mx-2 ${
                    status === 'completed' ? 'bg-green-500' : 'bg-gray-200'
                  }`}
                ></div>
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default ProgressBar;