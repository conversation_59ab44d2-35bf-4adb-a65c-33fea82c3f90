import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ader2, <PERSON><PERSON><PERSON>cle2 } from 'lucide-react';
import <PERSON><PERSON>nimation from './LabAnimation';

export default function AuraOverlay({ open, loading, text, onUse, onClose }) {
  if (!open) return null;
  return (
    <div className="fixed inset-0 z-[70]">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-slate-900/60 backdrop-blur-sm" onClick={onClose} />

      {/* Magical ambient layers */}
      <div className="pointer-events-none absolute inset-0 overflow-hidden">
        {/* Soft gradient blobs */}
        <div className="absolute -top-16 -left-10 h-56 w-56 rounded-full blur-3xl opacity-30 aura-float" style={{ background: 'radial-gradient(closest-side, rgba(16,185,129,.55), rgba(16,185,129,0))' }} />
        <div className="absolute -bottom-20 -right-8 h-64 w-64 rounded-full blur-3xl opacity-30 aura-float" style={{ animationDelay: '1.1s', background: 'radial-gradient(closest-side, rgba(20,184,166,.45), rgba(20,184,166,0))' }} />
        {/* Twinkling sparkles */}
        {[...Array(6)].map((_, i) => (
          <span key={i} className="twinkle absolute h-1.5 w-1.5 rounded-full bg-emerald-300/80" style={{ top: `${10 + i*12}%`, left: `${15 + (i*13)%70}%`, animationDelay: `${i*0.25}s` }} />
        ))}
      </div>

      {/* Dialog */}
      <div className="absolute inset-0 flex items-center justify-center p-4">
        <div className="relative w-full max-w-2xl rounded-2xl border border-white/20 bg-white/95 shadow-2xl glow-pulse">
          {/* Top accent & close */}
          <span className="absolute inset-x-0 -top-px h-1 rounded-t-2xl bg-gradient-to-r from-emerald-400 via-emerald-500 to-emerald-600" />
          <button
            aria-label="Close"
            onClick={onClose}
            className="absolute top-3 right-3 p-2 rounded-md text-slate-600 hover:bg-slate-100"
          >
            <X className="w-5 h-5" />
          </button>

          {/* Header */}
          <div className="px-5 pt-5 pb-3 flex items-center gap-2">
            <div className="h-8 w-8 rounded-md bg-emerald-50 border border-emerald-100 text-emerald-700 flex items-center justify-center">
              <Sparkles className="w-5 h-5" />
            </div>
            <h3 className="text-lg font-semibold text-slate-900">Aura Inspiration</h3>
          </div>

          {/* Content */}
          <div className="px-5 pb-5">
            {loading ? (
              <div className="relative overflow-hidden rounded-xl border border-slate-200 bg-white p-6 text-center">
                <LabAnimation />
                <p className="text-slate-900 font-medium">Channeling the wisdom of the cosmos…</p>
                <p className="text-slate-500 text-sm">Weaving insights into an elegant tapestry of innovation.</p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="rounded-xl border border-emerald-200 bg-emerald-50 p-4">
                  <div className="flex items-start gap-2">
                    <CheckCircle2 className="w-5 h-5 text-emerald-600 mt-0.5" />
                    <p className="text-slate-800 leading-relaxed whitespace-pre-line">{text}</p>
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <button onClick={onClose} className="px-4 py-2 rounded-lg border border-slate-300 text-slate-700 hover:bg-slate-50">Close</button>
                  <button onClick={onUse} className="px-4 py-2 rounded-lg bg-emerald-600 text-white hover:bg-emerald-700">Use This</button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
