import React, { useRef, useState } from 'react';
import IndustrySelection from '../pages/IndustrySelection';
import ProductTypeSelection from '../pages/ProductTypeSelection';
import SubCategorySelection from '../pages/SubCategorySelection';
import GoalsEmbedded from '../pages/GoalsEmbedded';
import SummaryEmbedded from '../pages/SummaryEmbedded';
import GenerationOverlay from './GenerationOverlay';
import { useAuth } from '../context/AuthContext';
import { useApp } from '../context/AppContext';
import { X } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Breadcrumbs from './Breadcrumbs';
import useWizardHotkeys from '../hooks/useWizardHotkeys';

// Simple slide transitions using CSS classes defined in index.css
const SlideLayer = ({ children, className }) => (
  <div className={`absolute inset-0 ${className}`}>{children}</div>
);

const NewProjectWizardModal = ({ onClose }) => {
  const [step, setStep] = useState(0); // 0: industry, 1: category, 2: subcategory, 3: goals, 4: summary
  const [anim, setAnim] = useState(''); // animation class
  const navigate = useNavigate();
  const goalsRef = useRef(null);
  const [canNext, setCanNext] = useState(false);
  const { apiRequest } = useAuth();
  const [overlayProject, setOverlayProject] = useState(null);
  const { formData } = useApp();

  const go = (next) => {
    const dir = next > step ? 'forward' : 'back';
    setAnim(dir === 'forward' ? 'slide-out-left' : 'slide-out-right');
    // Delay to start next slide-in
    setTimeout(() => {
      setStep(next);
      setAnim(dir === 'forward' ? 'slide-in-right' : 'slide-in-left');
      // clear anim after transition
      setTimeout(() => setAnim(''), 260);
    }, 10);
  };

  const close = () => {
    onClose?.();
  };

  const handleFinish = () => {
    // Move to goals embedded
    go(3);
  };

  const goSummary = () => {
    go(4);
  };

  const generateProject = async () => {
    try {
      const payload = {
        name: `${(formData.productSubCategory || formData.productCategory || 'Custom').replace(/-/g,' ')} Project`,
        industry: formData.industry,
        category: formData.productCategory,
        sub_category: formData.productSubCategory,
        description: formData.notes || formData.productDescription || '',
        goals: formData.goals || {}
      };
      const resp = await apiRequest('/projects', { method: 'POST', body: JSON.stringify(payload) });
      const projectId = resp?.data?.projectId;
      if (projectId) {
        setOverlayProject(projectId);
      } else {
        const msg = resp?.error || 'Unexpected response creating project';
        alert(msg);
      }
    } catch (e) {
      console.error('Generate project failed:', e);
      alert(e?.message || 'Failed to start project generation');
    }
  };

  // Hotkeys: Esc closes modal, ArrowLeft goes back
  useWizardHotkeys({ onEsc: close, enabled: true });

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/50" onClick={close} />
      <div className="relative bg-white rounded-2xl shadow-2xl border border-slate-200 w-full max-w-6xl h-[80vh] overflow-hidden">
        <button
          className="absolute top-3 right-3 p-2 rounded-md hover:bg-slate-100 text-slate-600"
          onClick={close}
          aria-label="Close"
        >
          <X className="w-5 h-5" />
        </button>
        <div className="relative w-full h-full flex flex-col">
          {/* Sleek top bar */}
          <div className="sticky top-0 z-10 bg-white border-b border-slate-200 px-6 py-3 flex items-center justify-between">
            <div className="text-slate-900 font-semibold">New Project</div>
            <Breadcrumbs items={[{ label: 'New Project' }, { label: step === 0 ? 'Industry' : step === 1 ? 'Category' : step === 2 ? 'Product Type' : step === 3 ? 'Goals' : 'Summary' }]} />
          </div>
          <div className="relative flex-1 overflow-y-auto">
          {step === 0 && (
            <SlideLayer className={anim}>
              <div className="p-6">
                <h2 className="text-xl font-semibold text-slate-900 mb-1">Choose Your Industry</h2>
                <p className="text-slate-500 italic mb-3">Select the industry that best matches your formulation needs</p>
                <IndustrySelection embedded onSelect={() => { setCanNext(true); go(1); }} />
              </div>
            </SlideLayer>
          )}
          {step === 1 && (
            <SlideLayer className={anim}>
              <div className="p-6">
                <h2 className="text-xl font-semibold text-slate-900 mb-1">Select Product Category</h2>
                <p className="text-slate-500 italic mb-3">Choose the category for your product</p>
                <ProductTypeSelection embedded onSelectCategory={() => { setCanNext(true); go(2); }} onContinueCustom={handleFinish} />
              </div>
            </SlideLayer>
          )}
          {step === 2 && (
            <SlideLayer className={anim}>
              <div className="p-6">
                <h2 className="text-xl font-semibold text-slate-900 mb-1">Select Specific Product Type</h2>
                <p className="text-slate-500 italic mb-3">Choose the exact product you want to formulate</p>
                <SubCategorySelection embedded onSelectSubcategory={handleFinish} onContinueCustom={handleFinish} />
              </div>
            </SlideLayer>
          )}
          {step === 3 && (
            <SlideLayer className={anim}>
              <div className="p-6">
                <h2 className="text-xl font-semibold text-slate-900 mb-1">Set Your Goals & Constraints</h2>
                <p className="text-slate-500 italic mb-3">Define objectives and key requirements</p>
                <GoalsEmbedded ref={goalsRef} onComplete={() => { goSummary(); }} />
              </div>
            </SlideLayer>
          )}
          {step === 4 && (
            <SlideLayer className={anim}>
              <div className="p-6">
                <SummaryEmbedded />
              </div>
            </SlideLayer>
          )}
          </div>
          {/* Footer controls */}
          <div className="sticky bottom-0 border-t border-slate-200 p-4 flex items-center justify-between bg-white">
            <button
              onClick={() => (step === 0 ? close() : go(step - 1))}
              className="px-4 py-2 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50"
            >
              Back
            </button>
            <button
              onClick={async () => {
                if (step < 2) {
                  // Next is handled by selection; keep disabled to avoid skipping
                  return;
                }
                if (step === 2) {
                  // Move to goals after subcategory if user chooses to click next
                  handleFinish();
                } else if (step === 3) {
                  if (goalsRef.current) {
                    const ok = await goalsRef.current.submit();
                    if (ok) goSummary();
                  }
                } else if (step === 4) {
                  await generateProject();
                }
              }}
              className={`px-6 py-2 rounded-lg text-white shadow-sm ${step >= 3 ? 'bg-emerald-600 hover:bg-emerald-700' : 'bg-slate-400 cursor-not-allowed'}`}
            >
              {step === 4 ? 'See the Magic Happen' : step === 3 ? 'Continue' : 'Next'}
            </button>
          </div>
          {overlayProject && (
            <GenerationOverlay projectId={overlayProject} onClose={() => setOverlayProject(null)} />
          )}
        </div>
      </div>
    </div>
  );
};

export default NewProjectWizardModal;
