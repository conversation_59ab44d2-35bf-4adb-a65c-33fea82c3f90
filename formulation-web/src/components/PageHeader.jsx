import React from 'react';
import Breadcrumbs from './Breadcrumbs';

// Consistent left-aligned page header with optional right slot
const PageHeader = ({ title, subtitle, breadcrumbs = null, rightSlot = null }) => {
  return (
    <div className="mb-6 md:mb-8">
      {breadcrumbs && <Breadcrumbs items={breadcrumbs} />}
      <div className="flex items-start justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-slate-900 mb-1">{title}</h1>
          {subtitle && (
            <p className="text-base md:text-lg text-slate-600">{subtitle}</p>
          )}
        </div>
        {rightSlot && <div className="shrink-0">{rightSlot}</div>}
      </div>
    </div>
  );
};

export default PageHeader;

