import React, { useMemo, useState, useEffect } from 'react';
import { ChevronRight, ChevronDown, Layers } from 'lucide-react';
import { buildTaxonomyTree, filterTaxonomiesByQuery } from '../utils/taxonomyTree';

const Toggle = ({ open }) => (
  open ? <ChevronDown className="w-4 h-4 text-slate-500" /> : <ChevronRight className="w-4 h-4 text-slate-500" />
);

const Badge = ({ children, color='slate' }) => (
  <span className={`ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-[11px] border bg-${color}-50 text-${color}-700 border-${color}-200`}>{children}</span>
);

const levelColor = (level) => {
  if (level === 'industry') return 'emerald';
  if (level === 'category') return 'sky';
  if (level === 'sub_category') return 'violet';
  return 'slate';
};

const Node = ({ node, depth = 0, expanded, setExpanded, onSelect, selectedId, onDropMove }) => {
  const hasChildren = node.children && node.children.length > 0;
  const id = String(node._id);
  const isOpen = !!expanded[id];
  const color = levelColor(node.level);

  const isSelected = selectedId && String(selectedId)===id;
  // Neutral, clearly visible highlight
  const hlBg = `bg-slate-100`;
  const hlRing = `ring-2 ring-slate-300`;
  return (
    <div>
      <div
        className={`flex items-center py-1.5 rounded-md w-full px-1 ${isSelected ? `${hlBg} ${hlRing}` : ''}`}
        draggable
        onDragStart={(e)=>{ e.dataTransfer.setData('text/node-id', String(node._id)); }}
        onDragOver={(e)=>{ e.preventDefault(); }}
        onDrop={(e)=>{ e.preventDefault(); const dragId = e.dataTransfer.getData('text/node-id'); if (dragId && onDropMove) onDropMove(dragId, String(node._id), node); }}
      >
        <button
          className={`mr-1 h-5 w-5 flex items-center justify-center ${hasChildren ? '' : 'opacity-0'}`}
          onClick={() => setExpanded(prev => ({ ...prev, [id]: !isOpen }))}
          aria-label={isOpen ? 'Collapse' : 'Expand'}
        >
          <Toggle open={isOpen} />
        </button>
        <div className="flex-1 min-w-0">
          <div className={`inline-flex items-center gap-2 ${isSelected ? 'text-slate-900' : ''}`}>
            <span className={`inline-flex items-center justify-center h-6 w-6 rounded-md border bg-${color}-50 text-${color}-700 border-${color}-100`}>
              <Layers className="w-3.5 h-3.5" />
            </span>
            <button onClick={() => onSelect?.(node)} className={`text-sm font-medium truncate focus:outline-none ${isSelected ? 'text-slate-900' : 'text-slate-800 hover:underline'}`}>
              {node.name || node.slug}
            </button>
            <Badge color={color}>{node.level}</Badge>
            {hasChildren && <Badge color={color}>{node.children.length}</Badge>}
            {/* slug hidden per request */}
          </div>
        </div>
      </div>
      {hasChildren && isOpen && (
        <div className="ml-6 border-l border-slate-100 pl-3">
          {node.children.map(child => (
            <Node key={child._id} node={child} depth={depth+1} expanded={expanded} setExpanded={setExpanded} onSelect={onSelect} selectedId={selectedId} onDropMove={onDropMove} />
          ))}
        </div>
      )}
    </div>
  );
};

const TaxonomyTree = ({ items = [], onSelect, onAction, selectedId }) => {
  // Type-ahead fuzzy search when the tree has focus
  const [query, setQuery] = useState('');
  useEffect(() => {
    if (!query) return;
    const t = setTimeout(() => setQuery(''), 1200);
    return () => clearTimeout(t);
  }, [query]);

  const filtered = useMemo(() => query ? filterTaxonomiesByQuery(items, query) : items, [items, query]);
  const tree = useMemo(() => buildTaxonomyTree(filtered), [filtered]);
  const [expanded, setExpanded] = useState({});
  const [menu, setMenu] = useState(null); // {x,y,node}

  // auto-expand roots and categories to show subcategories by default
  React.useEffect(() => {
    const next = {};
    const expandNode = (node) => {
      // Expand industry and category levels by default
      if (node.level === 'industry' || node.level === 'category') {
        next[String(node._id)] = true;
      }
      // Recursively expand children
      if (node.children) {
        node.children.forEach(expandNode);
      }
    };
    tree.forEach(expandNode);
    setExpanded(next);
  }, [tree]);

  const closeMenu = () => setMenu(null);
  React.useEffect(() => {
    const onDoc = () => closeMenu();
    document.addEventListener('click', onDoc);
    document.addEventListener('contextmenu', onDoc);
    return () => {
      document.removeEventListener('click', onDoc);
      document.removeEventListener('contextmenu', onDoc);
    };
  }, []);

  const onKeyDown = (e) => {
    const k = e.key;
    if (k === 'Escape') { setQuery(''); return; }
    if (k === 'Backspace') { setQuery(q => q.slice(0, -1)); return; }
    if (k.length === 1 && /[\w\s\-]/.test(k)) {
      setQuery(q => (q + k).slice(-40));
    }
  };

  return (
    <div className="relative rounded-md border border-slate-200 bg-white" tabIndex={0} onKeyDown={onKeyDown}>
      <div className="p-3" onContextMenu={(e)=>e.preventDefault()}>
        {tree.length === 0 ? (
          <div className="text-sm text-slate-500">No taxonomies found.</div>
        ) : (
          tree.map(node => (
            <div key={node._id} onContextMenu={(e)=>{ e.preventDefault(); setMenu({ x:e.clientX, y:e.clientY, node }); }}>
              <Node node={node} expanded={expanded} setExpanded={setExpanded} onSelect={onSelect} selectedId={selectedId} onDropMove={(dragId, targetId)=> onAction?.('drop-move', { dragId, targetId })} />
            </div>
          ))
        )}
      </div>
      {query && (
        <div className="absolute top-1 right-1 text-[11px] px-2 py-1 rounded-md bg-slate-100 border border-slate-200 text-slate-700">
          {query}
        </div>
      )}
      {menu && (
        <div
          className="absolute z-20 bg-white border border-slate-200 rounded-md shadow-md text-sm"
          style={{ top: menu.y, left: menu.x }}
          onClick={(e)=>e.stopPropagation()}
        >
          {[
            { key:'view', label:'View Details' },
            { key:'add-child', label:'Add Child' },
            { key:'add-sibling', label:'Add Sibling' },
            { key:'rename', label:'Rename' },
            { key:'move', label:'Move' },
            { key:'delete', label:'Delete', danger:true }
          ].map(i => (
            <button
              key={i.key}
              className={`block w-full text-left px-3 py-2 hover:bg-slate-50 ${i.danger ? 'text-red-600' : 'text-slate-700'}`}
              onClick={() => { onAction?.(i.key, menu.node); closeMenu(); }}
            >
              {i.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default TaxonomyTree;
