import React, { forwardRef } from 'react';
import { CheckCircle2 } from 'lucide-react';

// Reusable selectable card button with optional badge
const OptionCard = forwardRef(function OptionCard(
  { title, description, selected, onClick, badge, icon: Icon, variant = 'default', className = '', ...rest },
  ref
) {
  const variantClasses = {
    category: {
      base: 'hover:border-emerald-300',
      stripe: 'bg-emerald-200',
      iconWrap: 'bg-emerald-50 text-emerald-700 border-emerald-100',
      selected: 'border-emerald-500 ring-2 ring-emerald-200 bg-emerald-50'
    },
    subcategory: {
      base: 'hover:border-sky-300',
      stripe: 'bg-sky-200',
      iconWrap: 'bg-sky-50 text-sky-700 border-sky-100',
      selected: 'border-sky-500 ring-2 ring-sky-200 bg-sky-50'
    },
    default: {
      base: 'hover:border-slate-300',
      stripe: 'bg-slate-200',
      iconWrap: 'bg-slate-50 text-slate-700 border-slate-100',
      selected: 'border-slate-500 ring-2 ring-slate-200 bg-slate-50'
    }
  }[variant] || {};

  return (
    <button
      type="button"
      ref={ref}
      onClick={onClick}
      aria-pressed={selected}
      data-option
      tabIndex={0}
      className={[
        'relative w-full text-left rounded-xl border p-5 transition outline-none',
        // Stronger base shadow to emphasize right & bottom edges
        'bg-white shadow hover:shadow-md focus-visible:ring-2 focus-visible:ring-emerald-400 hover:scale-[1.01]',
        selected ? (variantClasses.selected || 'border-emerald-500 ring-2 ring-emerald-200 bg-emerald-50') : `border-slate-300 ${variantClasses.base || 'hover:border-slate-400'}`,
        className
      ].join(' ')}
      {...rest}
    >
      {/* Accent stripe */}
      <span className={`absolute inset-x-0 top-0 h-1 rounded-t-xl ${variantClasses.stripe || 'bg-slate-200'}`} />

      {/* Selected marker */}
      {selected && (
        <span className="absolute top-2 right-2 text-emerald-600">
          <CheckCircle2 className="w-5 h-5" aria-hidden="true" />
        </span>
      )}

      {Icon && (
        <div className={`mb-2 text-emerald-600 inline-flex items-center justify-center h-8 w-8 rounded-md border ${variantClasses.iconWrap || 'bg-slate-50 text-slate-700 border-slate-100'}`}>
          <Icon className="w-6 h-6" aria-hidden="true" />
        </div>
      )}
      {badge && (
        <span className="absolute top-2 right-2 inline-flex items-center rounded-full bg-slate-100 text-slate-700 px-2 py-0.5 text-xs font-medium">
          {badge}
        </span>
      )}
      <h3 className={`text-base font-semibold ${selected ? 'text-emerald-900' : 'text-slate-900'}`}>{title}</h3>
      {description && (
        <p className="mt-1 text-sm leading-6 text-slate-500">{description}</p>
      )}
    </button>
  );
});

export default OptionCard;
