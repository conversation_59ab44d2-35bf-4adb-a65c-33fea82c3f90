import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  Home, 
  Beaker, 
  FolderOpen, 
  Settings, 
  User, 
  LogOut,
  Plus,
  Search,
  Bell,
  Menu,
  X,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useApp } from '../context/AppContext';
import Logo from './Logo';

const Layout = ({ children }) => {
  const { user, logout, apiRequest } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [projectCount, setProjectCount] = useState(0);
  const [formulationCount, setFormulationCount] = useState(0);
  const [isLoadingCounts, setIsLoadingCounts] = useState(true);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    if (user) {
      fetchProjectCounts();
    }
  }, [user]); // Only refresh when user changes

  const fetchProjectCounts = async () => {
    try {
      setIsLoadingCounts(true);
      const response = await apiRequest('/projects?limit=1000');
      if (response.success) {
        const projects = response.data;
        setProjectCount(projects.length);
        setFormulationCount(projects.filter(p => p.current_formulation).length);
      }
    } catch (error) {
      console.error('Error fetching project counts:', error);
    } finally {
      setIsLoadingCounts(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleNewProject = () => {
    // Navigate to dashboard and trigger new project modal
    navigate('/dashboard');
    // You might want to pass a state or use context to trigger the modal
  };

  const isAdmin = location.pathname.startsWith('/admin');
  const [adminOpen, setAdminOpen] = useState(isAdmin);

  const sidebarItems = [
    { 
      icon: Home, 
      label: 'Dashboard', 
      path: '/dashboard',
      active: location.pathname === '/dashboard'
    },
    { 
      icon: FolderOpen, 
      label: 'Projects', 
      count: isLoadingCounts ? '...' : projectCount,
      path: '/projects',
      active: location.pathname === '/projects'
    },
    { 
      icon: Settings, 
      label: 'Settings', 
      path: '/settings',
      active: location.pathname === '/settings'
    },
  ];

  const AdminGroup = () => (
    <li>
      <button
        onClick={() => setAdminOpen(!adminOpen)}
        className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
          isAdmin ? 'bg-green-50 text-green-700 border-l-4 border-green-500' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
        }`}
      >
        <Settings className="w-5 h-5 mr-3" />
        <span className="font-medium flex-1">Administration</span>
        {adminOpen ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
      </button>
      {adminOpen && (
        <ul className="mt-1 ml-8 space-y-1">
          <li>
            <button onClick={()=>navigate('/admin/config')} className={`w-full text-left px-3 py-2 rounded-md ${location.pathname === '/admin/config' ? 'bg-green-50 text-green-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`}>Config</button>
          </li>
          <li>
            <button onClick={()=>navigate('/admin/parameters')} className={`w-full text-left px-3 py-2 rounded-md ${location.pathname === '/admin/parameters' ? 'bg-green-50 text-green-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`}>Parameters</button>
          </li>
        </ul>
      )}
    </li>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex w-64 bg-white border border-slate-300 shadow-xl hover:shadow-2xl transition-all duration-200 flex-col h-screen fixed left-0 top-0 z-10">
        {/* Logo */}
        <div className="p-6 border-b border-gray-200">
          <Logo className="h-8" />
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 overflow-y-auto">
          <ul className="space-y-2">
            {sidebarItems.map((item, index) => (
              <li key={index}>
                <button 
                  onClick={() => item.path && navigate(item.path)}
                  className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                    item.active 
                      ? 'bg-green-50 text-green-700 border-l-4 border-green-500' 
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}>
                  <item.icon className="w-5 h-5 mr-3" />
                  <span className="font-medium">{item.label}</span>
                  {item.count !== undefined && (
                    <span className="ml-auto bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                      {item.count}
                    </span>
                  )}
                </button>
              </li>
            ))}
            <AdminGroup />
          </ul>
        </nav>

        {/* User Profile - Sticky Footer */}
        <div className="p-4 border-t border-gray-200 bg-gray-50 flex-shrink-0">
          <div className="flex items-center mb-3">
            <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
              {user?.first_name?.[0] || user?.email?.[0]?.toUpperCase()}
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium text-gray-900">
                {user?.first_name ? `${user.first_name} ${user.last_name || ''}`.trim() : user?.email?.split('@')[0]}
              </p>
              <p className="text-xs text-gray-500">{user?.email}</p>
            </div>
          </div>
          <button 
            onClick={handleLogout}
            className="w-full flex items-center justify-center px-3 py-2 text-sm text-white bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors font-medium"
          >
            <LogOut className="w-4 h-4 mr-2" />
            Sign Out
          </button>
        </div>
      </div>

      {/* Mobile Header */}
      <div className="lg:hidden fixed top-0 left-0 right-0 bg-white shadow-sm z-20">
        <div className="flex items-center justify-between px-4 py-3">
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2 rounded-lg hover:bg-gray-100"
          >
            {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
          <Logo className="h-6" />
          <button 
            onClick={handleNewProject}
            className="p-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            <Plus className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {isMobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-30" onClick={() => setIsMobileMenuOpen(false)}>
          <div 
            className="w-64 bg-white h-full border border-slate-300 shadow-xl hover:shadow-2xl transition-all duration-200 flex flex-col"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Mobile Logo */}
            <div className="p-6 border-b border-gray-200">
              <Logo className="h-8" />
            </div>

            {/* Mobile Navigation */}
            <nav className="flex-1 p-4 overflow-y-auto">
              <ul className="space-y-2">
                {sidebarItems.map((item, index) => (
                  <li key={index}>
                    <button 
                      onClick={() => {
                        item.path && navigate(item.path);
                        setIsMobileMenuOpen(false);
                      }}
                      className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                        item.active 
                          ? 'bg-green-50 text-green-700 border-l-4 border-green-500' 
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}>
                      <item.icon className="w-5 h-5 mr-3" />
                      <span className="font-medium">{item.label}</span>
                      {item.count !== undefined && (
                        <span className="ml-auto bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                          {item.count}
                        </span>
                      )}
                    </button>
                  </li>
                ))}
                {/* Admin group in mobile */}
                <li>
                  <button
                    onClick={() => setAdminOpen(!adminOpen)}
                    className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                      isAdmin ? 'bg-green-50 text-green-700 border-l-4 border-green-500' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Settings className="w-5 h-5 mr-3" />
                    <span className="font-medium flex-1">Administration</span>
                    {adminOpen ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                  </button>
                  {adminOpen && (
                    <ul className="mt-1 ml-8 space-y-1">
                      <li>
                        <button onClick={()=>{ navigate('/admin/config'); setIsMobileMenuOpen(false); }} className={`w-full text-left px-3 py-2 rounded-md ${location.pathname === '/admin/config' ? 'bg-green-50 text-green-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`}>Config</button>
                      </li>
                      <li>
                        <button onClick={()=>{ navigate('/admin/parameters'); setIsMobileMenuOpen(false); }} className={`w-full text-left px-3 py-2 rounded-md ${location.pathname === '/admin/parameters' ? 'bg-green-50 text-green-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`}>Parameters</button>
                      </li>
                    </ul>
                  )}
                </li>
              </ul>
            </nav>

            {/* Mobile User Profile */}
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center mb-3">
                <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {user?.first_name?.[0] || user?.email?.[0]?.toUpperCase()}
                </div>
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {user?.first_name ? `${user.first_name} ${user.last_name || ''}`.trim() : user?.email?.split('@')[0]}
                  </p>
                  <p className="text-xs text-gray-500">{user?.email}</p>
                </div>
              </div>
              <button 
                onClick={handleLogout}
                className="w-full flex items-center justify-center px-3 py-2 text-sm text-white bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors font-medium"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col lg:ml-64">
        {/* Header removed for all pages to reduce vertical chrome */}

        {/* Page Content */}
        <main className={`flex-1 overflow-auto pt-4`}>
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;
