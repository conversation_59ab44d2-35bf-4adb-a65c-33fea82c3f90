import React, { useState, useEffect } from 'react';
import { Info, AlertCircle } from 'lucide-react';

/**
 * Dynamic Form Field Component
 * Renders different input types based on widget configuration from MongoDB
 */
const DynamicFormField = ({ 
  field, 
  value, 
  onChange, 
  error, 
  disabled = false,
  className = "" 
}) => {
  const initial = value ?? field.default_value ?? field.initial_value ?? '';
  const [localValue, setLocalValue] = useState(initial);
  const [touched, setTouched] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  useEffect(() => {
    if (value !== undefined && value !== localValue) {
      setLocalValue(value);
    }
  }, [value]);

  const isEqual = (a, b) => {
    if (Array.isArray(a) || Array.isArray(b)) return JSON.stringify(a) === JSON.stringify(b);
    return a === b;
  };

  const handleChange = (newValue) => {
    setLocalValue(newValue);
    if (!touched && !isEqual(newValue, initial)) setTouched(true);
    if (onChange) {
      onChange(field.key, newValue);
    }
  };

  const renderField = () => {
    const widgetType = field.widget?.type || 'text';

    switch (widgetType) {
      case 'text':
        case 'email':
        case 'tel':
          return (
            <input
            type={widgetType}
            id={field.key}
            value={localValue}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={field.widget?.placeholder || ''}
            disabled={disabled}
            min={field.widget?.min}
            max={field.widget?.max}
            step={field.widget?.step}
            className={`w-full h-10 px-3 border rounded-md focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 ${
              error ? 'border-red-400' : 'border-slate-200'
            } ${disabled ? 'bg-slate-50 cursor-not-allowed' : 'bg-white'}`}
          />
        );

      case 'number':
        return (
          <div className="relative">
            <input
              type="number"
              id={field.key}
              value={localValue}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={field.widget?.placeholder || ''}
              disabled={disabled}
              min={field.widget?.min}
              max={field.widget?.max}
              step={field.widget?.step}
              className={`w-full h-10 pr-14 pl-3 border rounded-md focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 ${
                error ? 'border-red-400' : 'border-slate-200'
              } ${disabled ? 'bg-slate-50 cursor-not-allowed' : 'bg-white'}`}
            />
            <span className="absolute top-1/2 -translate-y-1/2 right-0 inline-flex items-center h-6 px-2 rounded-md bg-slate-100 text-slate-700 border border-slate-200 text-[11px] mr-1.5">
              {(() => {
                const unit = field.guidance?.unit;
                return unit ? `${localValue || ''} ${unit}` : `${localValue || ''}`;
              })()}
            </span>
          </div>
        );

      case 'textarea':
        return (
          <textarea
            id={field.key}
            value={localValue}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={field.widget?.placeholder || ''}
            disabled={disabled}
            rows={field.widget?.rows || 3}
            className={`w-full px-3 py-2 border rounded-md focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 ${
              error ? 'border-red-400' : 'border-slate-200'
            } ${disabled ? 'bg-slate-50 cursor-not-allowed' : 'bg-white'}`}
          />
        );

      case 'select':
      case 'dropdown':
        return (
          <select
            id={field.key}
            value={localValue}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            className={`w-full h-10 px-3 border rounded-md focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 ${
              error ? 'border-red-400' : 'border-slate-200'
            } ${disabled ? 'bg-slate-50 cursor-not-allowed' : 'bg-white'}`}
          >
            <option value="">Select...</option>
            {field.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'radio':
        return (
          <div className="space-y-2">
            {field.options?.map(option => (
              <label key={option.value} className="flex items-center space-x-2">
                <input
                  type="radio"
                  name={field.key}
                  value={option.value}
                  checked={localValue === option.value}
                  onChange={(e) => handleChange(e.target.value)}
                  disabled={disabled}
                  className="text-emerald-600 focus:ring-emerald-500"
                />
                <span className={disabled ? 'text-gray-500' : ''}>
                  {option.label}
                </span>
              </label>
            ))}
          </div>
        );

      case 'checkbox':
        return field.options ? (
          // Multiple checkboxes
          <div className="space-y-2">
            {field.options.map(option => (
              <label key={option.value} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  value={option.value}
                  checked={Array.isArray(localValue) && localValue.includes(option.value)}
                  onChange={(e) => {
                    const currentValues = Array.isArray(localValue) ? localValue : [];
                    const newValues = e.target.checked
                      ? [...currentValues, option.value]
                      : currentValues.filter(v => v !== option.value);
                    handleChange(newValues);
                  }}
                  disabled={disabled}
                  className="text-green-600 focus:ring-green-500"
                />
                <span className={disabled ? 'text-gray-500' : ''}>
                  {option.label}
                </span>
              </label>
            ))}
          </div>
        ) : (
          // Single checkbox (boolean)
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={localValue === true || localValue === 'true'}
              onChange={(e) => handleChange(e.target.checked)}
              disabled={disabled}
              className="text-emerald-600 focus:ring-emerald-500"
            />
            <span className={disabled ? 'text-gray-500' : ''}>
              {field.label}
            </span>
          </label>
        );

      case 'toggle':
      case 'switch':
        return (
          <button
            type="button"
            onClick={() => handleChange(!localValue)}
            disabled={disabled}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              localValue ? 'bg-emerald-600' : 'bg-slate-200'
            } ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                localValue ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        );

      case 'slider':
      case 'range':
        return (
          <div className="space-y-1">
            <div className="relative">
              <input
                type="range"
                id={field.key}
                value={localValue}
                onChange={(e) => handleChange(e.target.value)}
                disabled={disabled}
                min={field.widget?.min || 0}
                max={field.widget?.max || 100}
                step={field.widget?.step || 1}
                className="w-full accent-emerald-600 pr-14"
              />
              <span className="absolute top-1/2 -translate-y-1/2 right-0 inline-flex items-center h-5 px-1.5 rounded-md bg-slate-100 text-slate-700 border border-slate-200 text-[11px]">
                {(() => {
                  const unit = field.guidance?.unit;
                  return unit ? `${localValue} ${unit}` : `${localValue}`;
                })()}
              </span>
            </div>
            <div className="flex justify-between text-[11px] text-slate-500">
              {(() => {
                const unit = field.guidance?.unit;
                const min = field.widget?.min ?? 0;
                const max = field.widget?.max ?? 100;
                return (
                  <>
                    <span>{unit ? `${min} ${unit}` : min}</span>
                    <span>{unit ? `${max} ${unit}` : max}</span>
                  </>
                );
              })()}
            </div>
          </div>
        );

      case 'multiselect':
        return (
          <div className="space-y-2 max-h-48 overflow-y-auto border rounded-lg p-2">
            {field.options?.map(option => (
              <label key={option.value} className="flex items-center space-x-2 p-1">
                <input
                  type="checkbox"
                  value={option.value}
                  checked={Array.isArray(localValue) && localValue.includes(option.value)}
                  onChange={(e) => {
                    const currentValues = Array.isArray(localValue) ? localValue : [];
                    const newValues = e.target.checked
                      ? [...currentValues, option.value]
                      : currentValues.filter(v => v !== option.value);
                    handleChange(newValues);
                  }}
                  disabled={disabled}
                  className="text-emerald-600 focus:ring-emerald-500"
                />
                <span className={disabled ? 'text-gray-500' : ''}>
                  {option.label}
                </span>
              </label>
            ))}
          </div>
        );

      case 'tags':
        return (
          <div className="space-y-2">
            <div className="flex flex-wrap gap-2">
              {Array.isArray(localValue) && localValue.map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-green-100 text-green-700 rounded-full text-sm flex items-center gap-1"
                >
                  {tag}
                  {!disabled && (
                    <button
                      type="button"
                      onClick={() => {
                        const newTags = localValue.filter((_, i) => i !== index);
                        handleChange(newTags);
                      }}
                      className="text-green-600 hover:text-green-800"
                    >
                      ×
                    </button>
                  )}
                </span>
              ))}
            </div>
            {!disabled && (
              <input
                type="text"
                placeholder="Type and press Enter to add tag"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    const newTag = e.target.value.trim();
                    if (newTag) {
                      const currentTags = Array.isArray(localValue) ? localValue : [];
                      handleChange([...currentTags, newTag]);
                      e.target.value = '';
                    }
                  }
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              />
            )}
          </div>
        );

      default:
        return (
          <input
            type="text"
            id={field.key}
            value={localValue}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={field.widget?.placeholder || ''}
            disabled={disabled}
            className={`w-full h-10 px-3 border rounded-md focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 ${
              error ? 'border-red-400' : 'border-slate-200'
            } ${disabled ? 'bg-slate-50 cursor-not-allowed' : 'bg-white'}`}
          />
        );
    }
  };

  return (
    <div className={`space-y-1.5 ${className}`}>
      {/* Label with optional tooltip */}
      {field.widget?.type !== 'checkbox' && (
        <label htmlFor={field.key} className="block">
          <div className="flex items-center gap-2">
            <span className="text-sm font-semibold text-slate-900">
              {field.label}
              {field.required && <span className="text-red-500">*</span>}
            </span>
            {/* Default badge removed per spec */}
            {field.widget?.help_text && (
              <div className="relative">
                <Info
                  className="w-4 h-4 text-slate-400 cursor-help"
                  onMouseEnter={() => setShowTooltip(true)}
                  onMouseLeave={() => setShowTooltip(false)}
                />
                {showTooltip && (
                  <div className="absolute z-10 w-64 p-2 text-xs md:text-sm text-slate-600 bg-white border border-slate-200 rounded-md shadow -top-2 left-6">
                    {field.widget?.help_text}
                  </div>
                )}
              </div>
            )}
          </div>
        </label>
      )}

      {/* Field */}
      {renderField()}

      {/* Error message */}
      {error && (
        <div className="flex items-center gap-1 text-red-600 text-sm">
          <AlertCircle className="w-4 h-4" />
          <span>{error}</span>
        </div>
      )}

      {/* Helper text */}
      {field.widget?.helper_text && !error && (
        <p className="text-xs text-slate-500">{field.widget.helper_text}</p>
      )}
    </div>
  );
};

export default DynamicFormField;
