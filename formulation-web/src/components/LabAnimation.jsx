import React from 'react';
import { Atom, Loader2 } from 'lucide-react';

// theme: 'emerald' | 'blue' | 'purple'
export default function LabAnimation({ className = '', size = 128, bubbleCount = 5, showSpinner = false, theme = 'emerald' }) {
  const boxStyle = { height: `${size}px`, width: `${size}px` };

  const palettes = {
    emerald: {
      atom: 'text-emerald-300',
      border: 'border-emerald-400',
      bg: 'bg-emerald-50/70',
      bubble: 'bg-emerald-500',
      spinner: 'text-emerald-600'
    },
    blue: {
      atom: 'text-sky-300',
      border: 'border-sky-400',
      bg: 'bg-sky-50/70',
      bubble: 'bg-sky-500',
      spinner: 'text-sky-600'
    },
    purple: {
      atom: 'text-purple-300',
      border: 'border-purple-400',
      bg: 'bg-purple-50/70',
      bubble: 'bg-purple-500',
      spinner: 'text-purple-600'
    },
    red: {
      atom: 'text-red-300',
      border: 'border-red-400',
      bg: 'bg-red-50/70',
      bubble: 'bg-red-500',
      spinner: 'text-red-600'
    }
  };

  const c = palettes[theme] || palettes.emerald;

  return (
    <div className={`relative mx-auto ${className}`} style={boxStyle}>
      {/* Orbiting atom */}
      <div className="absolute inset-0 flex items-center justify-center chem-orbit">
        <Atom className={`w-20 h-20 ${c.atom}`} />
      </div>
      {/* Flask */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div
          className={`relative h-16 w-14 rounded-b-xl border-2 ${c.border} ${c.bg} chem-glow`}
          style={{ clipPath: 'polygon(20% 0, 80% 0, 80% 20%, 90% 30%, 90% 95%, 10% 95%, 10% 30%, 20% 20%)' }}
        />
      </div>
      {/* Bubbles */}
      {Array.from({ length: bubbleCount }).map((_, i) => (
        <span
          key={i}
          className={`absolute bottom-6 left-1/2 h-1.5 w-1.5 rounded-full ${c.bubble} chem-bubble`}
          style={{ transform: `translateX(${(-16 + i * 8)}px)`, animationDelay: `${i * 0.25}s` }}
        />
      ))}
      {/* Inner spinner */}
      {showSpinner && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Loader2 className={`w-6 h-6 ${c.spinner} animate-spin`} />
        </div>
      )}
    </div>
  );
}
