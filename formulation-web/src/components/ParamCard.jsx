import React from 'react';

// Lightweight card wrapper for parameter fields
export default function ParamCard({ children, required = false, className = '' }) {
  return (
    <div
      className={[
        'relative rounded-xl border bg-white p-4 shadow',
        'hover:shadow-md transition',
        'border-slate-200',
        className
      ].join(' ')}
    >
      <span className="absolute inset-x-0 top-0 h-1 rounded-t-xl bg-slate-200" />
      {required && (
        <span className="absolute top-2 right-2 inline-flex items-center rounded-full bg-emerald-50 text-emerald-700 px-2 py-0.5 text-[10px] font-medium border border-emerald-100">
          Required
        </span>
      )}
      {children}
    </div>
  );
}

