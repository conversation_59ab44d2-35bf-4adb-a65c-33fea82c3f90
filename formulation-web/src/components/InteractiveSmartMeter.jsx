import React from 'react';
import { getInteractiveColorClasses } from '../utils/colorUtils';

const InteractiveSmartMeter = ({ 
  label, 
  value, 
  color = "green", 
  icon: Icon, 
  onChange, 
  min = 0, 
  max = 100, 
  suffix = "" 
}) => {
  const colors = getInteractiveColorClasses(color);

  return (
    <div className={`p-4 rounded-xl border-2 ${colors} transition-all duration-300 hover:shadow-lg`}>
      <div className="flex items-center justify-between mb-3">
        <span className="text-sm font-medium">{label}</span>
        {Icon && <Icon className="w-4 h-4" />}
      </div>
      
      <div className="text-3xl font-bold mb-2">{value}{suffix}</div>
      
      <div className="w-full bg-white rounded-full h-3 mb-3 overflow-hidden">
        <div 
          className={`h-3 rounded-full transition-all duration-500 ${
            color === 'green' ? 'bg-gradient-to-r from-green-400 to-green-600' : 
            color === 'blue' ? 'bg-gradient-to-r from-blue-400 to-blue-600' : 
            color === 'purple' ? 'bg-gradient-to-r from-purple-400 to-purple-600' : 
            color === 'orange' ? 'bg-gradient-to-r from-orange-400 to-orange-600' :
            'bg-gradient-to-r from-red-400 to-red-600'
          }`}
          style={{ width: `${Math.min(((value - min) / (max - min)) * 100, 100)}%` }}
        ></div>
      </div>
      
      {onChange && (
        <input
          type="range"
          min={min}
          max={max}
          value={value}
          onChange={(e) => onChange(parseInt(e.target.value))}
          className={`w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:${colors.split(' ')[3]}`}
        />
      )}
    </div>
  );
};

export default InteractiveSmartMeter;