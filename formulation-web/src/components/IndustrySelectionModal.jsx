import React, { useState } from 'react';
import { ChevronRight, Droplets, Pill, Leaf, Check, X } from 'lucide-react';
import { useApp } from '../context/AppContext';
import { getDetailedColorClasses } from '../utils/colorUtils';

const IndustrySelectionModal = ({ isOpen, onClose, onContinue }) => {
  const { updateFormData } = useApp();
  const [selectedIndustry, setSelectedIndustry] = useState('');

  const industries = [
    {
      id: 'beverages',
      name: 'Functional Beverages',
      icon: Droplets,
      description: 'Juices, energy drinks, wellness shots',
      color: 'blue'
    },
    {
      id: 'nutraceuticals',
      name: 'Nutraceuticals',
      icon: Pill,
      description: 'Supplements, vitamins, health formulations',
      color: 'green'
    },
    {
      id: 'cosmetics',
      name: 'Herbal Cosmetics',
      icon: Leaf,
      description: 'Natural skincare, Ayurvedic formulations',
      color: 'purple'
    }
  ];

  const handleSelect = (industryId) => {
    setSelectedIndustry(industryId);
    updateFormData('industry', industryId);
  };

  const handleContinue = () => {
    if (selectedIndustry && onContinue) {
      onContinue(selectedIndustry);
    }
  };

  const handleCancel = () => {
    setSelectedIndustry('');
    if (onClose) {
      onClose();
    }
  };

  // Use shared color utility function from colorUtils.js

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Choose Your Industry</h2>
            <p className="text-gray-600 mt-1">Select the industry that best matches your formulation needs</p>
          </div>
          <button 
            onClick={handleCancel}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Modal Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            {industries.map((industry) => {
              const colors = getDetailedColorClasses(industry.color);
              const Icon = industry.icon;
              const isSelected = selectedIndustry === industry.id;
              
              return (
                <div
                  key={industry.id}
                  onClick={() => handleSelect(industry.id)}
                  className={`p-6 rounded-xl border-2 cursor-pointer transition-all text-center ${
                    isSelected 
                      ? `${colors.selectedBorder} ${colors.selectedBg}` 
                      : `${colors.border} bg-white hover:${colors.bg}`
                  }`}
                >
                  <Icon className={`w-12 h-12 mx-auto mb-3 ${colors.text}`} />
                  <h3 className="text-lg font-semibold mb-2">{industry.name}</h3>
                  <p className="text-gray-600 text-sm mb-3">{industry.description}</p>
                  
                  {isSelected && (
                    <div className="mt-3">
                      <Check className={`w-5 h-5 mx-auto ${colors.text}`} />
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Modal Footer */}
          <div className="flex justify-between items-center pt-4 border-t border-gray-200">
            <button 
              onClick={handleCancel}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button 
              onClick={handleContinue}
              disabled={!selectedIndustry}
              className="px-8 py-3 bg-gray-900 text-white rounded-lg hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center transition-colors"
            >
              Continue <ChevronRight className="ml-2 w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IndustrySelectionModal;