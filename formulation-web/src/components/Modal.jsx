import React from 'react';

const Modal = ({ open, title, children, onClose, onSubmit, submitLabel = 'Save', destructive = false }) => {
  if (!open) return null;
  return (
    <div className="fixed inset-0 z-30 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/30" onClick={onClose} />
      <div className="relative bg-white w-full max-w-lg rounded-xl border border-slate-200 shadow-xl">
        <div className="px-5 py-3 border-b border-slate-200 flex items-center justify-between">
          <h3 className="text-base font-semibold text-slate-900">{title}</h3>
          <button className="text-slate-500 hover:text-slate-700" onClick={onClose}>✕</button>
        </div>
        <div className="p-5">
          {children}
        </div>
        <div className="px-5 py-3 border-t border-slate-200 flex items-center justify-end gap-2">
          <button className="px-3 py-2 rounded-md border border-slate-300 text-slate-700" onClick={onClose}>Cancel</button>
          <button className={`px-3 py-2 rounded-md ${destructive ? 'bg-red-600 hover:bg-red-700' : 'bg-emerald-600 hover:bg-emerald-700'} text-white`} onClick={onSubmit}>{submitLabel}</button>
        </div>
      </div>
    </div>
  );
};

export default Modal;

