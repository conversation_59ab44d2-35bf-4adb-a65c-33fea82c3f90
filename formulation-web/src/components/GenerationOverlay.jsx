import React, { useEffect, useState } from 'react';
import { Loader2, CheckCircle2, XCircle, Archive, Trash2 } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import LabAnimation from './LabAnimation';
import FailedAnimation from './FailedAnimation';
import { projectApi } from '../utils/apiUtils';

const GenerationOverlay = ({ projectId, onClose }) => {
  const { apiRequest } = useAuth();
  const [status, setStatus] = useState('queued');
  const [error, setError] = useState(null);
  const [phase, setPhase] = useState(0);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showArchiveDialog, setShowArchiveDialog] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    let timer;
    const poll = async () => {
      try {
        const resp = await projectApi.getStatus(projectId);
        const s = resp?.data?.status || 'queued';
        setStatus(s);
        setError(null);
        if (s === 'ready' || s === 'failed') return; // stop polling
        if (s === 'generating') {
          // Auto-navigate to playground when status becomes generating/in_progress
          setTimeout(() => {
            navigate(`/playground/${projectId}`);
          }, 1000);
          return;
        }
      } catch (e) {
        // Keep overlay blocking; show a soft reconnect message instead of a hard error
        setError('reconnect');
      }
      timer = setTimeout(poll, 3000);
    };
    poll();
    return () => clearTimeout(timer);
  }, [projectId, navigate]);

  // Rotate fun lab phases while waiting
  useEffect(() => {
    if (status === 'ready' || status === 'failed') return;
    const t = setInterval(() => setPhase((p) => (p + 1) % 4), 2400);
    return () => clearInterval(t);
  }, [status]);

  const goToPlayground = () => navigate(`/playground/${projectId}`);

  const handleRetry = async () => {
    try {
      await projectApi.retry(projectId);
      setStatus('queued');
    } catch (error) {
      console.error('Failed to retry project:', error);
    }
  };

  const handleArchive = async () => {
    try {
      await projectApi.archive(projectId);
      setShowArchiveDialog(false);
      navigate('/dashboard');
    } catch (error) {
      console.error('Failed to archive project:', error);
    }
  };

  const handleDelete = async () => {
    try {
      await projectApi.delete(projectId);
      setShowDeleteDialog(false);
      navigate('/dashboard');
    } catch (error) {
      console.error('Failed to delete project:', error);
    }
  };

  const blocking = status === 'queued';

  return (
    <div className="fixed inset-0 z-[80] flex items-center justify-center">
      <div className="absolute inset-0 bg-black/55 backdrop-blur-sm" onClick={blocking ? undefined : onClose} />
      <div className="relative w-full max-w-lg bg-white/85 backdrop-blur-md rounded-2xl border border-white/30 shadow-2xl p-6 pt-8 text-center">

        {blocking ? (
          <>
            <h3 className="text-xl font-semibold text-slate-900 mb-4">Brewing your formulation…</h3>
            <div className="flex flex-col items-center space-y-4">
              <LabAnimation className="" size={84} />
              <div className="text-slate-600 text-sm text-center">
                {
                  [
                    'Balancing pH and prepping phases',
                    'Calibrating scales and warming vessels',
                    'Mixing the base and checking viscosity',
                    'Filling jars and tidying the lab bench'
                  ][phase]
                }
              </div>
              <div className="text-xs text-slate-600 text-center">This may take a minute. We'll open it the moment it's ready.</div>
              {error && (
                <div className="text-xs text-slate-500">Reconnecting to the lab…</div>
              )}
              <div className="pt-2">
                <button
                  className="px-4 py-2 rounded-lg border border-slate-300 text-slate-700 hover:bg-slate-50"
                  onClick={() => navigate('/dashboard')}
                >
                  Back to Dashboard
                </button>
              </div>
            </div>
          </>
        ) : status === 'ready' ? (
          <>
            <CheckCircle2 className="w-12 h-12 text-emerald-600 mx-auto mb-2" />
            <h3 className="text-xl font-semibold text-slate-900 mb-1">Project is ready</h3>
            <p className="text-slate-600 mb-4">Open the playground to explore your formulation.</p>
            <button onClick={goToPlayground} className="px-5 py-2.5 rounded-lg bg-emerald-600 text-white hover:bg-emerald-700">Open Playground</button>
          </>
        ) : status === 'failed' ? (
          <>
            <div className="flex flex-col items-center space-y-4">
              <div className="mb-2">
                <FailedAnimation size={64} />
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold text-slate-900 mb-2">Generation failed</h3>
                <p className="text-slate-600 text-sm mb-6">The formulation process encountered an error. You can retry the generation, archive this project for later review, or delete it permanently.</p>
              </div>
              
              <div className="flex gap-2 justify-center pt-2">
                <button
                  onClick={() => navigate('/dashboard')}
                  className="flex items-center gap-2 px-4 py-2.5 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors font-medium"
                >
                  Cancel
                </button>
                <button
                  onClick={handleRetry}
                  className="flex items-center gap-2 px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  <Loader2 className="w-4 h-4" />
                  Retry
                </button>
                <button
                  onClick={() => setShowArchiveDialog(true)}
                  className="flex items-center gap-2 px-4 py-2.5 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors font-medium"
                >
                  <Archive className="w-4 h-4" />
                  Archive
                </button>
                <button
                  onClick={() => setShowDeleteDialog(true)}
                  className="flex items-center gap-2 px-4 py-2.5 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors font-medium"
                >
                  <Trash2 className="w-4 h-4" />
                  Delete
                </button>
              </div>
            </div>
          </>
        ) : status === 'generating' ? (
          <>
            <h3 className="text-xl font-semibold text-slate-900 mb-4">Finalizing your formulation…</h3>
            <div className="flex flex-col items-center space-y-4">
              <LabAnimation className="" size={84} />
              <div className="text-slate-600 text-sm text-center">
                {
                  [
                    'Balancing pH and prepping phases',
                    'Calibrating scales and warming vessels',
                    'Mixing the base and checking viscosity',
                    'Filling jars and tidying the lab bench'
                  ][phase]
                }
              </div>
              <div className="text-xs text-slate-600 text-center">Opening playground in a moment...</div>
              <div className="pt-2">
                <button
                  className="px-4 py-2 rounded-lg border border-slate-300 text-slate-700 hover:bg-slate-50"
                  onClick={() => navigate('/dashboard')}
                >
                  Back to Dashboard
                </button>
              </div>
            </div>
           </>
        ) : null}
      </div>
      
      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && (
        <div className="fixed inset-0 z-[90] flex items-center justify-center">
          <div className="absolute inset-0 bg-black/60 backdrop-blur-sm" onClick={() => setShowDeleteDialog(false)} />
          <div className="relative bg-white rounded-2xl border shadow-2xl p-6 max-w-md mx-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                <Trash2 className="w-5 h-5 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-slate-900">Delete Project</h3>
            </div>
            <p className="text-slate-600 mb-6">
              Are you sure you want to delete this project? This action cannot be undone and will permanently remove all project data.
            </p>
            <div className="flex gap-3 justify-end">
              <button
                onClick={() => setShowDeleteDialog(false)}
                className="px-4 py-2 text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Archive Confirmation Dialog */}
      {showArchiveDialog && (
        <div className="fixed inset-0 z-[90] flex items-center justify-center">
          <div className="absolute inset-0 bg-black/60 backdrop-blur-sm" onClick={() => setShowArchiveDialog(false)} />
          <div className="relative bg-white rounded-2xl border shadow-2xl p-6 max-w-md mx-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                <Archive className="w-5 h-5 text-gray-600" />
              </div>
              <h3 className="text-lg font-semibold text-slate-900">Archive Project</h3>
            </div>
            <p className="text-slate-600 mb-6">
              Archive this project? You can restore it later from your archived projects.
            </p>
            <div className="flex gap-3 justify-end">
              <button
                onClick={() => setShowArchiveDialog(false)}
                className="px-4 py-2 text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleArchive}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Archive
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GenerationOverlay;
