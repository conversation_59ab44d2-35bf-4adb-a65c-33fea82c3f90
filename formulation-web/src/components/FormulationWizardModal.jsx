import React, { useState, useRef, useEffect } from 'react';
import { 
  ChevronRight, ChevronLeft, Droplets, Pill, Leaf, Check, X, Target, DollarSign, Shield, Zap,
  Coffee, Zap as Lightning, Sparkles, FlaskConical, Heart, Dumbbell, Eye, Flower2,
  Waves, Paintbrush, Scissors, Shirt, Globe, Gauge, Leaf as Eco, TrendingUp, Clock,
  Palette, Layers, Package, Users, BarChart3, Lightbulb
} from 'lucide-react';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import { getDetailedColorClasses } from '../utils/colorUtils';

const FormulationWizardModal = ({ isOpen, onClose, onComplete, isGenerating = false, preSelectedIndustry = '', preSelectedProductType = '', startStep = 0, retryData = null }) => {
  const { updateFormData } = useApp();
  const { apiRequest, user, isAuthenticated } = useAuth();
  const [currentStep, setCurrentStep] = useState(retryData ? 2 : startStep); // Start at goals step (2) for retry
  const customInputRef = useRef(null);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStatus, setGenerationStatus] = useState('');
  const [generatedResults, setGeneratedResults] = useState(null);
  const [isLoadingResults, setIsLoadingResults] = useState(false);
  const [formState, setFormState] = useState({
    industry: retryData?.industry || preSelectedIndustry,
    productType: retryData?.productType || preSelectedProductType,
    productDescription: '',
    // Updated parameters to match user screenshots
    budgetPerUnit: 200, // INR per unit
    nutritionScore: 80, // percentage
    sustainability: 70, // percentage
    shelfLifeDays: 365, // days
    // Compliance checkboxes (matching screenshots)
    veganFriendly: false,
    organicCertified: false,
    lowCalorie: false,
    // Industry-specific parameters (removed for now to match screenshots)
    // Will be added back if needed for specific industries
    flavor: '',
    texture: '',
    absorption: 75,
    packaging: '',
    skinType: ''
  });

  // Reset modal state when it closes
  useEffect(() => {
    if (!isOpen) {
      setCurrentStep(0);
      setFormState({
        industry: '',
        productType: '',
        productDescription: '',
        budgetPerUnit: 200,
        nutritionScore: 80,
        sustainability: 70,
        shelfLifeDays: 365,
        veganFriendly: false,
        organicCertified: false,
        lowCalorie: false,
        flavor: '',
        texture: '',
        absorption: 75,
        packaging: '',
        skinType: ''
      });
    }
  }, [isOpen]);

  // Set retry data when modal opens for retry
  useEffect(() => {
    if (isOpen && retryData) {
      setCurrentStep(2); // Goals step
      setFormState(prev => ({
        ...prev,
        industry: retryData.industry || '',
        productType: retryData.productType || ''
      }));
    }
  }, [isOpen, retryData]);

  const steps = [
    { 
      id: 'industry', 
      label: 'Industry', 
      title: 'Choose Your Industry',
      subtitle: 'Select the industry that best matches your formulation needs',
      completed: false 
    },
    { 
      id: 'product', 
      label: 'Product Type', 
      title: 'Define Your Product',
      subtitle: 'Specify the product type and provide detailed requirements',
      completed: false 
    },
    { 
      id: 'goals', 
      label: 'Generate', 
      title: 'Goals & Requirements',
      subtitle: 'Configure your targets and generate AI formulation',
      completed: false 
    }
  ];

  const industries = [
    {
      id: 'beverages',
      name: 'Functional Beverages',
      icon: Droplets,
      description: 'Juices, energy drinks, wellness shots',
      color: 'blue'
    },
    {
      id: 'nutraceuticals',
      name: 'Nutraceuticals',
      icon: Pill,
      description: 'Supplements, vitamins, health formulations',
      color: 'green'
    },
    {
      id: 'cosmetics',
      name: 'Herbal Cosmetics',
      icon: Leaf,
      description: 'Natural skincare, Ayurvedic formulations',
      color: 'purple'
    }
  ];

  const productTypes = {
    beverages: [
      { id: 'antioxidant-juice', name: 'Antioxidant Juice', description: 'Rich in vitamins and antioxidants', icon: Sparkles, color: 'purple' },
      { id: 'energy-drink', name: 'Energy Drink', description: 'Natural energy boosters', icon: Lightning, color: 'orange' },
      { id: 'wellness-shot', name: 'Wellness Shot', description: 'Concentrated health benefits', icon: Heart, color: 'red' },
      { id: 'functional-water', name: 'Functional Water', description: 'Enhanced hydration', icon: Waves, color: 'blue' },
      { id: 'other', name: 'Other', description: 'Define your own product type', icon: FlaskConical, color: 'gray' }
    ],
    nutraceuticals: [
      { id: 'multivitamin', name: 'Multivitamin', description: 'Complete nutrition supplement', icon: Pill, color: 'green' },
      { id: 'protein-powder', name: 'Protein Powder', description: 'Muscle building and recovery', icon: Dumbbell, color: 'blue' },
      { id: 'herbal-capsule', name: 'Herbal Capsule', description: 'Natural health remedies', icon: Leaf, color: 'green' },
      { id: 'immunity-booster', name: 'Immunity Booster', description: 'Immune system support', icon: Shield, color: 'purple' },
      { id: 'other', name: 'Other', description: 'Define your own product type', icon: FlaskConical, color: 'gray' }
    ],
    cosmetics: [
      { id: 'face-serum', name: 'Face Serum', description: 'Anti-aging and hydration', icon: Eye, color: 'pink' },
      { id: 'herbal-cream', name: 'Herbal Cream', description: 'Natural moisturizing', icon: Flower2, color: 'green' },
      { id: 'ayurvedic-oil', name: 'Ayurvedic Oil', description: 'Traditional hair and skin care', icon: Droplets, color: 'yellow' },
      { id: 'natural-cleanser', name: 'Natural Cleanser', description: 'Gentle skin cleansing', icon: Sparkles, color: 'blue' },
      { id: 'other', name: 'Other', description: 'Define your own product type', icon: FlaskConical, color: 'gray' }
    ]
  };

  const updateFormState = (key, value) => {
    setFormState(prev => {
      if (key === 'productType') {
        // Clear the product description when changing product type
        return { ...prev, [key]: value, productDescription: '' };
      }
      return { ...prev, [key]: value };
    });
  };

  const toggleArrayValue = (key, value) => {
    setFormState(prev => {
      const currentArray = prev[key] || [];
      const isSelected = currentArray.includes(value);
      
      if (isSelected) {
        return { ...prev, [key]: currentArray.filter(item => item !== value) };
      } else {
        return { ...prev, [key]: [...currentArray, value] };
      }
    });
  };

  // Auto-focus and scroll to product description when any product type is selected
  useEffect(() => {
    if (formState.productType && customInputRef.current) {
      setTimeout(() => {
        customInputRef.current.focus();
        customInputRef.current.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        });
      }, 100);
    }
  }, [formState.productType]);

  // Use shared color utility function from colorUtils.js

  const handleNext = async () => {
    console.log('🔍 handleNext called - currentStep:', currentStep, 'steps.length:', steps.length);
    // If on the goals step (step 2), generate formulation
    if (currentStep === 2) {
      console.log('🎯 Starting formulation generation process...');
        // Generate formulation with Claude AI
        const finalFormData = {
          industry: formState.industry,
          productType: formState.productType,
          productDescription: formState.productDescription,
          goals: {
            budgetPerUnit: formState.budgetPerUnit,
            nutritionScore: formState.nutritionScore,
            sustainability: formState.sustainability,
            shelfLifeDays: formState.shelfLifeDays,
            // Compliance as individual boolean flags
            veganFriendly: formState.veganFriendly,
            organicCertified: formState.organicCertified,
            lowCalorie: formState.lowCalorie
          },
          industryParams: {
            // Dynamic industry-specific parameters
            ...(formState.flavor && { flavor: formState.flavor }),
            ...(formState.texture && { texture: formState.texture }),
            ...(formState.absorption && { absorption: formState.absorption }),
            ...(formState.packaging && { packaging: formState.packaging }),
            ...(formState.skinType && { skinType: formState.skinType })
          }
        };

        let progressInterval;
        try {
          setIsLoadingResults(true);
          setGenerationProgress(0);
          setGenerationStatus('Initializing Claude AI formulation generation...');
          
          // Simulate progress updates during generation
          progressInterval = setInterval(() => {
            setGenerationProgress(prev => {
              if (prev < 90) {
                // Use smaller increments to prevent overflow
                const increment = Math.random() * 8 + 2; // 2-10% increments
                return Math.min(prev + increment, 95); // Cap at 95% until completion
              }
              return Math.min(prev, 95); // Ensure we never exceed 95% during simulation
            });
          }, 2000);
          
          // Update status messages periodically
          setTimeout(() => setGenerationStatus('Analyzing product requirements...'), 2000);
          setTimeout(() => setGenerationStatus('Generating ingredient combinations...'), 8000);
          setTimeout(() => setGenerationStatus('Calculating nutritional profiles...'), 15000);
          setTimeout(() => setGenerationStatus('Validating formulation data...'), 25000);
          
          // Validate form data before sending
          console.log('🔍 Form data validation:');
          console.log('  industry:', finalFormData.industry);
          console.log('  productType:', finalFormData.productType);
          console.log('  productDescription:', finalFormData.productDescription);
          console.log('  goals:', finalFormData.goals);
          console.log('  industryParams:', finalFormData.industryParams);
          
          if (!finalFormData.industry || !finalFormData.productType) {
            throw new Error('Missing required fields: industry or productType');
          }
          
          // Debug authentication state
          console.log('🔍 Debug Authentication State:');
          console.log('  User authenticated:', !!user);
          console.log('  isAuthenticated flag:', isAuthenticated);
          console.log('  User data:', user);
          console.log('  Token available:', !!localStorage.getItem('auth_token'));
          console.log('  Token expires:', localStorage.getItem('token_expires_at'));
          
          // Check authentication before making API call
          if (!isAuthenticated || !user) {
            throw new Error('User not authenticated. Please log in again.');
          }
          
          // Call the API to generate formulation using authenticated apiRequest
          console.log('🚀 Making API request to /formulations/generate with data:', finalFormData);
          const result = await apiRequest('/formulations/generate', {
            method: 'POST',
            body: JSON.stringify({ formData: finalFormData })
          });
          console.log('✅ API Response received:', result);
          
          clearInterval(progressInterval);
          setGenerationProgress(100);

          if (result.success) {
            setGeneratedResults(result.data);
            
            // Update context with form data and results
            updateFormData('industry', finalFormData.industry);
            updateFormData('productType', finalFormData.productType);
            updateFormData('productDescription', finalFormData.productDescription);
            updateFormData('goals', finalFormData.goals);
            updateFormData('industryParams', finalFormData.industryParams);
            updateFormData('generatedFormulation', result.data);
            
            // Show success briefly, then navigate directly to results dashboard
            setGenerationStatus('🎉 Formulation complete! Navigating to results...');
            
            setTimeout(() => {
              // Reset loading state and navigate
              setIsLoadingResults(false);
              setGenerationStatus('');
              setGenerationProgress(0);
              
              if (onComplete && result.data) {
                onComplete(result.data);
              }
              onClose();
            }, 2000); // 2-second delay to show completion
          } else {
            throw new Error(result.error || 'Failed to generate formulation');
          }
        } catch (error) {
          console.error('Error generating formulation:', error);
          clearInterval(progressInterval);
          
          // Try resilient playground endpoint as fallback
          console.log('🔄 Attempting resilient generation via playground...');
          setGenerationStatus('Trying alternative generation method...');
          
          try {
            const playgroundResult = await apiRequest('/playground/generate', {
              method: 'POST',
              body: JSON.stringify({ formData: finalFormData })
            });
            
            if (playgroundResult.success) {
              console.log('✅ Playground generation successful with resilient handling');
              
              // Update context with available data
              updateFormData('industry', finalFormData.industry);
              updateFormData('productType', finalFormData.productType);
              updateFormData('productDescription', finalFormData.productDescription);
              updateFormData('goals', finalFormData.goals);
              updateFormData('industryParams', finalFormData.industryParams);
              
              // Even if there are validation errors, we have partial data
              setGenerationStatus('🎯 Moving to Product Playground for optimization...');
              setGenerationProgress(100);
              
              setTimeout(() => {
                setIsLoadingResults(false);
                setGenerationStatus('');
                setGenerationProgress(0);
                
                // Navigate to playground instead of results
                setCurrentStep('playground');
                onClose();
              }, 1500);
              
              return; // Exit successfully
              
            } else {
              throw new Error(playgroundResult.error || 'Playground generation also failed');
            }
            
          } catch (playgroundError) {
            console.error('Playground generation also failed:', playgroundError);
            setGenerationProgress(0);
            
            // Enhanced error handling with playground option
            let errorMessage = 'Unable to generate formulation with current parameters.';
            let retryAction = 'This might be due to API constraints or temporary issues.';
            
            if (error.message.includes('validation failed')) {
              errorMessage = 'Generated formulation needs optimization.';
              retryAction = 'Would you like to go to the Product Playground to manually create and optimize your formulation?';
            } else if (error.message.includes('500')) {
              errorMessage = 'AI service is temporarily experiencing high load.';
              retryAction = 'Would you like to go to the Product Playground to start manually?';
            }
            
            // Offer playground as alternative
            const userWantsPlayground = confirm(`${errorMessage}\n\n${retryAction}\n\nClick OK for Product Playground or Cancel to go back.`);
            
            if (userWantsPlayground) {
              // Navigate to playground anyway with form data
              updateFormData('industry', finalFormData.industry);
              updateFormData('productType', finalFormData.productType);
              updateFormData('productDescription', finalFormData.productDescription);
              updateFormData('goals', finalFormData.goals);
              
              setIsLoadingResults(false);
              setGenerationStatus('');
              setGenerationProgress(0);
              
              // Close modal and navigate to playground
              onClose();
              
              // Navigate to product playground page
              setTimeout(() => {
                window.location.href = '/product-playground';
              }, 100);
            } else {
              // Reset loading state
              setIsLoadingResults(false);
              setGenerationStatus('');
              setGenerationProgress(0);
            }
          }
        } finally {
          if (progressInterval) {
            clearInterval(progressInterval);
          }
          // Note: Loading state is reset in success timeout or error handling
        }
    } else if (currentStep < steps.length - 1) {
      // Normal step progression for steps 0 and 1
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleCancel = () => {
    setCurrentStep(0);
    setFormState({
      industry: '',
      productType: '',
      productDescription: '',
      // Reset to default values matching new structure
      budgetPerUnit: 200,
      nutritionScore: 80,
      sustainability: 70,
      shelfLifeDays: 365,
      // Compliance checkboxes
      veganFriendly: false,
      organicCertified: false,
      lowCalorie: false,
      // Industry-specific parameters
      flavor: '',
      texture: '',
      absorption: 75,
      packaging: '',
      skinType: ''
    });
    if (onClose) {
      onClose();
    }
  };

  const canProceed = () => {
    switch(currentStep) {
      case 0: return formState.industry !== '';
      case 1: return formState.productType !== '' && formState.productDescription.trim().length >= 50;
      case 2: return formState.budgetPerUnit > 0; // Goals step - ready to generate formulation
      default: return false;
    }
  };

  const renderStepContent = () => {
    switch(currentStep) {
      case 0:
        return (
          <div className="space-y-8">
            {/* Industry Selection Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {industries.map((industry) => {
                const colors = getDetailedColorClasses(industry.color);
                const Icon = industry.icon;
                const isSelected = formState.industry === industry.id;
                
                return (
                  <div
                    key={industry.id}
                    onClick={() => updateFormState('industry', industry.id)}
                    className={`group p-6 rounded-2xl border-2 cursor-pointer transition-all text-center hover:shadow-lg ${
                      isSelected 
                        ? `${colors.selectedBorder} ${colors.selectedBg} shadow-lg transform scale-105` 
                        : `${colors.border} bg-white hover:${colors.bg} hover:border-opacity-60`
                    }`}
                  >
                    <div className={`w-16 h-16 mx-auto mb-4 ${colors.bg} rounded-2xl flex items-center justify-center ${isSelected ? 'ring-4 ring-opacity-20' : ''}`}>
                      <Icon className={`w-8 h-8 ${colors.text}`} />
                    </div>
                    <h4 className="text-xl font-bold mb-2 text-gray-900">{industry.name}</h4>
                    <p className="text-gray-600 text-sm mb-3 leading-relaxed">{industry.description}</p>
                    
                    {/* Industry Stats */}
                    <div className="text-xs text-gray-500 space-y-1 border-t pt-3 mt-3">
                      {industry.id === 'beverages' && (
                        <>
                          <div className="flex justify-between">
                            <span>Market Size:</span>
                            <span className="font-medium">$1.3T Global</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Growth Rate:</span>
                            <span className="font-medium text-green-600">+6.8% CAGR</span>
                          </div>
                          <div className="flex justify-between">
                            <span>R&D Investment:</span>
                            <span className="font-medium">₹2-5L per product</span>
                          </div>
                        </>
                      )}
                      {industry.id === 'nutraceuticals' && (
                        <>
                          <div className="flex justify-between">
                            <span>Market Size:</span>
                            <span className="font-medium">$425B Global</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Growth Rate:</span>
                            <span className="font-medium text-green-600">+8.3% CAGR</span>
                          </div>
                          <div className="flex justify-between">
                            <span>R&D Investment:</span>
                            <span className="font-medium">₹5-15L per product</span>
                          </div>
                        </>
                      )}
                      {industry.id === 'cosmetics' && (
                        <>
                          <div className="flex justify-between">
                            <span>Market Size:</span>
                            <span className="font-medium">$380B Global</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Growth Rate:</span>
                            <span className="font-medium text-green-600">+5.9% CAGR</span>
                          </div>
                          <div className="flex justify-between">
                            <span>R&D Investment:</span>
                            <span className="font-medium">₹3-8L per product</span>
                          </div>
                        </>
                      )}
                    </div>
                    
                    {isSelected && (
                      <div className={`mt-3 flex items-center justify-center space-x-2 ${colors.text}`}>
                        <Check className="w-5 h-5" />
                        <span className="font-semibold text-sm">Selected</span>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Industry Insights */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-2xl border-2 border-blue-100">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center mr-3">
                  <BarChart3 className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">Industry Insights</h4>
                  <p className="text-sm text-gray-600">Key factors driving formulation innovation across sectors</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="bg-white p-4 rounded-xl border border-blue-200">
                  <div className="flex items-center mb-2">
                    <TrendingUp className="w-4 h-4 text-green-600 mr-2" />
                    <span className="text-sm font-semibold text-gray-900">Consumer Trends</span>
                  </div>
                  <ul className="text-xs text-gray-600 space-y-1">
                    <li>• Clean label ingredients (+23%)</li>
                    <li>• Personalized nutrition (+31%)</li>
                    <li>• Sustainable packaging (+19%)</li>
                    <li>• Plant-based alternatives (+42%)</li>
                  </ul>
                </div>
                
                <div className="bg-white p-4 rounded-xl border border-blue-200">
                  <div className="flex items-center mb-2">
                    <Shield className="w-4 h-4 text-purple-600 mr-2" />
                    <span className="text-sm font-semibold text-gray-900">Regulatory Focus</span>
                  </div>
                  <ul className="text-xs text-gray-600 space-y-1">
                    <li>• FDA/FSSAI compliance</li>
                    <li>• Novel ingredient approvals</li>
                    <li>• Health claim substantiation</li>
                    <li>• Safety assessment protocols</li>
                  </ul>
                </div>
                
                <div className="bg-white p-4 rounded-xl border border-blue-200">
                  <div className="flex items-center mb-2">
                    <Zap className="w-4 h-4 text-orange-600 mr-2" />
                    <span className="text-sm font-semibold text-gray-900">Innovation Areas</span>
                  </div>
                  <ul className="text-xs text-gray-600 space-y-1">
                    <li>• Bioavailability enhancement</li>
                    <li>• Microencapsulation tech</li>
                    <li>• AI-driven optimization</li>
                    <li>• Precision dosing systems</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Quick Facts */}
            {formState.industry && (
              <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-5 rounded-xl border-2 border-green-100">
                <div className="flex items-center mb-3">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                    <Lightbulb className="w-4 h-4 text-green-600" />
                  </div>
                  <h4 className="text-base font-semibold text-gray-900">
                    {formState.industry === 'beverages' ? 'Beverages Industry' :
                     formState.industry === 'nutraceuticals' ? 'Nutraceuticals Industry' :
                     'Cosmetics Industry'} Quick Facts
                  </h4>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-center">
                  {formState.industry === 'beverages' && (
                    <>
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-lg font-bold text-blue-600">300+</div>
                        <div className="text-xs text-gray-600">New launches/month</div>
                      </div>
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-lg font-bold text-green-600">12-18</div>
                        <div className="text-xs text-gray-600">Months to market</div>
                      </div>
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-lg font-bold text-purple-600">25%</div>
                        <div className="text-xs text-gray-600">Functional share</div>
                      </div>
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-lg font-bold text-orange-600">₹15-50</div>
                        <div className="text-xs text-gray-600">Price range/unit</div>
                      </div>
                    </>
                  )}
                  {formState.industry === 'nutraceuticals' && (
                    <>
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-lg font-bold text-blue-600">150+</div>
                        <div className="text-xs text-gray-600">New launches/month</div>
                      </div>
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-lg font-bold text-green-600">18-24</div>
                        <div className="text-xs text-gray-600">Months to market</div>
                      </div>
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-lg font-bold text-purple-600">65%</div>
                        <div className="text-xs text-gray-600">Preventive focus</div>
                      </div>
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-lg font-bold text-orange-600">₹200-2K</div>
                        <div className="text-xs text-gray-600">Price range/unit</div>
                      </div>
                    </>
                  )}
                  {formState.industry === 'cosmetics' && (
                    <>
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-lg font-bold text-blue-600">200+</div>
                        <div className="text-xs text-gray-600">New launches/month</div>
                      </div>
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-lg font-bold text-green-600">6-12</div>
                        <div className="text-xs text-gray-600">Months to market</div>
                      </div>
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-lg font-bold text-purple-600">40%</div>
                        <div className="text-xs text-gray-600">Natural/organic</div>
                      </div>
                      <div className="bg-white p-3 rounded-lg">
                        <div className="text-lg font-bold text-orange-600">₹99-5K</div>
                        <div className="text-xs text-gray-600">Price range/unit</div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        );

      case 1:
        const currentProducts = productTypes[formState.industry] || [];
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-5 gap-3">
              {currentProducts.map((product) => {
                const isSelected = formState.productType === product.id;
                const colors = getDetailedColorClasses(product.color);
                const Icon = product.icon;
                
                return (
                  <div
                    key={product.id}
                    onClick={() => updateFormState('productType', product.id)}
                    className={`p-3 rounded-xl border-2 cursor-pointer transition-all text-center ${
                      isSelected 
                        ? `${colors.selectedBorder} ${colors.selectedBg}` 
                        : `${colors.border} bg-white hover:${colors.bg}`
                    }`}
                  >
                    <div className={`w-8 h-8 ${colors.iconBg} rounded-lg mx-auto mb-2 flex items-center justify-center`}>
                      <Icon className={`w-4 h-4 ${colors.text}`} />
                    </div>
                    <h4 className="text-sm font-semibold mb-1">{product.name}</h4>
                    <p className="text-gray-600 text-xs mb-2">{product.description}</p>
                    
                    {isSelected && (
                      <div className="mt-1">
                        <Check className={`w-3 h-3 mx-auto ${colors.text}`} />
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
            {/* Always show product description textarea */}
            <div className="mt-6 bg-gray-50 p-4 rounded-lg">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                More about Product <span className="text-red-500">*</span>
                <span className="text-xs text-gray-500 ml-2">(minimum 50 characters)</span>
              </label>
              <textarea
                ref={customInputRef}
                rows={4}
                value={formState.productDescription}
                onChange={(e) => updateFormState('productDescription', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 resize-none ${
                  formState.productDescription.trim().length < 50 ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Provide detailed information about your product, including target benefits, key ingredients you have in mind, target audience, and any specific requirements. This helps our AI generate a more accurate formulation."
              />
              <div className="mt-2 flex justify-between items-center">
                <span className={`text-xs ${
                  formState.productDescription.trim().length < 50 ? 'text-red-500' : 'text-gray-500'
                }`}>
                  {formState.productDescription.trim().length}/50 characters
                </span>
                {formState.productDescription.trim().length >= 50 && (
                  <span className="text-xs text-green-600 flex items-center">
                    <Check className="w-3 h-3 mr-1" />
                    Ready for AI processing
                  </span>
                )}
              </div>
            </div>
          </div>
        );

      case 2:
        // Dynamic configuration for professional, flexible goals interface
        const coreParameters = [
          {
            key: 'budgetPerUnit',
            label: 'Budget per Unit',
            type: 'range',
            min: 50,
            max: 5000,
            step: 50,
            unit: 'INR',
            icon: DollarSign,
            color: 'green',
            description: 'Cost target per unit of finished product'
          },
          {
            key: 'nutritionScore',
            label: 'Nutrition Score Target',
            type: 'range',
            min: 50,
            max: 100,
            step: 5,
            unit: '%',
            icon: Target,
            color: 'blue',
            description: 'Desired nutritional value and bioavailability'
          },
          {
            key: 'sustainability',
            label: 'Sustainability Priority',
            type: 'range',
            min: 30,
            max: 100,
            step: 5,
            unit: '%',
            icon: Eco,
            color: 'green',
            description: 'Environmental impact and sourcing responsibility'
          },
          {
            key: 'shelfLifeDays',
            label: 'Shelf Life Requirement',
            type: 'range',
            min: 30,
            max: 1095, // 3 years
            step: 30,
            unit: 'days',
            icon: Clock,
            color: 'orange',
            description: 'Product stability and storage requirements'
          }
        ];

        const complianceOptions = [
          { 
            key: 'veganFriendly', 
            label: 'Vegan Friendly', 
            icon: '🌱', 
            description: 'No animal-derived ingredients' 
          },
          { 
            key: 'organicCertified', 
            label: 'Organic Certified', 
            icon: '🌿', 
            description: 'USDA/EU organic standards' 
          },
          { 
            key: 'lowCalorie', 
            label: 'Low Calorie', 
            icon: '⚡', 
            description: 'Reduced caloric content' 
          }
        ];

        // Realistic industry-specific parameters based on actual industry practices and standards
        const getIndustryProductParameters = (industry, productType) => {
          const parameterConfigs = {
            beverages: {
              'antioxidant-juice': [
                {
                  key: 'oracValue',
                  label: 'ORAC Target (Antioxidant Capacity)',
                  type: 'range',
                  min: 1000,
                  max: 15000,
                  step: 500,
                  unit: 'μmol TE/100g',
                  icon: Shield,
                  color: 'purple',
                  description: 'Oxygen Radical Absorbance Capacity - industry standard for antioxidant measurement'
                },
                {
                  key: 'brixLevel',
                  label: 'Brix Level (Sugar Content)',
                  type: 'range',
                  min: 8,
                  max: 16,
                  step: 0.5,
                  unit: '°Bx',
                  icon: Droplets,
                  color: 'orange',
                  description: 'Total soluble solids content - critical for taste and preservation'
                },
                {
                  key: 'phLevel',
                  label: 'pH Level',
                  type: 'range',
                  min: 3.2,
                  max: 4.2,
                  step: 0.1,
                  unit: 'pH',
                  icon: Target,
                  color: 'blue',
                  description: 'Acidity level for safety and taste optimization'
                },
                {
                  key: 'preservationMethod',
                  label: 'Preservation Method',
                  type: 'select',
                  options: ['HPP (High Pressure Processing)', 'Pasteurization', 'Natural Preservatives', 'Cold Chain Only'],
                  icon: Clock,
                  color: 'green',
                  description: 'FDA-approved preservation methods for shelf stability'
                }
              ],
              'energy-drink': [
                {
                  key: 'caffeineContent',
                  label: 'Caffeine Content',
                  type: 'range',
                  min: 50,
                  max: 200,
                  step: 10,
                  unit: 'mg/serving',
                  icon: Zap,
                  color: 'orange',
                  description: 'FDA limit: 400mg/day for adults, typical energy drinks: 50-200mg'
                },
                {
                  key: 'taurineContent',
                  label: 'Taurine Content',
                  type: 'range',
                  min: 500,
                  max: 2000,
                  step: 250,
                  unit: 'mg/serving',
                  icon: TrendingUp,
                  color: 'blue',
                  description: 'Amino acid for energy metabolism, EFSA safe level: up to 6g/day'
                },
                {
                  key: 'sugarContent',
                  label: 'Sugar Content Strategy',
                  type: 'select',
                  options: ['Full Sugar (25-35g)', 'Reduced Sugar (10-20g)', 'Zero Sugar (Artificial)', 'Natural Sweeteners'],
                  icon: Droplets,
                  color: 'pink',
                  description: 'Market positioning and regulatory compliance strategy'
                },
                {
                  key: 'bVitamins',
                  label: 'B-Vitamin Complex',
                  type: 'select',
                  options: ['Standard (B3,B6,B12)', 'Enhanced (Full B-Complex)', 'Targeted (B12+Folate)', 'Minimal'],
                  icon: Heart,
                  color: 'green',
                  description: 'Energy metabolism support - must comply with RDA limits'
                }
              ],
              'wellness-shot': [
                {
                  key: 'activeConcentration',
                  label: 'Active Ingredient Concentration',
                  type: 'range',
                  min: 1000,
                  max: 10000,
                  step: 500,
                  unit: 'mg/30ml shot',
                  icon: Target,
                  color: 'green',
                  description: 'Total active compounds per serving for therapeutic efficacy'
                },
                {
                  key: 'shotVolume',
                  label: 'Shot Volume',
                  type: 'select',
                  options: ['30ml (1 oz)', '60ml (2 oz)', '90ml (3 oz)'],
                  icon: Package,
                  color: 'blue',
                  description: 'Standard wellness shot formats for consumer convenience'
                },
                {
                  key: 'deliverySystem',
                  label: 'Bioavailability Enhancement',
                  type: 'select',
                  options: ['Liposomal Encapsulation', 'Nano-Emulsion', 'Fermented Extracts', 'Standard Extraction'],
                  icon: Sparkles,
                  color: 'purple',
                  description: 'Technology to improve nutrient absorption'
                }
              ],
              'functional-water': [
                {
                  key: 'electrolytePpm',
                  label: 'Total Dissolved Solids (TDS)',
                  type: 'range',
                  min: 50,
                  max: 300,
                  step: 25,
                  unit: 'ppm',
                  icon: Waves,
                  color: 'blue',
                  description: 'Mineral content for hydration and taste optimization'
                },
                {
                  key: 'functionalClaim',
                  label: 'Primary Functional Claim',
                  type: 'select',
                  options: ['Enhanced Hydration', 'Immune Support', 'Energy & Focus', 'Recovery & Electrolytes', 'Beauty & Collagen'],
                  icon: Target,
                  color: 'green',
                  description: 'FDA Structure/Function claims for marketing'
                }
              ]
            },
            nutraceuticals: {
              'multivitamin': [
                {
                  key: 'rdaPercentage',
                  label: 'RDA Coverage Target',
                  type: 'range',
                  min: 50,
                  max: 200,
                  step: 25,
                  unit: '% RDA',
                  icon: Target,
                  color: 'blue',
                  description: 'Percentage of Recommended Daily Allowance per serving'
                },
                {
                  key: 'bioavailabilityForm',
                  label: 'Bioavailable Forms',
                  type: 'select',
                  options: ['Standard Synthetic', 'Chelated Minerals', 'Whole Food Derived', 'Fermented Nutrients'],
                  icon: TrendingUp,
                  color: 'green',
                  description: 'Nutrient forms for optimal absorption and efficacy'
                },
                {
                  key: 'tabletWeight',
                  label: 'Tablet/Capsule Size',
                  type: 'select',
                  options: ['Small (400-600mg)', 'Medium (700-900mg)', 'Large (1000-1200mg)', 'Multi-tablet System'],
                  icon: Package,
                  color: 'orange',
                  description: 'Consumer preference and swallowing ease considerations'
                },
                {
                  key: 'specialFormulation',
                  label: 'Specialized Formulation',
                  type: 'select',
                  options: ['Men\'s Formula', 'Women\'s Formula', 'Senior (50+)', 'Prenatal', 'Teen/Young Adult'],
                  icon: Users,
                  color: 'purple',
                  description: 'Demographic-specific nutrient profiles'
                }
              ],
              'protein-powder': [
                {
                  key: 'proteinContent',
                  label: 'Protein Content per Serving',
                  type: 'range',
                  min: 15,
                  max: 35,
                  step: 2.5,
                  unit: 'g/serving',
                  icon: Dumbbell,
                  color: 'blue',
                  description: 'Typical range: 20-30g for muscle protein synthesis'
                },
                {
                  key: 'proteinSource',
                  label: 'Primary Protein Source',
                  type: 'select',
                  options: ['Whey Isolate (90%+)', 'Whey Concentrate (80%)', 'Casein Protein', 'Pea Protein Isolate', 'Soy Protein Isolate', 'Multi-Source Blend'],
                  icon: Leaf,
                  color: 'green',
                  description: 'Protein quality and absorption rate characteristics'
                },
                {
                  key: 'aminoProfile',
                  label: 'Amino Acid Enhancement',
                  type: 'select',
                  options: ['Complete Profile Only', '+BCAAs (5g)', '+Leucine Boost', '+Glutamine (3g)', 'Full EAA Spectrum'],
                  icon: TrendingUp,
                  color: 'purple',
                  description: 'Essential amino acid supplementation for performance'
                },
                {
                  key: 'additives',
                  label: 'Performance Additives',
                  type: 'select',
                  options: ['Protein Only', '+Creatine (3g)', '+Digestive Enzymes', '+Probiotics', '+MCT Oil'],
                  icon: Zap,
                  color: 'orange',
                  description: 'Additional ingredients for enhanced performance benefits'
                }
              ],
              'herbal-capsule': [
                {
                  key: 'extractRatio',
                  label: 'Standardized Extract Ratio',
                  type: 'select',
                  options: ['4:1 Extract', '10:1 Extract', '20:1 Extract', '50:1 Extract', 'Whole Herb Powder'],
                  icon: Leaf,
                  color: 'green',
                  description: 'Concentration level - higher ratios = more potent extracts'
                },
                {
                  key: 'standardization',
                  label: 'Active Compound Standardization',
                  type: 'range',
                  min: 1,
                  max: 50,
                  step: 2,
                  unit: '% active',
                  icon: Target,
                  color: 'purple',
                  description: 'Percentage of standardized active compounds for consistency'
                },
                {
                  key: 'thirdPartyTesting',
                  label: 'Quality Assurance Level',
                  type: 'select',
                  options: ['Basic COA', 'Heavy Metals Testing', 'Microbiological Testing', 'Full Panel + Potency', 'USP Verified'],
                  icon: Shield,
                  color: 'blue',
                  description: 'Testing standards for purity and potency verification'
                }
              ],
              'immunity-booster': [
                {
                  key: 'vitaminCDose',
                  label: 'Vitamin C Dosage',
                  type: 'range',
                  min: 100,
                  max: 2000,
                  step: 100,
                  unit: 'mg',
                  icon: Shield,
                  color: 'orange',
                  description: 'RDA: 90mg, Upper limit: 2000mg for immune support'
                },
                {
                  key: 'zincForm',
                  label: 'Zinc Form & Dosage',
                  type: 'select',
                  options: ['Zinc Gluconate (15mg)', 'Zinc Picolinate (22mg)', 'Zinc Bisglycinate (25mg)', 'Zinc Citrate (30mg)'],
                  icon: TrendingUp,
                  color: 'blue',
                  description: 'Bioavailable zinc forms, RDA: 11mg, Upper limit: 40mg'
                },
                {
                  key: 'immuneComplex',
                  label: 'Immune Support Complex',
                  type: 'select',
                  options: ['Vitamin C + Zinc Only', '+Elderberry (300mg)', '+Echinacea (400mg)', '+Mushroom Blend (500mg)', 'Full Spectrum (8+ ingredients)'],
                  icon: Sparkles,
                  color: 'purple',
                  description: 'Synergistic immune-supporting ingredient combinations'
                }
              ]
            },
            cosmetics: {
              'face-serum': [
                {
                  key: 'activeConcentration',
                  label: 'Primary Active Concentration',
                  type: 'range',
                  min: 0.5,
                  max: 20,
                  step: 0.5,
                  unit: '%',
                  icon: Droplets,
                  color: 'purple',
                  description: 'Active ingredient percentage - higher = more potent but potentially irritating'
                },
                {
                  key: 'phLevel',
                  label: 'pH Level',
                  type: 'range',
                  min: 4.5,
                  max: 7.0,
                  step: 0.2,
                  unit: 'pH',
                  icon: Target,
                  color: 'blue',
                  description: 'Skin-compatible pH for safety and active stability'
                },
                {
                  key: 'primaryActive',
                  label: 'Primary Active Ingredient',
                  type: 'select',
                  options: ['Hyaluronic Acid (1-2%)', 'Vitamin C (10-20%)', 'Retinol (0.25-1%)', 'Niacinamide (5-10%)', 'Peptide Complex (3-8%)'],
                  icon: Sparkles,
                  color: 'pink',
                  description: 'Evidence-based concentrations for efficacy'
                },
                {
                  key: 'delivery',
                  label: 'Delivery System',
                  type: 'select',
                  options: ['Standard Solution', 'Liposomal Encapsulation', 'Micro-Encapsulation', 'Time-Release Technology'],
                  icon: Zap,
                  color: 'green',
                  description: 'Advanced delivery for enhanced penetration and stability'
                }
              ],
              'herbal-cream': [
                {
                  key: 'herbConcentration',
                  label: 'Herbal Extract Concentration',
                  type: 'range',
                  min: 1,
                  max: 15,
                  step: 1,
                  unit: '%',
                  icon: Leaf,
                  color: 'green',
                  description: 'Total herbal extract content in formulation'
                },
                {
                  key: 'baseFormulation',
                  label: 'Cream Base Type',
                  type: 'select',
                  options: ['Water-in-Oil (Rich)', 'Oil-in-Water (Light)', 'Anhydrous (Oil-based)', 'Gel-Cream Hybrid'],
                  icon: Layers,
                  color: 'blue',
                  description: 'Emulsion type affects texture and absorption'
                },
                {
                  key: 'ayurvedicBlend',
                  label: 'Traditional Ayurvedic Blend',
                  type: 'select',
                  options: ['Single Herb Focus', 'Tridoshic Balance', 'Pitta Pacifying', 'Vata Nourishing', 'Kapha Balancing'],
                  icon: Flower2,
                  color: 'purple',
                  description: 'Ayurvedic principles for holistic skin health'
                },
                {
                  key: 'preservativeSystem',
                  label: 'Preservation Strategy',
                  type: 'select',
                  options: ['Natural Preservatives Only', 'Broad Spectrum (Paraben-free)', 'Self-Preserving Formula', 'Minimal Processing'],
                  icon: Shield,
                  color: 'orange',
                  description: 'Microbial safety while maintaining natural appeal'
                }
              ],
              'ayurvedic-oil': [
                {
                  key: 'oilProcessing',
                  label: 'Traditional Processing Method',
                  type: 'select',
                  options: ['Taila Paka (Classical)', 'Cold Infusion', 'Steam Distillation', 'Solvent-Free Extraction'],
                  icon: Droplets,
                  color: 'yellow',
                  description: 'Authentic Ayurvedic oil preparation methods'
                },
                {
                  key: 'herbOilRatio',
                  label: 'Herb to Oil Ratio',
                  type: 'select',
                  options: ['1:4 (Mild)', '1:8 (Standard)', '1:12 (Potent)', '1:16 (Maximum)'],
                  icon: Target,
                  color: 'green',
                  description: 'Traditional Ayurvedic ratios for therapeutic potency'
                },
                {
                  key: 'baseOilBlend',
                  label: 'Carrier Oil Combination',
                  type: 'select',
                  options: ['Sesame Oil Only', 'Coconut + Sesame', 'Jojoba + Argan', 'Multi-Oil Blend (5+)', 'Seasonal Oils'],
                  icon: Waves,
                  color: 'blue',
                  description: 'Base oil selection for skin type and climate'
                },
                {
                  key: 'shelfStability',
                  label: 'Natural Stability Enhancement',
                  type: 'select',
                  options: ['No Additives', 'Vitamin E (Tocopherol)', 'Rosemary Extract', 'Traditional Herbs', 'Minimal Intervention'],
                  icon: Clock,
                  color: 'purple',
                  description: 'Natural antioxidants for oil preservation'
                }
              ],
              'natural-cleanser': [
                {
                  key: 'cleansingAgents',
                  label: 'Primary Cleansing System',
                  type: 'select',
                  options: ['Sulfate-Free Syndets', 'Plant-Based Saponins', 'Amino Acid Surfactants', 'Oil Cleansing Method', 'Clay & Charcoal'],
                  icon: Sparkles,
                  color: 'blue',
                  description: 'Gentle yet effective cleansing mechanisms'
                },
                {
                  key: 'phBalance',
                  label: 'pH Balance',
                  type: 'range',
                  min: 4.5,
                  max: 6.5,
                  step: 0.2,
                  unit: 'pH',
                  icon: Target,
                  color: 'green',
                  description: 'Skin-friendly pH to maintain acid mantle'
                },
                {
                  key: 'mildnessIndex',
                  label: 'Gentleness Level',
                  type: 'select',
                  options: ['Ultra-Gentle (Baby-safe)', 'Sensitive Skin', 'Normal Skin', 'Deep Cleansing', 'Exfoliating Action'],
                  icon: Heart,
                  color: 'pink',
                  description: 'Cleansing intensity appropriate for skin sensitivity'
                },
                {
                  key: 'naturalActives',
                  label: 'Natural Active Botanicals',
                  type: 'select',
                  options: ['Chamomile + Calendula', 'Green Tea + Aloe', 'Rose + Hibiscus', 'Neem + Turmeric', 'Lavender + Tea Tree'],
                  icon: Leaf,
                  color: 'purple',
                  description: 'Therapeutic plant extracts for skin benefits'
                }
              ]
            }
          };

          // Get parameters for specific industry + product type combination
          const industryParams = parameterConfigs[industry];
          if (!industryParams) return [];
          
          const productParams = industryParams[productType];
          if (!productParams) {
            // Fallback to generic industry parameters if specific product type not found
            const fallbackParams = {
              beverages: [
                {
                  key: 'flavor',
                  label: 'Flavor Profile',
                  type: 'select',
                  options: ['Natural', 'Citrus', 'Berry', 'Tropical', 'Custom'],
                  icon: Palette,
                  color: 'pink'
                }
              ],
              nutraceuticals: [
                {
                  key: 'packaging',
                  label: 'Dosage Form',
                  type: 'select',
                  options: ['Capsules', 'Tablets', 'Powder', 'Liquid'],
                  icon: Package,
                  color: 'indigo'
                }
              ],
              cosmetics: [
                {
                  key: 'skinType',
                  label: 'Target Skin Type',
                  type: 'select',
                  options: ['All Skin Types', 'Oily', 'Dry', 'Sensitive'],
                  icon: Users,
                  color: 'pink'
                }
              ]
            };
            return fallbackParams[industry] || [];
          }
          
          return productParams;
        };

        const industryParameters = getIndustryProductParameters(formState.industry, formState.productType);

        const getSliderColorClasses = (color) => {
          const colorMap = {
            green: { bg: 'bg-green-100', text: 'text-green-600', border: 'border-green-500', slider: 'slider-green' },
            blue: { bg: 'bg-blue-100', text: 'text-blue-600', border: 'border-blue-500', slider: 'slider-blue' },
            orange: { bg: 'bg-orange-100', text: 'text-orange-600', border: 'border-orange-500', slider: 'slider-orange' },
            purple: { bg: 'bg-purple-100', text: 'text-purple-600', border: 'border-purple-500', slider: 'slider-purple' },
            pink: { bg: 'bg-pink-100', text: 'text-pink-600', border: 'border-pink-500', slider: 'slider-pink' },
            indigo: { bg: 'bg-indigo-100', text: 'text-indigo-600', border: 'border-indigo-500', slider: 'slider-indigo' }
          };
          return colorMap[color] || colorMap.blue;
        };

        const formatValue = (value, unit) => {
          if (unit === 'USD') return `$${value}`;
          if (unit === 'INR') return `₹${value}`;
          if (unit === 'days') {
            if (value >= 365) return `${Math.round(value / 365 * 10) / 10} years`;
            if (value >= 30) return `${Math.round(value / 30)} months`;
            return `${value} days`;
          }
          return `${value}${unit}`;
        };

        return (
          <div className="space-y-6 max-h-[calc(90vh-240px)] overflow-y-auto">
            
            {/* Core Parameters - Single Row */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
              {coreParameters.map(param => {
                const colors = getSliderColorClasses(param.color);
                const IconComponent = param.icon;
                
                return (
                  <div key={param.key} className="bg-white p-4 rounded-xl border-2 border-gray-100 hover:border-gray-200 transition-all shadow-sm">
                    <div className="flex items-center mb-3">
                      <div className={`w-8 h-8 ${colors.bg} rounded-lg flex items-center justify-center mr-2`}>
                        <IconComponent className={`w-4 h-4 ${colors.text}`} />
                      </div>
                      <div>
                        <h4 className="text-sm font-semibold text-gray-900">{param.label}</h4>
                        <p className="text-xs text-gray-500 line-clamp-1">{param.description}</p>
                      </div>
                    </div>
                    
                    <input
                      type="range"
                      min={param.min}
                      max={param.max}
                      step={param.step}
                      value={formState[param.key]}
                      onChange={(e) => updateFormState(param.key, parseInt(e.target.value))}
                      className={`w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer ${colors.slider} mb-3`}
                    />
                    
                    <div className="text-center">
                      <span className={`text-lg font-bold ${colors.text} px-2 py-1 ${colors.bg} rounded-lg block`}>
                        {formatValue(formState[param.key], param.unit)}
                      </span>
                      <div className="flex justify-between text-xs text-gray-400 mt-1">
                        <span>{formatValue(param.min, param.unit)}</span>
                        <span>{formatValue(param.max, param.unit)}</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Compliance Requirements */}
            <div className="bg-gradient-to-br from-purple-50 to-blue-50 p-5 rounded-xl border-2 border-purple-100">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                  <Shield className="w-4 h-4 text-purple-600" />
                </div>
                <div>
                  <h4 className="text-base font-semibold text-gray-900">Compliance & Certifications</h4>
                  <p className="text-xs text-gray-600">Select applicable standards and certifications</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {complianceOptions.map(option => (
                  <div
                    key={option.key}
                    onClick={() => updateFormState(option.key, !formState[option.key])}
                    className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                      formState[option.key]
                        ? 'border-purple-500 bg-purple-100 shadow-md'
                        : 'border-gray-200 bg-white hover:border-purple-300 hover:shadow-sm'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{option.icon}</span>
                      <div className="flex-1">
                        <h5 className={`text-sm font-semibold ${formState[option.key] ? 'text-purple-800' : 'text-gray-900'}`}>
                          {option.label}
                        </h5>
                        <p className={`text-xs ${formState[option.key] ? 'text-purple-600' : 'text-gray-500'}`}>
                          {option.description}
                        </p>
                      </div>
                      {formState[option.key] && (
                        <Check className="w-4 h-4 text-purple-600" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Industry-Specific Parameters */}
            {industryParameters.length > 0 && (
              <div className="bg-gradient-to-br from-gray-50 to-blue-50 p-5 rounded-xl border-2 border-gray-100">
                <div className="flex items-center mb-4">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <Zap className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="text-base font-semibold text-gray-900">
                      Professional {formState.industry.charAt(0).toUpperCase() + formState.industry.slice(1)} Parameters
                    </h4>
                    <p className="text-xs text-gray-600">Industry-standard specifications for {formState.productType.replace('-', ' ')}</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {industryParameters.map(param => {
                    const colors = getSliderColorClasses(param.color);
                    const IconComponent = param.icon;
                    
                    return (
                      <div key={param.key} className="bg-white p-3 rounded-lg border border-gray-200">
                        <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                          <div className={`w-6 h-6 ${colors.bg} rounded-lg flex items-center justify-center mr-2`}>
                            <IconComponent className={`w-3 h-3 ${colors.text}`} />
                          </div>
                          {param.label}
                        </label>
                        {param.description && (
                          <p className="text-xs text-gray-600 mb-2 pl-8 leading-relaxed">
                            {param.description}
                          </p>
                        )}
                        
                        {param.type === 'range' ? (
                          <>
                            <input
                              type="range"
                              min={param.min}
                              max={param.max}
                              step={param.step}
                              value={formState[param.key]}
                              onChange={(e) => updateFormState(param.key, parseInt(e.target.value))}
                              className={`w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer ${colors.slider} mb-3`}
                            />
                            <div className="flex justify-between text-xs text-gray-600">
                              <span>{param.min}{param.unit}</span>
                              <span className={`font-bold ${colors.text}`}>
                                {formState[param.key]}{param.unit}
                              </span>
                              <span>{param.max}{param.unit}</span>
                            </div>
                          </>
                        ) : (
                          <select
                            value={formState[param.key]}
                            onChange={(e) => updateFormState(param.key, e.target.value)}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-sm font-medium"
                          >
                            <option value="">Choose {param.label}</option>
                            {param.options.map(option => (
                              <option key={option} value={option.toLowerCase()}>{option}</option>
                            ))}
                          </select>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* AI Optimization Note */}
            <div className="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-xl border border-green-200">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <Sparkles className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <h5 className="font-semibold text-green-800">AI Optimization Ready</h5>
                  <p className="text-sm text-green-600">
                    Our AI will balance all parameters to create optimal formulations within your constraints
                  </p>
                </div>
              </div>
            </div>
          </div>
        );


      default:
        return null;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      {/* Beautiful Loading Overlay - positioned at modal level */}
      {isLoadingResults && (
        <div className="absolute inset-0 bg-gradient-to-br from-green-900/40 via-blue-900/40 to-purple-900/40 backdrop-blur-sm z-60 flex items-center justify-center">
          <div className="bg-white/95 backdrop-blur-md rounded-3xl shadow-2xl p-12 max-w-2xl mx-4 border border-white/20">
            {/* Animated Icon */}
            <div className="text-center mb-8">
              <div className="relative inline-block">
                <div className="w-24 h-24 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse">
                  <FlaskConical className="w-12 h-12 text-white animate-bounce" />
                </div>
                {/* Floating particles */}
                <div className="absolute -top-4 -right-4 w-4 h-4 bg-green-400 rounded-full animate-ping"></div>
                <div className="absolute -bottom-2 -left-2 w-3 h-3 bg-blue-400 rounded-full animate-ping" style={{animationDelay: '1s'}}></div>
                <div className="absolute top-0 left-8 w-2 h-2 bg-purple-400 rounded-full animate-ping" style={{animationDelay: '2s'}}></div>
              </div>
            </div>

            {/* Main Content */}
            <div className="text-center space-y-6">
              <h3 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                🧪 Crafting Your Perfect Formulation
              </h3>
              
              <p className="text-lg text-gray-700 font-medium">
                {generationStatus}
              </p>

              {/* Enhanced Progress Circle */}
              <div className="relative w-32 h-32 mx-auto">
                <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                  <circle
                    cx="60"
                    cy="60"
                    r="54"
                    stroke="currentColor"
                    strokeWidth="8"
                    fill="none"
                    className="text-gray-200"
                  />
                  <circle
                    cx="60"
                    cy="60"
                    r="54"
                    stroke="currentColor"
                    strokeWidth="8"
                    fill="none"
                    strokeDasharray={340}
                    strokeDashoffset={340 - (340 * Math.min(generationProgress, 100)) / 100}
                    className="text-green-500 transition-all duration-500 ease-out"
                    strokeLinecap="round"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-2xl font-bold text-green-600">{Math.min(Math.round(generationProgress), 100)}%</span>
                </div>
              </div>

              {/* Time Estimate */}
              <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-4 border border-green-200">
                <div className="flex items-center justify-center space-x-2">
                  <Clock className="w-5 h-5 text-green-600" />
                  <span className="text-green-800 font-medium">
                    Est. {generationProgress < 50 ? '45-60' : generationProgress < 80 ? '20-30' : '5-10'} seconds remaining
                  </span>
                </div>
              </div>

              {/* Processing Steps */}
              <div className="space-y-3">
                <div className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-500 ${generationProgress > 0 ? 'bg-green-50 text-green-800' : 'bg-gray-50 text-gray-500'}`}>
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${generationProgress > 0 ? 'bg-green-200' : 'bg-gray-200'}`}>
                    {generationProgress > 0 ? <Check className="w-4 h-4" /> : <div className="w-2 h-2 bg-gray-400 rounded-full"></div>}
                  </div>
                  <span className="font-medium">Analyzing your requirements</span>
                </div>
                <div className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-500 ${generationProgress > 25 ? 'bg-green-50 text-green-800' : 'bg-gray-50 text-gray-500'}`}>
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${generationProgress > 25 ? 'bg-green-200' : 'bg-gray-200'}`}>
                    {generationProgress > 25 ? <Check className="w-4 h-4" /> : <div className="w-2 h-2 bg-gray-400 rounded-full"></div>}
                  </div>
                  <span className="font-medium">Generating ingredient combinations</span>
                </div>
                <div className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-500 ${generationProgress > 60 ? 'bg-green-50 text-green-800' : 'bg-gray-50 text-gray-500'}`}>
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${generationProgress > 60 ? 'bg-green-200' : 'bg-gray-200'}`}>
                    {generationProgress > 60 ? <Check className="w-4 h-4" /> : <div className="w-2 h-2 bg-gray-400 rounded-full"></div>}
                  </div>
                  <span className="font-medium">Calculating nutritional profiles</span>
                </div>
                <div className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-500 ${generationProgress > 85 ? 'bg-green-50 text-green-800' : 'bg-gray-50 text-gray-500'}`}>
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${generationProgress > 85 ? 'bg-green-200' : 'bg-gray-200'}`}>
                    {generationProgress > 85 ? <Check className="w-4 h-4" /> : <div className="w-2 h-2 bg-gray-400 rounded-full"></div>}
                  </div>
                  <span className="font-medium">Finalizing formulation data</span>
                </div>
              </div>

              {/* Fun Fact */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200">
                <div className="flex items-start space-x-3">
                  <Sparkles className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-semibold text-blue-800 mb-1">Did you know?</p>
                    <p className="text-sm text-blue-700">
                      Our AI considers over 1,000+ ingredient combinations and market factors to create your perfect {formState.industry} formulation, optimized for the Indian market.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <div className="bg-white rounded-xl max-w-7xl w-full h-[90vh] flex flex-col overflow-hidden">
        {/* Modal Header with Progress */}
        <div className="flex-shrink-0 bg-green-50 border-b border-green-200">
          <div className="flex items-center justify-between px-6 py-4">
            <div>
              <h2 className="text-xl font-bold text-gray-900">{steps[currentStep]?.title || 'New Formulation Project'}</h2>
              <p className="text-gray-600 text-sm">Step {currentStep + 1} of {steps.length} • {steps[currentStep]?.subtitle}</p>
            </div>
            <button 
              onClick={handleCancel}
              className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          {/* Progress Bar */}
          <div className="flex items-center space-x-3 px-6 pb-4">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`w-7 h-7 rounded-full flex items-center justify-center text-xs font-medium ${
                  index < currentStep 
                    ? 'bg-green-500 text-white' 
                    : index === currentStep 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 text-gray-600'
                }`}>
                  {index < currentStep ? <Check className="w-3 h-3" /> : index + 1}
                </div>
                <span className={`ml-2 text-xs font-medium ${
                  index <= currentStep ? 'text-gray-900' : 'text-gray-500'
                }`}>
                  {step.label}
                </span>
                {index < steps.length - 1 && (
                  <div className={`mx-3 h-px w-10 ${
                    index < currentStep ? 'bg-green-500' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Modal Content - Scrollable middle section */}
        <div className="flex-1 overflow-y-auto p-6">
          {renderStepContent()}
        </div>

        {/* Fixed Modal Footer */}
        <div className="flex-shrink-0 flex justify-between items-center px-6 py-4 border-t border-green-200 bg-green-50">
          <div>
            {currentStep > 0 && (
              <button 
                onClick={handleBack}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center"
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                Back
              </button>
            )}
          </div>
          <button 
            onClick={handleNext}
            disabled={!canProceed() || isLoadingResults}
            className="px-8 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center transition-colors"
          >
            {currentStep === steps.length - 1 ? (
              <>
                {isLoadingResults ? (
                  <div className="flex flex-col items-center space-y-2 w-full">
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Generating with Claude AI...</span>
                    </div>
                    <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
                      <div 
                        className="bg-white h-2 rounded-full transition-all duration-500"
                        style={{ width: `${Math.min(generationProgress, 100)}%` }}
                      ></div>
                    </div>
                    <span className="text-xs text-white opacity-80">{generationStatus}</span>
                  </div>
                ) : (
                  <>
                    <Zap className="w-4 h-4 mr-2" />
                    Generate Formulation
                  </>
                )}
              </>
            ) : (
              <>
                Continue <ChevronRight className="ml-2 w-4 h-4" />
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FormulationWizardModal;