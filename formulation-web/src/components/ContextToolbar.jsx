import React from 'react';
import { ChevronDown, Target, Layers } from 'lucide-react';

const ContextToolbar = ({ 
  variantScope, 
  setVariantScope, 
  componentScope, 
  setComponentScope,
  currentVariantName = 'Main Recipe'
}) => {
  
  // Variant scope options
  const variantOptions = [
    { value: 'current', label: 'Current Variant', icon: Target },
    { value: 'all', label: 'All Variants', icon: Layers }
  ];
  
  // Component scope options
  const componentOptions = [
    { value: 'all', label: 'All Components' },
    { value: 'ingredients', label: 'Ingredients' },
    { value: 'cost', label: 'Cost Analysis' },
    { value: 'nutrition', label: 'Nutritional Profile' },
    { value: 'compliance', label: 'Compliance' },
    { value: 'manufacturing', label: 'Manufacturing' },
    { value: 'sustainability', label: 'Sustainability' },
    { value: 'packaging', label: 'Packaging' }
  ];
  
  return (
    <div className="bg-transparent px-4 py-3 border-b border-emerald-200/50">
      <div className="flex items-center space-x-3">
        {/* Variant Scope Dropdown */}
        <div className="relative">
          <select
            value={variantScope}
            onChange={(e) => setVariantScope(e.target.value)}
            className="appearance-none bg-white border border-gray-300 text-gray-900 text-sm rounded-md 
                     focus:ring-2 focus:ring-green-500 focus:border-green-500 block pl-3 pr-8 py-1.5
                     hover:bg-gray-50 transition-colors cursor-pointer min-w-[140px]"
          >
            {variantOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <ChevronDown className="h-3 w-3" />
          </div>
        </div>
        
        {/* Component Scope Dropdown */}
        <div className="relative">
          <select
            value={componentScope}
            onChange={(e) => setComponentScope(e.target.value)}
            className="appearance-none bg-white border border-gray-300 text-gray-900 text-sm rounded-md 
                     focus:ring-2 focus:ring-green-500 focus:border-green-500 block pl-3 pr-8 py-1.5
                     hover:bg-gray-50 transition-colors cursor-pointer min-w-[140px]"
          >
            {componentOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <ChevronDown className="h-3 w-3" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContextToolbar;