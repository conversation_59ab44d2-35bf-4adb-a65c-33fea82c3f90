import React, { useEffect } from 'react';
import { Routes, Route, Navigate, useParams } from 'react-router-dom';
import { AuthProvider, useAuth } from './context/AuthContext';
import { AppProvider, useApp } from './context/AppContext';
import LandingPage from './pages/LandingPage';
import Dashboard from './pages/Dashboard';
import Projects from './pages/Projects';
import IndustrySelection from './pages/IndustrySelection';
import ResultsDashboard from './pages/ResultsDashboard';
import ProductPlayground from './pages/ProductPlayground';
import Login from './components/Login';
import ProtectedRoute from './components/ProtectedRoute';
import Layout from './components/Layout';
import ProductTypeSelection from './pages/ProductTypeSelection';
import SubCategorySelection from './pages/SubCategorySelection';
import GoalSetup from './pages/GoalSetup';
import BrandingStep from './pages/BrandingStep';
import GTMStep from './pages/GTMStep';
import NewProjectWizardModal from './components/NewProjectWizardModal';
import AdminConfig from './pages/AdminConfig';
import ParametersAdmin from './pages/ParametersAdmin';
import FormulationBlocks from './pages/FormulationBlocks';
import KeyboardTestComponent from './components/KeyboardTestComponent';
// Import other pages as they are created
// import FormulationInterface from './pages/FormulationInterface';
// import InteractiveResultsDashboard from './pages/InteractiveResultsDashboard';

// Temporary placeholders for pages not yet created
const FormulationInterface = () => <div className="min-h-screen flex items-center justify-center"><div className="text-xl">Formulation Interface - Coming Soon</div></div>;
const InteractiveResultsDashboard = () => <div className="min-h-screen flex items-center justify-center"><div className="text-xl">Results Dashboard - Coming Soon</div></div>;

// Wrapper component for ProductPlayground with params
const ProductPlaygroundWrapper = () => {
  const { projectId } = useParams();
  return <ProductPlayground projectId={projectId} />;
};

// Main routing component
const AppRoutes = () => {
  const { isAuthenticated } = useAuth();
  
  return (
    <Routes>
      {/* Public routes */}
      <Route path="/" element={<LandingPage />} />
      <Route path="/login" element={<Login />} />
      
      {/* Protected routes with Layout */}
      <Route path="/dashboard" element={
        <ProtectedRoute>
          <Layout>
            <Dashboard />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/projects" element={
        <ProtectedRoute>
          <Layout>
            <Projects />
          </Layout>
        </ProtectedRoute>
      } />
      
      {/* Playground route without Layout (no sidebar) */}
      <Route path="/playground/:projectId" element={
        <ProtectedRoute>
          <ProductPlaygroundWrapper />
        </ProtectedRoute>
      } />
      
      <Route path="/keyboard-test" element={
        <ProtectedRoute>
          <Layout>
            <KeyboardTestComponent />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/industry-selection" element={
        <ProtectedRoute>
          <Layout>
            <IndustrySelection />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/category-selector" element={
        <ProtectedRoute>
          <Layout>
            <ProductTypeSelection />
          </Layout>
        </ProtectedRoute>
      } />

      {/* New Project Wizard as modal */}
      <Route path="/new-project" element={
        <ProtectedRoute>
          <Layout>
            <NewProjectWizardModal onClose={() => window.history.back()} />
          </Layout>
        </ProtectedRoute>
      } />
      <Route path="/subcategory-selector" element={
        <ProtectedRoute>
          <Layout>
            <SubCategorySelection />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/goal-setup" element={
        <ProtectedRoute>
          <Layout>
            <GoalSetup />
          </Layout>
        </ProtectedRoute>
      } />

      <Route path="/admin/config" element={
        <ProtectedRoute>
          <Layout>
            <AdminConfig />
          </Layout>
        </ProtectedRoute>
      } />

      <Route path="/admin/parameters" element={
        <ProtectedRoute>
          <Layout>
            <ParametersAdmin />
          </Layout>
        </ProtectedRoute>
      } />

      <Route path="/admin/formulation-blocks" element={
        <ProtectedRoute>
          <Layout>
            <FormulationBlocks />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/formulation" element={
        <ProtectedRoute>
          <Layout>
            <FormulationInterface />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/branding" element={
        <ProtectedRoute>
          <Layout>
            <BrandingStep />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/gtm" element={
        <ProtectedRoute>
          <Layout>
            <GTMStep />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/results" element={
        <ProtectedRoute>
          <Layout>
            <ResultsDashboard />
          </Layout>
        </ProtectedRoute>
      } />
      
      {/* Catch all - redirect to home */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

const App = () => {
  return (
    <AuthProvider>
      <AppProvider>
        <div className="font-sans">
          <AppRoutes />
        </div>
      </AppProvider>
    </AuthProvider>
  );
};

export default App;
