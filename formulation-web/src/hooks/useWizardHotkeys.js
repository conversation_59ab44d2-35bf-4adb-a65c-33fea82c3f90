import { useEffect } from 'react';

export default function useWizardHotkeys({ onEsc, enabled = true, isDirty = false } = {}) {
  useEffect(() => {
    if (!enabled) return;
    const isTyping = (el) => {
      const tag = el?.tagName?.toLowerCase();
      return tag === 'input' || tag === 'textarea' || el?.isContentEditable;
    };
    const handler = (e) => {
      if (e.key !== 'Escape') return;
      if (isTyping(document.activeElement)) return;
      e.preventDefault();
      if (isDirty) {
        const ok = window.confirm('Discard your changes and go back?');
        if (!ok) return;
      }
      onEsc?.();
    };
    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, [onEsc, enabled, isDirty]);
}

