// Simple session-scoped cache for answer sets
const CACHE_KEY = 'answers_cache_v1';

const readCache = () => {
  try {
    const raw = sessionStorage.getItem(CACHE_KEY);
    return raw ? JSON.parse(raw) : {};
  } catch {
    return {};
  }
};

const writeCache = (data) => {
  try {
    sessionStorage.setItem(CACHE_KEY, JSON.stringify(data));
  } catch {
    // ignore write failures (private mode, quota, etc.)
  }
};

export const getAnswerSetsWithCache = async (apiRequest, keys = []) => {
  const unique = Array.from(new Set(keys.filter(Boolean)));
  if (unique.length === 0) return {};

  const cache = readCache();
  const missing = unique.filter(k => !cache[k]);

  if (missing.length > 0) {
    const resp = await apiRequest(`/param-answers?keys=${encodeURIComponent(missing.join(','))}`);
    const fetched = resp?.data || {};
    // merge into cache
    for (const k of Object.keys(fetched)) {
      cache[k] = fetched[k];
    }
    writeCache(cache);
  }

  // build result map for requested keys
  const result = {};
  for (const k of unique) {
    if (cache[k]) result[k] = cache[k];
  }
  return result;
};

