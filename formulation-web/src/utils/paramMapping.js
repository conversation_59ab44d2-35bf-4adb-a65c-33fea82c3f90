// Normalize bindings to field models
export const mapBindingsToFields = (bindings = []) =>
  bindings.map(b => {
    const validators = b.validators || {};
    const guidance = b.guidance || {};
    const widget = { type: 'text', ...(b.widget || {}) };
    const unit = guidance.unit;

    // If widget range missing, derive from validators
    if (widget.type === 'number' || widget.type === 'slider' || widget.type === 'range') {
      if (validators.min !== undefined && widget.min === undefined) widget.min = validators.min;
      if (validators.max !== undefined && widget.max === undefined) widget.max = validators.max;
      if (widget.type === 'slider' && widget.step === undefined) widget.step = 1;
    }

    // Compose helper text if not provided
    if (!widget.helper_text) {
      const withUnit = (v) => (unit ? `${v} ${unit}` : `${v}`);
      const parts = [];
      if (validators.min !== undefined) parts.push(`Min: ${withUnit(validators.min)}`);
      if (validators.max !== undefined) parts.push(`Max: ${withUnit(validators.max)}`);
      if (guidance.average !== undefined) parts.push(`Average: ${withUnit(guidance.average)}`);
      if (parts.length) widget.helper_text = parts.join(' · ');
    }

    return {
      key: b.parameter_key,
      label: b.name || b.parameter_key,
      type: b.type,
      widget,
      guidance,
      validators,
      required: !!b.required,
      default_value: b.default_value || guidance.recommended,
      // initial_value: use guidance.recommended as primary source, fallback to guidance.average for numeric fields
      initial_value:
        guidance.recommended !== undefined
          ? guidance.recommended
          : ((widget.type === 'slider' || widget.type === 'range' || b.type === 'number') && guidance.average !== undefined
              ? guidance.average
              : undefined),
      answers_key: b.answers_key,
      section: b.display?.section || 'General',
      display_order: typeof b.display?.order === 'number' ? b.display.order : 999
    };
  });

// Remove duplicate fields by key, keeping the first occurrence (lowest display_order wins)
const dedupeFieldsByKey = (fields = []) => {
  const seen = new Set();
  const result = [];
  for (const f of fields.sort((a, b) => (a.display_order ?? 999) - (b.display_order ?? 999))) {
    if (seen.has(f.key)) continue;
    seen.add(f.key);
    result.push(f);
  }
  return result;
};

export const groupFieldsBySection = (fields = []) => {
  const unique = dedupeFieldsByKey(fields);
  const grouped = unique.reduce((acc, f) => {
    const section = f.section || 'General';
    (acc[section] ||= []).push(f);
    return acc;
  }, {});
  return Object.entries(grouped).map(([name, params]) => ({
    name,
    parameters: params.sort((a, b) => a.display_order - b.display_order)
  }));
};

export const collectAnswerKeys = (fields = []) => {
  const keys = new Set();
  fields.forEach(f => { if (f.answers_key) keys.add(f.answers_key); });
  return Array.from(keys);
};

export const hydrateFieldsWithAnswers = (fields = [], answerMap = {}) =>
  fields.map(f => {
    if (!f.answers_key) return f;
    const set = answerMap[f.answers_key];
    if (!set || !Array.isArray(set.options)) return f;
    return { ...f, options: set.options };
  });
