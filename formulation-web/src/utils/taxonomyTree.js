export const buildTaxonomyTree = (flat = []) => {
  const byId = new Map();
  const childrenMap = new Map();

  flat.forEach(item => {
    byId.set(String(item._id), item);
    childrenMap.set(String(item._id), []);
  });

  const roots = [];
  flat.forEach(item => {
    const pid = item.parent_id ? String(item.parent_id) : null;
    if (pid && childrenMap.has(pid)) {
      childrenMap.get(pid).push(item);
    } else {
      // Root is an industry, or items without a valid parent
      roots.push(item);
    }
  });

  const sortByName = list => list.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
  sortByName(roots);
  childrenMap.forEach(sortByName);

  const attach = (node) => ({
    ...node,
    children: childrenMap.get(String(node._id))?.map(attach) || []
  });

  // Prefer only industries as true roots
  const industries = roots.filter(r => r.level === 'industry');
  const others = roots.filter(r => r.level !== 'industry');
  const top = (industries.length ? industries : roots).map(attach);

  // Attach stray nodes under a virtual root if needed
  if (!industries.length && others.length) {
    return [{ _id: 'virtual', name: 'All', level: 'root', children: top }];
  }
  return top;
};

// Keep nodes that match query or whose descendants match; retain their ancestors
export const filterTaxonomiesByQuery = (flat = [], q = '') => {
  const query = (q || '').trim().toLowerCase();
  if (!query) return flat;
  const byId = new Map(flat.map(i => [String(i._id), i]));
  const children = new Map();
  flat.forEach(i => children.set(String(i._id), []));
  const roots = [];
  flat.forEach(i => {
    const pid = i.parent_id ? String(i.parent_id) : null;
    if (pid && children.has(pid)) children.get(pid).push(i);
    else roots.push(i);
  });
  const keep = new Set();
  const matches = (n) => (n.name || '').toLowerCase().includes(query) || (n.slug || '').toLowerCase().includes(query) || (n.level || '').includes(query);
  const dfs = (n) => {
    const id = String(n._id);
    let ok = matches(n);
    (children.get(id) || []).forEach(c => { if (dfs(c)) ok = true; });
    if (ok) {
      keep.add(id);
      // also keep ancestors
      let p = n.parent_id ? String(n.parent_id) : null;
      while (p && byId.has(p)) { if (keep.has(p)) break; keep.add(p); p = byId.get(p).parent_id ? String(byId.get(p).parent_id) : null; }
    }
    return ok;
  };
  roots.forEach(dfs);
  return flat.filter(i => keep.has(String(i._id)));
};
