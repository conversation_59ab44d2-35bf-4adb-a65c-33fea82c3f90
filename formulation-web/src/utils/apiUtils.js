/**
 * Centralized API utilities for common request patterns
 */

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:7700/api';

/**
 * Base API request function with authentication
 */
export const apiRequest = async (endpoint, options = {}) => {
  const token = localStorage.getItem('token');
  
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    }
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers
    }
  });

  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Request failed' }));
    throw new Error(error.error || `HTTP ${response.status}: ${response.statusText}`);
  }

  const data = await response.json();
  return { success: true, data: data.data || data };
};

/**
 * Common project API operations
 */
export const projectApi = {
  // Get all projects with optional limit
  getAll: (limit = 100) => apiRequest(`/projects?limit=${limit}`),
  
  // Get project by ID
  getById: (id) => apiRequest(`/projects/${id}`),
  
  // Get project status
  getStatus: (id) => apiRequest(`/projects/${id}/status`),
  
  // Create new project
  create: (data) => apiRequest('/projects', { method: 'POST', body: JSON.stringify(data) }),
  
  // Update project
  update: (id, data) => apiRequest(`/projects/${id}`, { method: 'PUT', body: JSON.stringify(data) }),
  
  // Delete project
  delete: (id) => apiRequest(`/projects/${id}`, { method: 'DELETE' }),
  
  // Retry project generation
  retry: (id) => apiRequest(`/projects/${id}/retry`, { method: 'POST' }),
  
  // Archive project
  archive: (id) => apiRequest(`/projects/${id}/archive`, { method: 'POST' })
};

/**
 * Common playground API operations
 */
export const playgroundApi = {
  // Load playground data
  load: async (projectId) => {
    return apiRequest(`/playground/load/${projectId}`);
  },

  // Get playground status
  getStatus: async (projectId) => {
    return apiRequest(`/playground/status/${projectId}`);
  },

  // Send chat message
  chat: async (data) => {
    return apiRequest('/playground/chat', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  },

  // Generate formulation
  generate: async (data) => {
    return apiRequest('/playground/generate', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  },

  // Suggest notes
  suggestNotes: async (data) => {
    return apiRequest('/playground/suggest-notes', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }
};

/**
 * Common taxonomy API operations
 */
export const taxonomyApi = {
  // Get taxonomies by level
  getByLevel: async (level, parentId = null, status = 'active') => {
    const params = new URLSearchParams({ level, status });
    if (parentId) params.append('parent_id', parentId);
    return apiRequest(`/taxonomies?${params}`);
  },

  // Get taxonomy by slug
  getBySlug: async (slug) => {
    return apiRequest(`/taxonomies/${slug}`);
  },

  // Search taxonomies
  search: async (query, level = null) => {
    const params = new URLSearchParams({ query });
    if (level) params.append('level', level);
    return apiRequest(`/taxonomies/search?${params}`);
  }
};

/**
 * Common admin API operations
 */
export const adminApi = {
  // Taxonomy management
  taxonomies: {
    getAll: () => apiRequest('/admin/taxonomies'),
    create: (data) => apiRequest('/admin/taxonomies', {
      method: 'POST',
      body: JSON.stringify(data)
    }),
    update: (id, data) => apiRequest(`/admin/taxonomies/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    }),
    delete: (id) => apiRequest(`/admin/taxonomies/${id}`, { method: 'DELETE' })
  },

  // Bindings management
  bindings: {
    getByTarget: (level, slug) => apiRequest(`/admin/bindings?level=${encodeURIComponent(level)}&slug=${encodeURIComponent(slug)}`),
    create: (data) => apiRequest('/admin/bindings', {
      method: 'POST',
      body: JSON.stringify(data)
    }),
    update: (id, data) => apiRequest(`/admin/bindings/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    }),
    delete: (id) => apiRequest(`/admin/bindings/${id}`, { method: 'DELETE' })
  }
};

/**
 * Error handling wrapper for API calls
 */
export const withErrorHandling = async (apiCall, errorMessage = 'API request failed') => {
  try {
    return await apiCall();
  } catch (error) {
    console.error(errorMessage, error);
    throw error;
  }
};

/**
 * Batch API requests
 */
export const batchRequests = async (requests) => {
  try {
    const results = await Promise.allSettled(requests);
    return results.map((result, index) => ({
      index,
      success: result.status === 'fulfilled',
      data: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason : null
    }));
  } catch (error) {
    console.error('Batch request failed:', error);
    throw error;
  }
};

export default {
  apiRequest,
  project: projectApi,
  playground: playgroundApi,
  taxonomy: taxonomyApi,
  admin: adminApi,
  withErrorHandling,
  batchRequests
};