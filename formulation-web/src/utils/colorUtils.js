/**
 * Color utility functions for consistent styling across components
 */

/**
 * Get color classes for stroke and text (used in charts/meters)
 * @param {string} color - The color name
 * @returns {string} - Space-separated stroke and text classes
 */
export const getStrokeTextColorClasses = (color) => {
  switch(color) {
    case 'green': return 'stroke-green-500 text-green-600';
    case 'blue': return 'stroke-blue-500 text-blue-600';
    case 'purple': return 'stroke-purple-500 text-purple-600';
    case 'orange': return 'stroke-orange-500 text-orange-600';
    case 'red': return 'stroke-red-500 text-red-600';
    default: return 'stroke-gray-500 text-gray-600';
  }
};

/**
 * Get color classes for interactive elements (cards, buttons)
 * @param {string} color - The color name
 * @returns {string} - Space-separated classes for interactive elements
 */
export const getInteractiveColorClasses = (color) => {
  switch(color) {
    case 'green': return 'text-green-600 bg-green-50 border-green-200 ring-green-500';
    case 'blue': return 'text-blue-600 bg-blue-50 border-blue-200 ring-blue-500';
    case 'purple': return 'text-purple-600 bg-purple-50 border-purple-200 ring-purple-500';
    case 'orange': return 'text-orange-600 bg-orange-50 border-orange-200 ring-orange-500';
    case 'red': return 'text-red-600 bg-red-50 border-red-200 ring-red-500';
    default: return 'text-green-600 bg-green-50 border-green-200 ring-green-500';
  }
};

/**
 * Get detailed color classes object for complex components
 * @param {string} color - The color name
 * @returns {object} - Object with detailed color class properties
 */
export const getDetailedColorClasses = (color) => {
  switch(color) {
    case 'blue': return {
      border: 'border-blue-200',
      bg: 'bg-blue-50',
      text: 'text-blue-600',
      selectedBorder: 'border-blue-500',
      selectedBg: 'bg-blue-100',
      iconBg: 'bg-blue-100'
    };
    case 'green': return {
      border: 'border-green-200',
      bg: 'bg-green-50',
      text: 'text-green-600',
      selectedBorder: 'border-green-500',
      selectedBg: 'bg-green-100',
      iconBg: 'bg-green-100'
    };
    case 'purple': return {
      border: 'border-purple-200',
      bg: 'bg-purple-50',
      text: 'text-purple-600',
      selectedBorder: 'border-purple-500',
      selectedBg: 'bg-purple-100',
      iconBg: 'bg-purple-100'
    };
    case 'orange': return {
      border: 'border-orange-200',
      bg: 'bg-orange-50',
      text: 'text-orange-600',
      selectedBorder: 'border-orange-500',
      selectedBg: 'bg-orange-100',
      iconBg: 'bg-orange-100'
    };
    case 'red': return {
      border: 'border-red-200',
      bg: 'bg-red-50',
      text: 'text-red-600',
      selectedBorder: 'border-red-500',
      selectedBg: 'bg-red-100',
      iconBg: 'bg-red-100'
    };
    case 'pink': return {
      border: 'border-pink-200',
      bg: 'bg-pink-50',
      text: 'text-pink-600',
      selectedBorder: 'border-pink-500',
      selectedBg: 'bg-pink-100',
      iconBg: 'bg-pink-100'
    };
    case 'yellow': return {
      border: 'border-yellow-200',
      bg: 'bg-yellow-50',
      text: 'text-yellow-600',
      selectedBorder: 'border-yellow-500',
      selectedBg: 'bg-yellow-100',
      iconBg: 'bg-yellow-100'
    };
    default: return {
      border: 'border-gray-200',
      bg: 'bg-gray-50',
      text: 'text-gray-600',
      selectedBorder: 'border-gray-500',
      selectedBg: 'bg-gray-100',
      iconBg: 'bg-gray-100'
    };
  }
};

export const getStatusConfig = (status) => {
  switch (status) {
    case 'completed':
      return {
        colorClasses: 'bg-green-100 text-green-800 border-green-200',
        text: 'Completed'
      };
    case 'processing':
      return {
        colorClasses: 'bg-blue-100 text-blue-800 border-blue-200',
        text: 'Processing'
      };
    case 'failed':
      return {
        colorClasses: 'bg-red-100 text-red-800 border-red-200',
        text: 'Failed'
      };
    case 'queued':
      return {
        colorClasses: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        text: 'Queued'
      };
    case 'ready':
      return {
        colorClasses: 'bg-green-100 text-green-800 border-green-200',
        text: 'Ready'
      };
    case 'generating':
      return {
        colorClasses: 'bg-blue-100 text-blue-800 border-blue-200',
        text: 'Generating'
      };
    case 'closed':
      return {
        colorClasses: 'bg-gray-100 text-gray-800 border-gray-200',
        text: 'Closed'
      };
    case 'archived':
      return {
        colorClasses: 'bg-gray-100 text-gray-600 border-gray-200',
        text: 'Archived'
      };
    default:
      return {
        colorClasses: 'bg-gray-100 text-gray-800 border-gray-200',
        text: status || 'Unknown'
      };
  }
};

// Shared status mapping utility
export const mapStatus = (status) => {
  if (status === 'ready') return 'ready';
  if (status === 'generating') return 'generating';
  if (status === 'queued') return 'queued';
  if (status === 'failed') return 'failed';
  if (status === 'closed') return 'closed';
  if (status === 'archived') return 'archived';
  return status || 'queued';
};