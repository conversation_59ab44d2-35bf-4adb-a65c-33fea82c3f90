import {
  FlaskConical,
  ShieldCheck,
  Beaker,
  Target,
  DollarSign,
  Smile,
  Droplet,
  Package,
  Sparkles,
  Gauge,
  Calendar,
  BadgeCheck,
  Sliders
} from 'lucide-react';

// Keyword → Icon mapping (lowercase match contains)
const ICON_RULES = [
  { keys: ['formulation'], Icon: FlaskConical },
  { keys: ['compliance', 'guardrail'], Icon: ShieldCheck },
  { keys: ['ingredients', 'ingredient', 'actives', 'active'], Icon: Beaker },
  { keys: ['goals', 'goal', 'objectives', 'objective'], Icon: Target },
  { keys: ['budget', 'cost', 'pricing', 'price'], Icon: DollarSign },
  { keys: ['skin', 'skin type'], Icon: Smile },
  { keys: ['phases', 'phase', 'oil', 'water'], Icon: Droplet },
  { keys: ['packaging', 'package'], Icon: Package },
  { keys: ['fragrance', 'perfume', 'aroma'], Icon: Sparkles },
  { keys: ['ph', 'acidity'], Icon: Gauge },
  { keys: ['timeline', 'schedule', 'time'], Icon: Calendar },
  { keys: ['certification', 'certifications', 'certificate'], Icon: BadgeCheck }
];

export const pickIconFromTitle = (title = '') => {
  const t = String(title).toLowerCase();
  for (const rule of ICON_RULES) {
    if (rule.keys.some(k => t.includes(k))) return rule.Icon;
  }
  return Sliders; // default
};

