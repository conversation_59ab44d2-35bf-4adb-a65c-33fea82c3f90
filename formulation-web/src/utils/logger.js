// Frontend logging utility
// This handles both console logging and optional file logging for the React application

class FrontendLogger {
  constructor() {
    this.logLevel = import.meta.env.VITE_LOG_LEVEL || 'info';
    this.enableFileLogging = import.meta.env.VITE_LOG_TO_FILE === 'true';
    this.logFilePath = import.meta.env.VITE_LOG_FILE_PATH || '../logs/frontend.log';
    this.enableDebugMode = import.meta.env.VITE_ENABLE_DEBUG_MODE === 'true';
    
    // Log levels in order of priority
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
    
    this.currentLevel = this.levels[this.logLevel] || this.levels.info;
  }

  // Format log message with timestamp and context
  formatMessage(level, message, context = {}) {
    const timestamp = new Date().toISOString();
    const formattedMessage = {
      timestamp,
      level,
      message,
      service: 'agrizy-formulation-web',
      environment: import.meta.env.VITE_NODE_ENV || 'development',
      url: window.location.href,
      userAgent: navigator.userAgent,
      ...context
    };
    
    return formattedMessage;
  }

  // Check if log level should be output
  shouldLog(level) {
    return this.levels[level] <= this.currentLevel;
  }

  // Console logging with color coding
  logToConsole(level, formattedMessage) {
    const consoleMessage = `${formattedMessage.timestamp} [${level.toUpperCase()}]: ${formattedMessage.message}`;
    
    switch (level) {
      case 'error':
        console.error(consoleMessage, formattedMessage);
        break;
      case 'warn':
        console.warn(consoleMessage, formattedMessage);
        break;
      case 'info':
        console.info(consoleMessage, formattedMessage);
        break;
      case 'debug':
        if (this.enableDebugMode) {
          console.debug(consoleMessage, formattedMessage);
        }
        break;
      default:
        console.log(consoleMessage, formattedMessage);
    }
  }

  // Send logs to backend for file storage
  async logToFile(formattedMessage) {
    if (!this.enableFileLogging) return;
    
    try {
      // Send log to backend endpoint for file storage
      await fetch(`${import.meta.env.VITE_API_BASE_URL}/logs/frontend`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formattedMessage)
      });
    } catch (error) {
      // Silently fail file logging to avoid infinite loops
      console.warn('Failed to log to file:', error.message);
    }
  }

  // Core logging method
  log(level, message, context = {}) {
    if (!this.shouldLog(level)) return;
    
    const formattedMessage = this.formatMessage(level, message, context);
    
    // Always log to console
    this.logToConsole(level, formattedMessage);
    
    // Optionally log to file via backend
    this.logToFile(formattedMessage);
  }

  // Convenience methods
  error(message, context = {}) {
    this.log('error', message, context);
  }

  warn(message, context = {}) {
    this.log('warn', message, context);
  }

  info(message, context = {}) {
    this.log('info', message, context);
  }

  debug(message, context = {}) {
    this.log('debug', message, context);
  }

  // Application-specific logging methods
  logUserAction(action, details = {}) {
    this.info(`User Action: ${action}`, {
      category: 'user-action',
      action,
      ...details
    });
  }

  logApiCall(method, url, status, duration, details = {}) {
    const level = status >= 400 ? 'error' : 'info';
    this.log(level, `API Call: ${method} ${url}`, {
      category: 'api-call',
      method,
      url,
      status,
      duration: `${duration}ms`,
      ...details
    });
  }

  logFormulationEvent(event, details = {}) {
    this.info(`Formulation Event: ${event}`, {
      category: 'formulation',
      event,
      ...details
    });
  }

  logAuthEvent(action, success, details = {}) {
    this.info(`Auth Event: ${action}`, {
      category: 'authentication',
      action,
      success,
      ...details
    });
  }

  logError(error, context = {}) {
    this.error(error.message, {
      category: 'application-error',
      stack: error.stack,
      name: error.name,
      ...context
    });
  }
}

// Create singleton instance
const logger = new FrontendLogger();

// Global error handler
window.addEventListener('error', (event) => {
  logger.logError(event.error, {
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno
  });
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  logger.error('Unhandled Promise Rejection', {
    category: 'unhandled-rejection',
    reason: event.reason,
    stack: event.reason?.stack
  });
});

export default logger;