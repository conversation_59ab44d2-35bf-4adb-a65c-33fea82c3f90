// Date formatting utilities

// Format date to readable string (e.g., "Jan 15, 2024")
export const formatDate = (date) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
};

// Format date to relative time (e.g., "2 hours ago")
export const formatTimeAgo = (date) => {
  if (!date) return 'Never';
  const seconds = Math.floor((new Date() - new Date(date)) / 1000);
  const intervals = {
    year: 31536000,
    month: 2592000,
    week: 604800,
    day: 86400,
    hour: 3600,
    minute: 60
  };

  for (const [unit, value] of Object.entries(intervals)) {
    const interval = Math.floor(seconds / value);
    if (interval >= 1) {
      return `${interval} ${unit}${interval === 1 ? '' : 's'} ago`;
    }
  }
  return 'Just now';
};

// Get status badge CSS classes for project status
export const getStatusBadgeClass = (status) => {
  switch (status) {
    case 'queued':
      return 'bg-amber-50 text-amber-700 border-amber-200';
    case 'generating':
      return 'bg-blue-50 text-blue-700 border-blue-200';
    case 'ready':
      return 'bg-green-50 text-green-700 border-green-200';
    case 'failed':
      return 'bg-red-50 text-red-700 border-red-200';
    case 'closed':
      return 'bg-gray-50 text-gray-600 border-gray-200';
    case 'archived':
      return 'bg-purple-50 text-purple-700 border-purple-200';
    default:
      return 'bg-gray-50 text-gray-600 border-gray-200';
  }
};