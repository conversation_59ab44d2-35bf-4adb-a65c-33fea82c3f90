/**
 * Configuration API Service
 * Handles all MongoDB-driven configuration data fetching
 */

import { apiRequest } from '../utils/apiUtils.js';

/**
 * Helper function to extract data from API response
 */
const extractData = async (apiCall) => {
  const response = await apiCall;
  return response.data;
};

/**
 * Taxonomy API calls
 */
export const taxonomyApi = {
  // Get taxonomies by level (industry, category, sub_category)
  getByLevel: async (level, parentId = null, status = 'active') => {
    const params = new URLSearchParams({ level, status });
    if (parentId) params.append('parent_id', parentId);
    return extractData(apiRequest(`/taxonomies?${params}`));
  },

  // Get specific taxonomy by slug
  getBySlug: async (slug, fullHierarchy = false) => {
    const params = fullHierarchy ? '?full_hierarchy=true' : '';
    return extractData(apiRequest(`/taxonomies/${slug}${params}`));
  },

  // Search taxonomies
  search: async (query, level = null) => {
    const params = new URLSearchParams({ query });
    if (level) params.append('level', level);
    return extractData(apiRequest(`/taxonomies/search?${params}`));
  }
};

/**
 * Parameter API calls
 */
export const parameterApi = {
  // Get parameter bindings for a specific target
  getBindings: async (level, slug) => {
    const params = new URLSearchParams({ level, slug });
    return extractData(apiRequest(`/param-bindings?${params}`));
  },

  // Get answer sets for parameters
  getAnswerSets: async (keys) => {
    const keyList = Array.isArray(keys) ? keys.join(',') : keys;
    const params = new URLSearchParams({ keys: keyList });
    return extractData(apiRequest(`/param-answers?${params}`));
  }
};

/**
 * UI Flow API calls
 */
export const uiFlowApi = {
  // Get UI flow configuration
  getFlow: async (slug) => {
    return extractData(apiRequest(`/ui-flows/${slug}`));
  },

  // Get specific step in a UI flow
  getFlowStep: async (flowSlug, stepSlug) => {
    return extractData(apiRequest(`/ui-flows/${flowSlug}/step/${stepSlug}`));
  }
};

/**
 * Guardrails API calls
 */
export const guardrailsApi = {
  // Evaluate guardrails for given parameters
  evaluate: async (data) => {
    return extractData(apiRequest('/guardrails/evaluate', {
      method: 'POST',
      body: JSON.stringify(data)
    }));
  },

  // Get applicable guardrails
  getApplicable: async (industry, category, subCategory) => {
    const params = new URLSearchParams({
      industry,
      category,
      sub_category: subCategory
    });
    return extractData(apiRequest(`/guardrails?${params}`));
  }
};

/**
 * Helper function to cache API responses
 */
const cache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

export const cachedApiRequest = async (key, fetcher) => {
  const cached = cache.get(key);
  
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }

  const data = await fetcher();
  cache.set(key, { data, timestamp: Date.now() });
  return data;
};

// Clear cache on logout
export const clearConfigCache = () => {
  cache.clear();
};

export default {
  taxonomy: taxonomyApi,
  parameter: parameterApi,
  uiFlow: uiFlowApi,
  guardrails: guardrailsApi,
  clearCache: clearConfigCache
};