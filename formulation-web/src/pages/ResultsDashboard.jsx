import React, { useState, useEffect } from 'react';
import { 
  ArrowLeft, 
  Download, 
  Share2, 
  Edit3, 
  Save, 
  Beaker, 
  Target, 
  DollarSign, 
  Leaf, 
  Shield, 
  TrendingUp,
  Clock,
  Users,
  FileText,
  Settings,
  Globe,
  Star,
  CheckCircle,
  Award,
  Package,
  Zap,
  Factory,
  ClipboardList,
  Wrench,
  Timer,
  AlertTriangle
} from 'lucide-react';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import Breadcrumbs from '../components/Breadcrumbs';
import { getStrokeTextColorClasses } from '../utils/colorUtils';

const RecipeCard = ({ recipe, isSelected, onSelect, type = 'primary' }) => {
  const getCardStyle = () => {
    switch (type) {
      case 'primary':
        return 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-300 shadow-lg';
      case 'geographic':
        return 'bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-300 shadow-md';
      case 'flavor':
        return 'bg-gradient-to-br from-purple-50 to-pink-50 border-purple-300 shadow-md';
      case 'premium':
        return 'bg-gradient-to-br from-amber-50 to-yellow-50 border-amber-300 shadow-md';
      case 'value':
        return 'bg-gradient-to-br from-gray-50 to-slate-50 border-gray-300 shadow-sm';
      default:
        return 'bg-white border-gray-200';
    }
  };

  const getTypeIcon = () => {
    switch (type) {
      case 'geographic':
        return Globe;
      case 'flavor':
        return Star;
      case 'premium':
        return Award;
      case 'value':
        return DollarSign;
      default:
        return Beaker;
    }
  };

  const getTypeBadge = () => {
    switch (type) {
      case 'primary':
        return { label: 'PRIMARY', color: 'bg-green-500 text-white' };
      case 'geographic':
        return { label: 'REGIONAL', color: 'bg-blue-500 text-white' };
      case 'flavor':
        return { label: 'FLAVOR', color: 'bg-purple-500 text-white' };
      case 'premium':
        return { label: 'PREMIUM', color: 'bg-amber-500 text-white' };
      case 'value':
        return { label: 'VALUE', color: 'bg-gray-500 text-white' };
      default:
        return { label: 'STANDARD', color: 'bg-gray-400 text-white' };
    }
  };

  const TypeIcon = getTypeIcon();
  const typeBadge = getTypeBadge();

  return (
    <div 
      className={`p-6 rounded-xl border-2 cursor-pointer transition-all transform hover:scale-[1.02] ${getCardStyle()} ${
        isSelected ? 'ring-4 ring-green-400 border-green-500 scale-[1.02]' : 'hover:border-green-400'
      } min-h-[400px] flex flex-col`}
      onClick={onSelect}
    >
      {/* Header with Type Badge */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <TypeIcon className="w-6 h-6 text-gray-700" />
          <span className={`px-3 py-1.5 rounded-full text-xs font-bold ${typeBadge.color}`}>
            {typeBadge.label}
          </span>
        </div>
        {isSelected && <CheckCircle className="w-6 h-6 text-green-600" />}
      </div>

      {/* Recipe Name */}
      <h3 className="text-xl font-bold text-gray-900 mb-4 leading-tight">{recipe.name}</h3>
      
      {/* Key Differences - Most Prominent */}
      {recipe.key_differences && (
        <div className="mb-4 p-4 bg-white/80 rounded-lg border border-gray-200 flex-grow">
          <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Key Advantage</div>
          <div className="text-sm font-medium text-gray-800 leading-relaxed">{recipe.key_differences}</div>
        </div>
      )}

      {/* Target Market */}
      {recipe.targetMarket && (
        <div className="flex items-start mb-4">
          <Users className="w-4 h-4 text-gray-500 mr-3 mt-0.5 flex-shrink-0" />
          <span className="text-sm text-gray-600 leading-relaxed">{recipe.targetMarket}</span>
        </div>
      )}

      {/* Traditional Benefits or Innovation Highlights */}
      {recipe.traditional_benefits && (
        <div className="mb-4">
          <div className="text-xs font-semibold text-blue-600 mb-2 uppercase tracking-wide">Traditional Benefits</div>
          <div className="text-sm text-gray-600 leading-relaxed">{recipe.traditional_benefits}</div>
        </div>
      )}

      {recipe.innovation_highlights && (
        <div className="mb-4">
          <div className="text-xs font-semibold text-purple-600 mb-2 uppercase tracking-wide">Innovation</div>
          <div className="text-sm text-gray-600 leading-relaxed">{recipe.innovation_highlights}</div>
        </div>
      )}

      {recipe.cost_analysis && (
        <div className="grid grid-cols-2 gap-4 mb-6 mt-auto">
          <div className="text-center p-3 bg-white/60 rounded-lg">
            <div className="text-xs text-gray-500 font-medium mb-1">Raw Materials</div>
            <div className="text-lg font-bold text-green-600">
              ₹{recipe.cost_analysis.raw_material_cost?.toLocaleString() || 'N/A'}
            </div>
          </div>
          <div className="text-center p-3 bg-white/60 rounded-lg">
            <div className="text-xs text-gray-500 font-medium mb-1">Suggested Retail</div>
            <div className="text-lg font-bold text-blue-600">
              ₹{recipe.cost_analysis.suggested_retail?.toLocaleString() || 'N/A'}
            </div>
          </div>
        </div>
      )}

      {recipe.scores && (
        <div className="grid grid-cols-3 gap-3 mt-4">
          {Object.entries(recipe.scores).slice(0, 3).map(([key, value]) => (
            <div key={key} className="text-center p-2 bg-white/50 rounded-lg">
              <div className="text-lg font-bold text-gray-900">{value}</div>
              <div className="text-xs text-gray-600 font-medium leading-tight">
                {key === 'nutrition' ? 'Nutrition'
                : key === 'sustainability' ? 'Sustain'
                : key === 'cost_efficiency' ? 'Cost Eff'
                : key === 'compliance' ? 'Comply'
                : key === 'market_appeal' ? 'Market'
                : key.replace(/([A-Z])/g, ' $1').trim()}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const InteractiveSmartMeter = ({ label, value, max = 100, color = 'green', icon: Icon, unit = '%' }) => {
  const [animatedValue, setAnimatedValue] = useState(0);

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedValue(value);
    }, 500);
    return () => clearTimeout(timer);
  }, [value]);

  const percentage = (animatedValue / max) * 100;
  
  const colorClasses = getStrokeTextColorClasses(color);

  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-700">{label}</h3>
        {Icon && <Icon className={`w-5 h-5 ${colorClasses.split(' ')[1]}`} />}
      </div>
      
      <div className="relative w-24 h-24 mx-auto mb-4">
        <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
          <circle
            cx="50"
            cy="50"
            r="40"
            stroke="currentColor"
            strokeWidth="8"
            fill="none"
            className="text-gray-200"
          />
          <circle
            cx="50"
            cy="50"
            r="40"
            stroke="currentColor"
            strokeWidth="8"
            fill="none"
            strokeDasharray={`${2 * Math.PI * 40}`}
            strokeDashoffset={`${2 * Math.PI * 40 * (1 - percentage / 100)}`}
            className={`${colorClasses.split(' ')[0]} transition-all duration-1000 ease-out`}
            strokeLinecap="round"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <span className={`text-xl font-bold ${colorClasses.split(' ')[1]}`}>
            {Math.round(animatedValue)}{unit}
          </span>
        </div>
      </div>
    </div>
  );
};

const ResultsDashboard = () => {
  const { setCurrentStep, formData } = useApp();
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [selectedRecipeIndex, setSelectedRecipeIndex] = useState(0);
  const [activeTab, setActiveTab] = useState('recipes'); // 'recipes', 'details', 'sustainability'

  // Use the generated formulation from API, or fallback to mock data for development
  const generatedFormulation = formData.generatedFormulation;
  const claudeResponse = generatedFormulation?.claudeResponse;
  
  // Completely data-driven formulation state from Claude AI response
  const [formulation, setFormulation] = useState(() => {
    // If we have Claude AI response, use the primary recipe
    if (claudeResponse?.recipes?.[0]) {
      const primaryRecipe = claudeResponse.recipes[0];
      return {
        name: primaryRecipe.name,
        description: primaryRecipe.description,
        industry: formData.industry || 'beverages',
        productType: formData.productType || 'custom',
        targetMarket: primaryRecipe.targetMarket,
        totalCost: primaryRecipe.cost_analysis?.raw_material_cost || 0,
        estimatedRetail: primaryRecipe.cost_analysis?.suggested_retail || 0,
        batchSize: primaryRecipe.production_specs?.batch_size || '1000L',
        shelfLife: primaryRecipe.production_specs?.shelf_life || '12 months',
        ingredients: primaryRecipe.ingredients || [],
        scores: primaryRecipe.scores || {},
        nutritionalProfile: primaryRecipe.nutritional_profile || {},
        complianceDetails: primaryRecipe.compliance_details || {},
        costAnalysis: primaryRecipe.cost_analysis || {},
        productionSpecs: primaryRecipe.production_specs || {}
      };
    }
    
    // If we have legacy generated formulation data
    if (generatedFormulation) {
      return {
        name: generatedFormulation.name,
        description: generatedFormulation.description,
        industry: formData.industry || 'beverages',
        productType: formData.productType || 'custom',
        totalCost: generatedFormulation.totalCost,
        estimatedRetail: generatedFormulation.estimatedRetail,
        batchSize: generatedFormulation.batchSize,
        shelfLife: generatedFormulation.shelfLife,
        ingredients: generatedFormulation.ingredients,
        scores: generatedFormulation.scores,
        nutritionalProfile: generatedFormulation.nutritionalProfile,
        complianceDetails: generatedFormulation.complianceDetails
      };
    }
    
    // Minimal fallback when no data is available
    return {
      name: 'Custom Formulation',
      description: 'AI-generated formulation based on your requirements',
      industry: formData.industry || 'beverages',
      productType: formData.productType || 'custom',
      totalCost: 0,
      estimatedRetail: 0,
      batchSize: '1000L',
      shelfLife: '12 months',
      ingredients: [],
      scores: {},
      nutritionalProfile: {},
      complianceDetails: {}
    };
  });

  // Get all available recipes from Claude AI response for recipe cards
  const availableRecipes = claudeResponse?.recipes || [formulation];
  const recipeVariations = claudeResponse?.variations || [];
  const allRecipes = [...availableRecipes, ...recipeVariations];

  // Get sustainability and market positioning data from Claude AI
  const sustainabilityReport = claudeResponse?.sustainability_report || {};
  const marketPositioning = claudeResponse?.market_positioning || {};

  const handleBackToDashboard = () => {
    setCurrentStep('dashboard');
  };

  const handleEdit = () => {
    setIsEditing(!isEditing);
  };

  const handleSave = () => {
    setIsEditing(false);
    // Here you would typically save to backend
    console.log('Saving formulation:', formulation);
  };

  const handleExport = (format) => {
    alert(`Exporting formulation as ${format.toUpperCase()}...`);
  };

  const getTotalPercentage = () => {
    return formulation.ingredients.reduce((sum, ing) => sum + ing.percentage, 0);
  };

  // Handle recipe selection from recipe cards
  const handleRecipeSelect = (recipeIndex) => {
    setSelectedRecipeIndex(recipeIndex);
    const selectedRecipe = allRecipes[recipeIndex];
    
    if (selectedRecipe) {
      setFormulation({
        name: selectedRecipe.name,
        description: selectedRecipe.description,
        industry: formData.industry || 'beverages',
        productType: formData.productType || 'custom',
        targetMarket: selectedRecipe.targetMarket || selectedRecipe.target_geography,
        totalCost: selectedRecipe.cost_analysis?.raw_material_cost || 0,
        estimatedRetail: selectedRecipe.cost_analysis?.suggested_retail || 0,
        batchSize: selectedRecipe.production_specs?.batch_size || '1000L',
        shelfLife: selectedRecipe.production_specs?.shelf_life || '12 months',
        ingredients: selectedRecipe.ingredients || [],
        scores: selectedRecipe.scores || {},
        nutritionalProfile: selectedRecipe.nutritional_profile || {},
        complianceDetails: selectedRecipe.compliance_details || {},
        costAnalysis: selectedRecipe.cost_analysis || {},
        productionSpecs: selectedRecipe.production_specs || {}
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button 
              onClick={handleBackToDashboard}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <Breadcrumbs items={[{ label: 'Dashboard', to: '/dashboard' }, { label: 'Results' }]} />
              <h1 className="text-2xl font-bold text-gray-900">{formulation.name}</h1>
              <p className="text-gray-600">{formulation.description}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button 
              onClick={handleEdit}
              className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                isEditing 
                  ? 'bg-orange-100 text-orange-700 hover:bg-orange-200' 
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
              }`}
            >
              <Edit3 className="w-4 h-4 mr-2" />
              {isEditing ? 'Editing' : 'Edit'}
            </button>
            {isEditing && (
              <button 
                onClick={handleSave}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Save className="w-4 h-4 mr-2" />
                Save
              </button>
            )}
            <button 
              onClick={() => handleExport('pdf')}
              className="flex items-center px-4 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors"
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </button>
            <button className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
              <Share2 className="w-5 h-5" />
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Recipe Selection and Ingredient Comparison Section */}
        {allRecipes.length > 1 && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-8">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-2">Agrizy Recommends</h2>
                <p className="text-lg text-gray-600">Expertly curated formulation alternatives tailored for your market</p>
              </div>
              <div className="flex items-center space-x-6">
                <div className="text-center px-4 py-2 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="text-lg font-bold text-blue-600">{availableRecipes.length}</div>
                  <div className="text-xs text-blue-600 font-medium">Primary Recipe{availableRecipes.length !== 1 ? 's' : ''}</div>
                </div>
                <div className="text-center px-4 py-2 bg-green-50 rounded-lg border border-green-200">
                  <div className="text-lg font-bold text-green-600">{recipeVariations.length}</div>
                  <div className="text-xs text-green-600 font-medium">Variation{recipeVariations.length !== 1 ? 's' : ''}</div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-12 gap-8">
              {/* Recipe Variations - Left Side */}
              <div className="col-span-7">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {allRecipes.map((recipe, index) => {
                    // Determine recipe type based on properties for better visual differentiation
                    let recipeType = 'primary';
                    if (recipe.variation_type === 'premium' || recipe.variation_type === 'innovative') recipeType = 'premium';
                    else if (recipe.variation_type === 'regional' || recipe.target_geography === 'india_traditional') recipeType = 'geographic';
                    else if (recipe.variation_type === 'value' || recipe.target_geography === 'mass_market') recipeType = 'value';
                    else if (recipe.variation_type === 'flavor') recipeType = 'flavor';
                    
                    return (
                      <RecipeCard
                        key={`recipe-${index}`}
                        recipe={recipe}
                        type={recipeType}
                        isSelected={selectedRecipeIndex === index}
                        onSelect={() => handleRecipeSelect(index)}
                      />
                    );
                  })}
                </div>
              </div>

              {/* Ingredient Formulation - Right Side */}
              <div className="col-span-5">
                {activeTab === 'recipes' && (
                  <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-xl shadow-lg border-2 border-green-200 p-6 h-full">
                    <div className="flex items-center mb-4">
                      <Beaker className="w-6 h-6 text-green-600 mr-3" />
                      <h3 className="text-xl font-bold text-gray-900">Selected Recipe Ingredients</h3>
                    </div>
                    <div className="text-sm text-gray-600 bg-white px-3 py-1 rounded-full border border-gray-200 mb-4 inline-block">
                      {claudeResponse?.recipes?.[selectedRecipeIndex] ? (
                        <>{claudeResponse.recipes[selectedRecipeIndex].name}</>
                      ) : (
                        <>Total: {getTotalPercentage()}% | Batch: {formulation.batchSize}</>
                      )}
                    </div>
                  
                    <div className="overflow-y-auto max-h-96 bg-white rounded-lg border border-gray-200 shadow-inner">
                      <table className="w-full">
                        <thead className="bg-gradient-to-r from-green-500 to-blue-500 text-white sticky top-0">
                          <tr>
                            <th className="px-4 py-3 text-left text-sm font-semibold">Ingredient</th>
                            <th className="px-4 py-3 text-center text-sm font-semibold">%</th>
                            <th className="px-4 py-3 text-right text-sm font-semibold">Cost (₹)</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-100">
                          {((claudeResponse?.recipes?.[selectedRecipeIndex]?.ingredients) || formulation.ingredients).map((ingredient, index) => (
                            <tr key={index} className="hover:bg-green-50 transition-colors">
                              <td className="px-4 py-3">
                                <div className="font-medium text-gray-900 text-sm leading-tight">{ingredient.name}</div>
                                {ingredient.function && (
                                  <div className="text-xs text-gray-500 mt-1">{ingredient.function}</div>
                                )}
                              </td>
                              <td className="px-4 py-3 text-center">
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-bold bg-green-100 text-green-800">
                                  {ingredient.percentage}%
                                </span>
                              </td>
                              <td className="px-4 py-3 text-right">
                                <div className="font-semibold text-gray-900">
                                  ₹{ingredient.cost_per_kg ? (ingredient.cost_per_kg * ingredient.percentage / 100 * 10).toFixed(0) : ingredient.cost}
                                </div>
                                {ingredient.cost_per_kg && (
                                  <div className="text-xs text-gray-500">
                                    ₹{ingredient.cost_per_kg}/kg
                                  </div>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Show message if no recipes available */}
            {allRecipes.length === 0 && (
              <div className="text-center py-12">
                <Beaker className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Recipes Available</h3>
                <p className="text-gray-500">Generate a formulation to see recipe options here.</p>
              </div>
            )}
          </div>
        )}

        {/* Tabs for detailed view */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'recipes', label: 'Recipe Details', icon: Beaker },
                { id: 'manufacturing', label: 'Manufacturing', icon: Factory },
                { id: 'bom', label: 'Bill of Materials', icon: ClipboardList },
                { id: 'details', label: 'Production Specs', icon: Settings },
                { id: 'sustainability', label: 'Sustainability', icon: Leaf }
              ].map(tab => {
                const TabIcon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                      activeTab === tab.id
                        ? 'border-green-500 text-green-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <TabIcon className="w-4 h-4 mr-2" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        <div className="grid grid-cols-12 gap-8">
          {/* Left Column - Tab Content */}
          <div className="col-span-8 space-y-6">
            {/* Dynamic Tab Content */}
            {activeTab === 'recipes' && (
              <>
                {/* Performance Metrics */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-6">Performance Metrics</h2>
                  <div className="grid grid-cols-5 gap-4">
                    <InteractiveSmartMeter 
                      label="Nutrition Score" 
                      value={formulation.scores.nutrition} 
                      color="green" 
                      icon={Target}
                    />
                    <InteractiveSmartMeter 
                      label="Sustainability" 
                      value={formulation.scores.sustainability} 
                      color="green" 
                      icon={Leaf}
                    />
                    <InteractiveSmartMeter 
                      label="Cost Efficiency" 
                      value={formulation.scores.costEfficiency} 
                      color="orange" 
                      icon={DollarSign}
                    />
                    <InteractiveSmartMeter 
                      label="Compliance" 
                      value={formulation.scores.compliance} 
                      color="blue" 
                      icon={Shield}
                    />
                    <InteractiveSmartMeter 
                      label="Market Appeal" 
                      value={formulation.scores.marketAppeal} 
                      color="purple" 
                      icon={TrendingUp}
                    />
                  </div>
                </div>
              </>
            )}

            {activeTab === 'manufacturing' && allRecipes[selectedRecipeIndex] && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                  <Factory className="w-5 h-5 mr-2" />
                  Manufacturing Procedure
                </h2>
                {allRecipes[selectedRecipeIndex]?.production_specs?.detailed_manufacturing_procedure ? (
                  <div className="space-y-6">
                    {/* Pre-processing */}
                    <div className="bg-blue-50 rounded-lg p-4">
                      <h3 className="font-semibold text-blue-900 mb-3 flex items-center">
                        <Wrench className="w-4 h-4 mr-2" />
                        Pre-processing Steps
                      </h3>
                      <ol className="list-decimal list-inside space-y-2 text-sm text-blue-800">
                        {allRecipes[selectedRecipeIndex].production_specs.detailed_manufacturing_procedure.pre_processing?.map((step, index) => (
                          <li key={index}>{step}</li>
                        ))}
                      </ol>
                    </div>

                    {/* Mixing Procedure */}
                    <div className="bg-green-50 rounded-lg p-4">
                      <h3 className="font-semibold text-green-900 mb-3 flex items-center">
                        <Beaker className="w-4 h-4 mr-2" />
                        Mixing Procedure
                      </h3>
                      <ol className="list-decimal list-inside space-y-2 text-sm text-green-800">
                        {allRecipes[selectedRecipeIndex].production_specs.detailed_manufacturing_procedure.mixing_procedure?.map((step, index) => (
                          <li key={index}>{step}</li>
                        ))}
                      </ol>
                    </div>

                    {/* Quality Control */}
                    <div className="bg-yellow-50 rounded-lg p-4">
                      <h3 className="font-semibold text-yellow-900 mb-3 flex items-center">
                        <Shield className="w-4 h-4 mr-2" />
                        Quality Control Checkpoints
                      </h3>
                      <ul className="list-disc list-inside space-y-2 text-sm text-yellow-800">
                        {allRecipes[selectedRecipeIndex].production_specs.detailed_manufacturing_procedure.quality_control?.map((checkpoint, index) => (
                          <li key={index}>{checkpoint}</li>
                        ))}
                      </ul>
                    </div>

                    {/* Equipment Required */}
                    <div className="bg-purple-50 rounded-lg p-4">
                      <h3 className="font-semibold text-purple-900 mb-3 flex items-center">
                        <Settings className="w-4 h-4 mr-2" />
                        Equipment Required
                      </h3>
                      <div className="grid grid-cols-2 gap-2">
                        {allRecipes[selectedRecipeIndex].production_specs.detailed_manufacturing_procedure.equipment_required?.map((equipment, index) => (
                          <div key={index} className="text-sm text-purple-800 bg-white rounded px-3 py-1">{equipment}</div>
                        ))}
                      </div>
                    </div>

                    {/* Critical Control Points */}
                    <div className="bg-red-50 rounded-lg p-4">
                      <h3 className="font-semibold text-red-900 mb-3 flex items-center">
                        <AlertTriangle className="w-4 h-4 mr-2" />
                        Critical Control Points
                      </h3>
                      <ul className="list-disc list-inside space-y-2 text-sm text-red-800">
                        {allRecipes[selectedRecipeIndex].production_specs.detailed_manufacturing_procedure.critical_control_points?.map((ccp, index) => (
                          <li key={index}>{ccp}</li>
                        ))}
                      </ul>
                    </div>

                    {/* Time Estimate */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                        <Timer className="w-4 h-4 mr-2" />
                        Production Timeline
                      </h3>
                      <div className="text-2xl font-bold text-blue-600">
                        {allRecipes[selectedRecipeIndex].production_specs.detailed_manufacturing_procedure.total_time_hours} hours
                      </div>
                      <div className="text-sm text-gray-600">Total production time per batch</div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Factory className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">Manufacturing procedure details not available for this recipe.</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'bom' && allRecipes[selectedRecipeIndex] && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                  <ClipboardList className="w-5 h-5 mr-2" />
                  Bill of Materials (BOM)
                </h2>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                      <tr>
                        <th className="px-4 py-3 text-left text-sm font-semibold">Ingredient</th>
                        <th className="px-4 py-3 text-center text-sm font-semibold">Qty/Batch</th>
                        <th className="px-4 py-3 text-center text-sm font-semibold">MOQ</th>
                        <th className="px-4 py-3 text-center text-sm font-semibold">Lead Time</th>
                        <th className="px-4 py-3 text-center text-sm font-semibold">Grade</th>
                        <th className="px-4 py-3 text-right text-sm font-semibold">Cost</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-100">
                      {allRecipes[selectedRecipeIndex]?.ingredients?.map((ingredient, index) => (
                        <tr key={index} className="hover:bg-blue-50 transition-colors">
                          <td className="px-4 py-3">
                            <div className="font-medium text-gray-900">{ingredient.name}</div>
                            <div className="text-xs text-gray-500">{ingredient.function}</div>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <div className="font-semibold">
                              {ingredient.bom_details?.quantity_per_batch || `${(ingredient.percentage * 10).toFixed(0)}kg`}
                            </div>
                            <div className="text-xs text-gray-500">{ingredient.percentage}%</div>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <div className="text-sm font-medium">
                              {ingredient.bom_details?.minimum_order_quantity || '500kg'}
                            </div>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <div className="text-sm font-medium">
                              {ingredient.bom_details?.lead_time_days || '14'} days
                            </div>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full font-medium">
                              {ingredient.bom_details?.quality_grade || 'Food Grade'}
                            </span>
                          </td>
                          <td className="px-4 py-3 text-right">
                            <div className="font-semibold text-gray-900">
                              ₹{ingredient.cost_per_kg ? (ingredient.cost_per_kg * ingredient.percentage / 100 * 10).toFixed(0) : 'N/A'}
                            </div>
                            <div className="text-xs text-gray-500">₹{ingredient.cost_per_kg}/kg</div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                {/* BOM Summary */}
                <div className="mt-6 grid grid-cols-3 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">{allRecipes[selectedRecipeIndex]?.ingredients?.length || 0}</div>
                    <div className="text-sm text-blue-600 font-medium">Total Ingredients</div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">
                      ₹{allRecipes[selectedRecipeIndex]?.cost_analysis?.raw_material_cost?.toLocaleString() || 'N/A'}
                    </div>
                    <div className="text-sm text-green-600 font-medium">Raw Material Cost</div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {formulation.batchSize || '1000L'}
                    </div>
                    <div className="text-sm text-purple-600 font-medium">Batch Size</div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'details' && allRecipes[selectedRecipeIndex] && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Production Specifications</h2>
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium text-gray-900 mb-3">Manufacturing Details</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Batch Size:</span>
                        <span className="font-medium">{formulation.batchSize}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Shelf Life:</span>
                        <span className="font-medium">{formulation.shelfLife}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Storage:</span>
                        <span className="font-medium">{formulation.productionSpecs?.storage_conditions || 'Cool, dry place'}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 mb-3">Process Overview</h3>
                    <p className="text-sm text-gray-600">
                      {formulation.productionSpecs?.manufacturing_process || 'Advanced processing with quality control at each stage'}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'sustainability' && claudeResponse?.sustainability_report && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Sustainability Report</h2>
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium text-gray-900 mb-3">Environmental Impact</h3>
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm text-gray-600">Carbon Footprint:</span>
                        <p className="text-sm font-medium">{claudeResponse.sustainability_report.carbon_footprint}</p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-600">Sustainable Sourcing:</span>
                        <p className="text-sm font-medium">{claudeResponse.sustainability_report.sustainable_sourcing}</p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 mb-3">Certifications</h3>
                    <div className="flex flex-wrap gap-2">
                      {claudeResponse.sustainability_report.certifications?.map((cert, index) => (
                        <span key={index} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                          {cert}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}


            {/* Nutritional Profile */}
            {activeTab === 'recipes' && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-6">Nutritional Profile (per 250ml serving)</h2>
              <div className="grid grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{formulation.nutritionalProfile.calories}</div>
                  <div className="text-sm text-gray-600">Calories</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{formulation.nutritionalProfile.sugar}g</div>
                  <div className="text-sm text-gray-600">Sugar</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{formulation.nutritionalProfile.vitaminC}mg</div>
                  <div className="text-sm text-gray-600">Vitamin C</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{formulation.nutritionalProfile.antioxidants}</div>
                  <div className="text-sm text-gray-600">ORAC Units</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{formulation.nutritionalProfile.caffeine}mg</div>
                  <div className="text-sm text-gray-600">Caffeine</div>
                </div>
              </div>
            </div>
            )}
          </div>

          {/* Right Column - Project Info & Actions */}
          <div className="col-span-4 space-y-6">
            {/* Project Overview */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Overview</h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Industry</span>
                  <span className="font-medium capitalize">{formulation.industry}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Product Type</span>
                  <span className="font-medium capitalize">{formulation.productType.replace('-', ' ')}</span>
                </div>
                {claudeResponse?.recipes?.[selectedRecipeIndex] ? (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Batch Size</span>
                      <span className="font-medium">{claudeResponse.recipes[selectedRecipeIndex].production_specs?.batch_size}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Shelf Life</span>
                      <span className="font-medium">{claudeResponse.recipes[selectedRecipeIndex].production_specs?.shelf_life}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Raw Materials</span>
                      <span className="font-medium text-green-600">₹{claudeResponse.recipes[selectedRecipeIndex].cost_analysis?.raw_material_cost?.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Suggested Retail</span>
                      <span className="font-medium text-blue-600">₹{claudeResponse.recipes[selectedRecipeIndex].cost_analysis?.suggested_retail?.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Margin</span>
                      <span className="font-medium text-purple-600">{claudeResponse.recipes[selectedRecipeIndex].cost_analysis?.margin_percentage}%</span>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Batch Size</span>
                      <span className="font-medium">{formulation.batchSize}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Shelf Life</span>
                      <span className="font-medium">{formulation.shelfLife}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Cost</span>
                      <span className="font-medium text-green-600">${formulation.totalCost}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Est. Retail</span>
                      <span className="font-medium text-blue-600">${formulation.estimatedRetail}</span>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Compliance Status */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Compliance Status</h3>
              <div className="space-y-3">
                {claudeResponse?.recipes?.[selectedRecipeIndex]?.compliance_details ? (
                  <>
                    {/* Handle regulatory_status as object or string */}
                    {claudeResponse.recipes[selectedRecipeIndex].compliance_details.regulatory_status && (
                      typeof claudeResponse.recipes[selectedRecipeIndex].compliance_details.regulatory_status === 'object' 
                        ? Object.entries(claudeResponse.recipes[selectedRecipeIndex].compliance_details.regulatory_status).map(([key, value]) => (
                            <div key={key} className="flex items-center justify-between">
                              <span className="text-gray-600 capitalize">{key.toUpperCase()}</span>
                              <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {typeof value === 'string' ? value : JSON.stringify(value)}
                              </span>
                            </div>
                          ))
                        : (
                            <div className="flex items-center justify-between">
                              <span className="text-gray-600">Regulatory Status</span>
                              <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {claudeResponse.recipes[selectedRecipeIndex].compliance_details.regulatory_status}
                              </span>
                            </div>
                          )
                    )}
                    {claudeResponse.recipes[selectedRecipeIndex].compliance_details.certifications_needed?.map((cert, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-gray-600">{cert}</span>
                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Required
                        </span>
                      </div>
                    ))}
                  </>
                ) : (
                  Object.entries(formulation.complianceDetails).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between">
                      <span className="text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        value === 'Approved' || value === 'Verified' || value === 'Certified'
                          ? 'bg-green-100 text-green-800'
                          : value === 'Pending Certification'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                      }`}>
                        {value}
                      </span>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Market Positioning */}
            {claudeResponse?.market_positioning && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Market Positioning</h3>
                <div className="space-y-4">
                  <div>
                    <span className="text-sm font-medium text-gray-700">Target Consumer:</span>
                    <p className="text-sm text-gray-600 mt-1">{claudeResponse.market_positioning.target_consumer}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-700">Price Positioning:</span>
                    <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      {claudeResponse.market_positioning.price_positioning}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-700">Key Selling Points:</span>
                    <div className="mt-2 space-y-1">
                      {claudeResponse.market_positioning.unique_selling_points?.map((point, index) => (
                        <div key={index} className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="w-3 h-3 text-green-500 mr-2 flex-shrink-0" />
                          {point}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button 
                  onClick={() => handleExport('pdf')}
                  className="w-full flex items-center px-4 py-2 text-left text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <FileText className="w-4 h-4 mr-3" />
                  Generate Manufacturing Specs
                </button>
                <button className="w-full flex items-center px-4 py-2 text-left text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                  <Users className="w-4 h-4 mr-3" />
                  Share with Team
                </button>
                <button className="w-full flex items-center px-4 py-2 text-left text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                  <Clock className="w-4 h-4 mr-3" />
                  Schedule Production
                </button>
                <button className="w-full flex items-center px-4 py-2 text-left text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                  <Settings className="w-4 h-4 mr-3" />
                  Optimize Formulation
                </button>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultsDashboard;
