import React, { forwardRef, useEffect, useImperative<PERSON>andle, useState } from 'react';
import { Loader2, AlertCircle } from 'lucide-react';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import DynamicFormField from '../components/DynamicFormField';
import ParamCard from '../components/ParamCard';
import FormSection from '../components/FormSection';
import { mapBindingsToFields, groupFieldsBySection, collectAnswerKeys, hydrateFieldsWithAnswers } from '../utils/paramMapping';
import { getAnswerSetsWithCache } from '../utils/answersCache';

const GoalsEmbedded = forwardRef(function GoalsEmbedded({ onComplete }, ref) {
  const { formData, updateFormData } = useApp();
  const { apiRequest } = useAuth();
  const [parameters, setParameters] = useState([]);
  const [formValues, setFormValues] = useState({});
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(true);
  const [apiError, setApiError] = useState(null);
  const [validating, setValidating] = useState(false);

  useEffect(() => {
    fetchParameters();
  }, [formData.productType, formData.productSubCategory, formData.productCategory, formData.industry]);

  const fetchParameters = async () => {
    try {
      setLoading(true);
      setApiError(null);

      const tryLevels = [];
      if (formData.productSubCategory && formData.productSubCategory !== 'custom') {
        tryLevels.push(['sub_category', formData.productSubCategory]);
      }
      if (formData.productCategory && formData.productCategory !== 'custom') {
        tryLevels.push(['category', formData.productCategory]);
      }
      if (formData.industry) {
        tryLevels.push(['industry', formData.industry]);
      }

      let bindings = [];
      for (const [level, slug] of tryLevels) {
        const resp = await apiRequest(`/param-bindings?level=${encodeURIComponent(level)}&slug=${encodeURIComponent(slug)}`);
        if (resp?.data?.length) { bindings = resp.data; break; }
      }

      let fields = mapBindingsToFields(bindings);

      // Fetch and hydrate answer sets (for selects/enums)
      const answerKeys = collectAnswerKeys(fields);
      if (answerKeys.length > 0) {
        const answerMap = await getAnswerSetsWithCache(apiRequest, answerKeys);
        fields = hydrateFieldsWithAnswers(fields, answerMap);
      }
      const sections = groupFieldsBySection(fields);
      setParameters(sections);

      // Initialize form values with initial_value (default_value → guidance.average fallback)
      const initialValues = {};
      fields.forEach(f => {
        if (f.initial_value !== undefined) initialValues[f.key] = f.initial_value;
        if (initialValues[f.key] === undefined) {
          // Fallback defaults to ensure all goals have some value
          if (f.type === 'number') {
            const min = f.widget?.min;
            const max = f.widget?.max;
            const avg = f.guidance?.average;
            initialValues[f.key] = avg !== undefined ? avg : (min !== undefined && max !== undefined ? Math.round((min + max)/2) : (min !== undefined ? min : 0));
          } else if (f.type === 'enum' || f.widget?.type === 'select') {
            initialValues[f.key] = f.options?.[0]?.value ?? '';
          } else if (f.type === 'boolean' || f.widget?.type === 'checkbox') {
            initialValues[f.key] = false;
          } else {
            initialValues[f.key] = '';
          }
        }
      });
      setFormValues(initialValues);
    } catch (e) {
      console.error('GoalsEmbedded fetch error:', e);
      setApiError('Failed to load goals. You can still proceed.');
      setParameters([]);
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = (key, value) => {
    setFormValues(prev => ({ ...prev, [key]: value }));
    if (errors[key]) setErrors(prev => { const n = { ...prev }; delete n[key]; return n; });
  };

  const validate = () => {
    const newErrors = {};
    parameters.forEach(section => {
      section.parameters.forEach(param => {
        if (param.required && (formValues[param.key] === undefined || formValues[param.key] === '')) {
          newErrors[param.key] = `${param.label || param.key} is required`;
        }
      });
    });
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Expose submit to parent (modal) Next button
  useImperativeHandle(ref, () => ({
    async submit() {
      setValidating(true);
      const ok = validate();
      setValidating(false);
      if (!ok) return false;
      updateFormData('goals', formValues);
      onComplete?.(formValues);
      return true;
    }
  }));

  return (
    <div>
      {apiError && (
        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg flex items-start gap-2 text-sm">
          <AlertCircle className="w-4 h-4 text-yellow-600 mt-0.5" />
          <div className="text-yellow-800">{apiError}</div>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-40"><Loader2 className="w-6 h-6 animate-spin text-gray-400" /></div>
      ) : parameters.length === 0 ? (
        <div className="text-sm text-slate-600">No goal parameters configured. Click Next to continue.</div>
      ) : (
        <div className="space-y-4">
          {parameters.map(section => (
            <FormSection key={section.name} title={section.name} meta={null}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {section.parameters.map(param => (
                  <ParamCard key={param.key} required={param.required}>
                    <DynamicFormField
                      field={param}
                      value={formValues[param.key]}
                      onChange={handleFieldChange}
                      error={errors[param.key]}
                      disabled={validating}
                    />
                  </ParamCard>
                ))}
              </div>
            </FormSection>
          ))}
        </div>
      )}
    </div>
  );
});

export default GoalsEmbedded;
