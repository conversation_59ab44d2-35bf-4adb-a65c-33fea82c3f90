import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Plus,
  Calendar,
  ArrowRight,
  Clock,
  CheckCircle,
  FileText,
  Play,
  ExternalLink,
  FolderOpen,
  Beaker,
  RefreshCw,
  AlertTriangle,
  X
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useApp } from '../context/AppContext';
import FormulationWizardModal from '../components/FormulationWizardModal';
import { mapStatus } from '../utils/colorUtils';
import { formatDate } from '../utils/dateUtils';
import { projectApi, playgroundApi } from '../utils/apiUtils';

const Dashboard = () => {
  const { user, apiRequest } = useAuth();
  const { setCurrentStep, updateFormData } = useApp();
  const navigate = useNavigate();
  const [showWizardModal, setShowWizardModal] = useState(false);
  const [retryProjectData, setRetryProjectData] = useState(null);
  const [recentProjects, setRecentProjects] = useState([]);
  const [totalProjects, setTotalProjects] = useState([]);
  const [isLoadingProjects, setIsLoadingProjects] = useState(true);
  const [retryConfirm, setRetryConfirm] = useState({ show: false, project: null });

  // Fetch recent projects on component mount and when user changes
  useEffect(() => {
    if (user) {
      fetchRecentProjects();
    }
  }, [user]);

  const fetchRecentProjects = async () => {
    try {
      setIsLoadingProjects(true);
      const [recentResponse, totalResponse] = await Promise.all([
        projectApi.getAll(5),
        projectApi.getAll(1000)
      ]);
      
      if (recentResponse.success) {
        setRecentProjects((recentResponse.data || []).map(p => ({ ...p, status: mapStatus(p.status) })));
      }
      if (totalResponse.success) {
        setTotalProjects((totalResponse.data || []).map(p => ({ ...p, status: mapStatus(p.status) })));
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
    } finally {
      setIsLoadingProjects(false);
    }
  };


  const handleStartNewProject = () => {
    // Open new project wizard modal route
    navigate('/new-project');
  };

  const handleWizardComplete = async (generatedFormulation) => {
    console.log('🎉 Dashboard received completed formulation:', generatedFormulation);
    
    try {
      // Store the generated formulation in context
      updateFormData('generatedFormulation', generatedFormulation);
      
      // Refresh projects list to include new project
      await fetchRecentProjects();
      
      // Close modal and navigate to product playground
      setShowWizardModal(false);
      setCurrentStep('playground');
      
      console.log('✅ Dashboard formulation handling completed successfully');
    } catch (error) {
      console.error('❌ Error handling completed formulation:', error);
      alert('An error occurred while processing the formulation. Please try again.');
    }
  };

  const handleCloseModal = () => {
    setShowWizardModal(false);
    setRetryProjectData(null); // Reset retry data when modal closes
  };

  const handleOpenInPlayground = async (project) => {
    try {
      console.log('🎮 Opening project in playground:', project);
      
      const projectId = project._id || project.id;
      
      // First check if project has formulations
      const statusResponse = await playgroundApi.getStatus(projectId);
      
      if (statusResponse.success && statusResponse.data.hasFormulation) {
        console.log('✅ Project has persisted formulation data, loading from database');
        
        // Store the project metadata in context for playground navigation
        updateFormData('selectedProject', {
          id: projectId,
          name: project.name,
          description: project.description,
          industry: project.industry || statusResponse.data.industry,
          product_type: project.product_type || statusResponse.data.product_type,
          currentVersion: statusResponse.data.currentVersion,
          qualityScore: statusResponse.data.qualityScore,
          lastUpdated: statusResponse.data.lastUpdated
        });
        
        // Set flag to load from database
        updateFormData('loadFromDatabase', true);
        updateFormData('forceOverlay', true);
        updateFormData('projectIdToLoad', projectId);
        
        // Clear any cached/wizard data to ensure we load from DB
        updateFormData('cachedFormulation', null);
        updateFormData('generatedFormulation', null);
        
        // Navigate to playground with proper URL routing
        navigate(`/playground/${projectId}`);
        
        console.log('✅ Successfully navigated to playground with projectId:', projectId);
      } else {
        // No formulation yet → open playground with processing overlay
        updateFormData('selectedProject', { id: projectId, name: project.name, description: project.description, industry: project.industry, product_type: project.product_type });
        updateFormData('projectIdToLoad', projectId);
        updateFormData('loadFromDatabase', true);
        navigate(`/playground/${projectId}`);
      }
    } catch (error) {
      console.error('❌ Error opening project in playground:', error);
      alert('An error occurred while opening the project. Please try again.');
    }
  };

  const handleRetryProject = (project, event) => {
    event.stopPropagation(); // Prevent card click
    setRetryConfirm({ show: true, project });
  };

  const confirmRetryProject = async () => {
    const project = retryConfirm.project;
    setRetryConfirm({ show: false, project: null });
    
    try {
      const response = await projectApi.retry(project._id);
      if (response.success) {
        // Refresh the projects list to show updated status
        await fetchRecentProjects();
        alert('Project has been retried successfully and added to the queue.');
      }
    } catch (error) {
      console.error('❌ Error retrying project:', error);
      alert('Failed to retry project. Please try again.');
    }
  };

  return (
    <>
    <div className="px-6 py-3">
      {/* Dashboard Header */}
      <div className="mb-4 flex items-center justify-between">
        <div>
          <h1 className="text-xl font-semibold text-gray-900">Welcome back, {user?.first_name || 'User'}!</h1>
          <p className="mt-0.5 text-sm text-gray-500">Here's an overview of your formulation projects</p>
        </div>
        <button
          onClick={handleStartNewProject}
          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center transition-all shadow-sm hover:shadow-md"
        >
          <Plus className="w-4 h-4 mr-2" />
          New Project
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-lg">
                  <FolderOpen className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm text-gray-600">Active Projects</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {isLoadingProjects ? '...' : totalProjects.filter(p => p.status === 'in_progress').length}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <Beaker className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm text-gray-600">Total Projects</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {isLoadingProjects ? '...' : totalProjects.length}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <CheckCircle className="w-6 h-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {isLoadingProjects ? '...' : totalProjects.filter(p => p.status === 'completed').length}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Projects */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Recent Projects</h2>
            </div>
            <div className="p-6">
              {isLoadingProjects ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
                  <p className="text-gray-500 mt-2">Loading projects...</p>
                </div>
              ) : recentProjects.length === 0 ? (
                <div className="text-center py-12">
                  <FolderOpen className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No projects yet</h3>
                  <p className="text-gray-600 mb-6">Get started by creating your first formulation project</p>
                  <button 
                    onClick={handleStartNewProject}
                    className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 flex items-center mx-auto transition-colors"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create Your First Project
                  </button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {recentProjects.map((project) => {
                    const getIndustryIcon = (industry) => {
                      switch (industry) {
                        case 'beverages': return '🥤';
                        case 'nutraceuticals': return '💊';
                        case 'cosmetics': return '✨';
                        default: return '📦';
                      }
                    };

                    return (
                      <div
                        key={project._id}
                        className={`relative bg-white rounded-lg border-2 cursor-pointer transition-all hover:border-green-400 group ${
                          project.industry === 'beverages' ? 'border-blue-300 bg-blue-50 hover:bg-blue-100' :
                          project.industry === 'nutraceuticals' ? 'border-green-300 bg-green-50 hover:bg-green-100' :
                          project.industry === 'cosmetics' ? 'border-purple-300 bg-purple-50 hover:bg-purple-100' :
                          'border-gray-300 bg-gray-50 hover:bg-gray-100'
                        }`}
                        onClick={() => handleOpenInPlayground(project)}
                      >
                        {/* Status Icon */}
                        <div className="absolute top-2 right-2 flex gap-4">
                          {project.status === 'completed' ? (
                            <CheckCircle className="w-4 h-4 text-green-600" />
                          ) : project.status === 'in_progress' ? (
                            <Clock className="w-4 h-4 text-blue-600" />
                          ) : project.status === 'queued' ? (
                            <Clock className="w-4 h-4 text-blue-600" />
                          ) : project.status === 'failed' ? (
                            <>
                              <AlertTriangle className="w-4 h-4 text-red-600" />
                              <button
                                onClick={(e) => handleRetryProject(project, e)}
                                className="w-4 h-4 text-blue-600 hover:text-blue-800 transition-colors"
                                title="Retry project"
                              >
                                <RefreshCw className="w-4 h-4" />
                              </button>
                            </>
                          ) : (
                            <FileText className="w-4 h-4 text-gray-400" />
                          )}
                        </div>
                        
                        <div className="p-4">
                          {/* Header */}
                          <div className="mb-3">
                            <div className="flex items-center gap-2 mb-2">
                              <span className="text-lg">{getIndustryIcon(project.industry)}</span>
                              <h3 className="font-semibold text-gray-900 group-hover:text-green-600 transition-colors line-clamp-1 text-sm">
                                {project.name.split(' ').map(word => 
                                  word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                                ).join(' ')}
                              </h3>
                              <span className={`ml-auto inline-flex items-center px-2 py-0.5 rounded text-[10px] font-medium border ${
                                project.status === 'ready' ? 'bg-green-50 text-green-700 border-green-200' :
                                project.status === 'generating' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                                project.status === 'queued' ? 'bg-amber-50 text-amber-700 border-amber-200' :
                                project.status === 'failed' ? 'bg-red-50 text-red-700 border-red-200' :
                                project.status === 'closed' ? 'bg-purple-50 text-purple-700 border-purple-200' :
                                project.status === 'archived' ? 'bg-gray-50 text-gray-700 border-gray-200' :
                                'bg-gray-50 text-gray-600 border-gray-200'
                              }`}>
                                {project.status === 'queued' ? 'Queued' : 
                                 project.status === 'generating' ? 'Generating' : 
                                 project.status === 'ready' ? 'Ready' : 
                                 project.status === 'failed' ? 'Failed' : 
                                 project.status === 'closed' ? 'Closed' : 
                                 project.status === 'archived' ? 'Archived' : 'Queued'}
                              </span>
                            </div>
                          </div>

                          {/* Tags */}
                          <div className="flex flex-wrap gap-1 mb-3">
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium text-gray-600 bg-white/60">
                              {project.industry.charAt(0).toUpperCase() + project.industry.slice(1)}
                            </span>
                            {project.product_type && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium text-gray-600 bg-white/60">
                                {project.product_type.split('-').map(word => 
                                  word.charAt(0).toUpperCase() + word.slice(1)
                                ).join(' ')}
                              </span>
                            )}
                          </div>

                          {/* Footer */}
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500">
                              {formatDate(project.updated_at)}
                            </span>
                            {project.current_formulation && (
                              <span className="text-xs text-green-600 font-medium">
                                v{project.current_formulation.version || 1}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  
                  {/* More Projects Card */}
                  {totalProjects.length > 5 && (
                    <div
                      className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-6 cursor-pointer hover:border-green-400 hover:bg-green-50 transition-all group"
                      onClick={() => navigate('/projects')}
                    >
                      <div className="text-center">
                        <div className="text-2xl mb-2 group-hover:text-green-600 text-gray-400">📁</div>
                        <p className="text-sm text-gray-600 group-hover:text-green-600 font-medium">
                          +{totalProjects.length - 5} More Projects
                        </p>
                        <p className="text-xs text-gray-500 group-hover:text-green-600 mt-1">
                          View All
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

      {/* Formulation Wizard Modal */}
      <FormulationWizardModal 
        isOpen={showWizardModal}
        onClose={handleCloseModal}
        onComplete={handleWizardComplete}
        retryData={retryProjectData}
      />

      {/* Retry Confirmation Dialog */}
      {retryConfirm.show && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={() => setRetryConfirm({ show: false, project: null })}>
          <div className="bg-white rounded-lg border-2 border-gray-200 p-6 max-w-sm mx-4 shadow-xl" onClick={(e) => e.stopPropagation()}>
            <div className="flex items-center mb-4">
              <div className="p-2 bg-blue-100 rounded-lg mr-3">
                <RefreshCw className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Retry Project</h3>
              <button
                onClick={() => setRetryConfirm({ show: false, project: null })}
                className="ml-auto p-1 text-gray-400 hover:text-gray-600 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <p className="text-sm text-gray-600 mb-6">
              Are you sure you want to retry generating the formulation for "<strong>{retryConfirm.project?.name}</strong>"? This will requeue the project for processing.
            </p>
            
            <div className="flex gap-3">
              <button
                onClick={() => setRetryConfirm({ show: false, project: null })}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={confirmRetryProject}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Dashboard;
