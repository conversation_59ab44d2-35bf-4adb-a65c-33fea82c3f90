import React, { useState, useEffect, useRef } from 'react';
import { ChevronRight, ChevronLeft, Loader2 } from 'lucide-react';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import OptionCard from '../components/OptionCard';
import PageHeader from '../components/PageHeader';
import useWizardHotkeys from '../hooks/useWizardHotkeys';

const ProductTypeSelection = ({ embedded = false, onSelectCategory, onContinueCustom }) => {
  const { formData, updateFormData } = useApp();
  const { apiRequest } = useAuth();
  const navigate = useNavigate();
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [error, setError] = useState(null);
  const [customDescription, setCustomDescription] = useState('');
  const [useCustom, setUseCustom] = useState(false);

  // Keyboard navigation refs and indices
  const catRefs = useRef([]);
  const [catIndex, setCatIndex] = useState(0);

  useEffect(() => {
    if (categories.length > 0) {
      setCatIndex(prev => Math.min(prev, categories.length - 1));
    } else {
      setCatIndex(0);
    }
  }, [categories.length]);

  // No subcategory index on this page

  // Fetch categories when component mounts or industry changes
  useEffect(() => {
    if (formData.industry) {
      fetchCategories();
    } else {
        // If there is no industry in the context, redirect to the industry selection page
        navigate('/industry-selection');
    }
  }, [formData.industry]);

  // ESC returns to Industry page; warn if custom text entered
  useWizardHotkeys({ onEsc: () => navigate('/industry-selection'), enabled: !embedded, isDirty: useCustom && !!customDescription.trim() });

  // No subcategory fetch on this page (moved to dedicated page)

  const fetchCategories = async () => {
    try {
      setLoadingCategories(true);
      setError(null);

      // Get the industry doc (for _id)
      const industryResponse = await apiRequest(`/taxonomies/${formData.industry}`);
      const industryData = industryResponse.data;

      // Fetch categories for this industry
      const categoryResponse = await apiRequest(`/taxonomies?level=category&parent_id=${industryData?._id}`);
      const data = categoryResponse.data || [];

      // Transform and add custom option
      const transformedCategories = data.map(item => ({
        id: item.slug,
        name: item.name,
        description: item.description || '',
        parentId: item.parent_id
      }));

      // Add custom option at the end
      transformedCategories.push({
        id: 'custom',
        name: 'Custom Product',
        description: 'Define your own product type',
        isCustom: true
      });

      setCategories(transformedCategories);
    } catch (err) {
      console.error('Error fetching categories:', err);
      setError('Failed to load product categories. Please try again.');
      // Fallback to some default categories
      setCategories([
        { id: 'custom', name: 'Custom Product', description: 'Define your own product type', isCustom: true }
      ]);
    } finally {
      setLoadingCategories(false);
    }
  };

  // Subcategories handled in SubCategorySelection page

  const handleCategorySelect = (categoryId) => {
    setSelectedCategory(categoryId);
    setUseCustom(categoryId === 'custom');
    if (categoryId === 'custom') return;
    // Persist and auto-advance
    updateFormData('productCategory', categoryId);
    updateFormData('productSubCategory', '');
    if (onSelectCategory) {
      onSelectCategory(categoryId);
    } else {
      navigate('/subcategory-selector');
    }
  };

  // No-op here

  const handleContinue = () => {
    // Save only for custom path
    if (useCustom || selectedCategory === 'custom') {
      updateFormData('productType', 'custom');
      updateFormData('productDescription', customDescription);
      updateFormData('productCategory', 'custom');
    }
    if (onContinueCustom) {
      onContinueCustom({ description: customDescription });
    } else {
      navigate('/goal-setup');
    }
  };

  // Simple linear arrow navigation for grids
  const handleCatKeyDown = (e) => {
    if (!['ArrowRight', 'ArrowLeft', 'ArrowDown', 'ArrowUp'].includes(e.key)) return;
    e.preventDefault();
    const max = categories.length - 1;
    let next = catIndex;
    if (e.key === 'ArrowRight' || e.key === 'ArrowDown') next = Math.min(max, catIndex + 1);
    if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') next = Math.max(0, catIndex - 1);
    setCatIndex(next);
    const el = catRefs.current[next];
    if (el) el.focus();
  };

  // No subcategory grid keyboard handler on this page

  const canContinue = () => {
    if (selectedCategory === 'custom') {
      return customDescription.trim().length > 0;
    }
    return selectedCategory === 'custom';
  };

  return (
    <div className="min-h-screen bg-slate-50 px-6 py-3">
      {!embedded && (
        <div className="mb-3 max-w-6xl mx-auto">
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-lg font-semibold text-slate-900">Select Product Category</h1>
              <p className="text-xs text-slate-500 mt-0.5">{`Choose the type of ${formData.industry.replace(/-/g, ' ')} product you want to formulate`}</p>
            </div>
            <span className="px-2.5 py-1 rounded-full bg-emerald-100 text-emerald-800 text-xs font-medium capitalize">{formData.industry.replace(/-/g, ' ')}</span>
          </div>
        </div>
      )}

        {/* Category Selection */}
        {loadingCategories ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
          </div>
        ) : error ? (
          <div className="text-center p-8">
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={fetchCategories}
              className="px-4 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800"
            >
              Retry
            </button>
          </div>
        ) : categories.length === 0 ? (
          <div className="max-w-6xl mx-auto">
            <section className="p-6 bg-white rounded-xl shadow-sm border border-slate-200 mb-8 text-center">
              <h2 className="text-lg font-semibold text-slate-900">No categories found</h2>
              <p className="mt-2 text-slate-600">This industry has no categories configured. Check taxonomy data or try again.</p>
              <div className="mt-4 flex justify-center gap-3">
                <button onClick={fetchCategories} className="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700">Retry</button>
                <button onClick={() => handleCategorySelect('custom')} className="px-4 py-2 border border-slate-300 rounded-lg hover:bg-slate-50">Use Custom Product</button>
              </div>
            </section>
          </div>
        ) : (
          <div className="max-w-6xl mx-auto">
            <section className="p-4 md:p-5 bg-white rounded-xl shadow-sm border border-slate-200 mb-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4" onKeyDown={handleCatKeyDown}>
                {categories.map((category, idx) => (
                  <OptionCard
                    key={category.id}
                    title={category.name}
                    description={category.description}
                    selected={selectedCategory === category.id}
                    variant="category"
                    badge={category.isCustom ? 'Custom' : undefined}
                    onClick={() => handleCategorySelect(category.id)}
                    ref={el => (catRefs.current[idx] = el)}
                  />
                ))}
              </div>
            </section>

            {/* Sub-categories moved to dedicated page; keep only custom flow here */}

            {/* Custom Product Description */}
            {selectedCategory === 'custom' && (
                <div className="p-4 md:p-5 bg-white rounded-xl shadow-sm border border-slate-200 mb-6">
                  <label className="block text-sm md:text-base font-semibold text-slate-900 mb-2">Describe Your Product</label>
                  <textarea
                    value={customDescription}
                    onChange={(e) => setCustomDescription(e.target.value)}
                    placeholder="E.g., Energy drink with adaptogenic herbs and nootropics"
                    className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400"
                    rows={3}
                  />
                </div>
            )}

            {/* Continue Button (only for custom flow) */}
            {useCustom && (
              <div className="flex justify-end max-w-6xl mx-auto mt-2">
                <button
                  onClick={handleContinue}
                  disabled={!canContinue()}
                  className="px-5 md:px-6 py-2.5 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-slate-300 disabled:cursor-not-allowed flex items-center shadow-sm"
                >
                  Continue to Goals <ChevronRight className="ml-2 w-4 h-4" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>
  );
};

export default ProductTypeSelection;
