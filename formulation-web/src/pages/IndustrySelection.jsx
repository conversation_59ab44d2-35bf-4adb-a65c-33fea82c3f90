import React, { useState, useEffect, useRef } from 'react';
import { ChevronRight, Droplets, Pill, Leaf, Loader2 } from 'lucide-react';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import OptionCard from '../components/OptionCard';
import PageHeader from '../components/PageHeader';
import useWizardHotkeys from '../hooks/useWizardHotkeys';

const IndustrySelection = ({ embedded = false, onSelect }) => {
  const { updateFormData } = useApp();
  const { apiRequest } = useAuth();
  const navigate = useNavigate();
  const [selectedIndustry, setSelectedIndustry] = useState('');
  const [industries, setIndustries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Keyboard navigation
  const itemRefs = useRef([]);
  const [focusIndex, setFocusIndex] = useState(0);

  // Icon mapping based on industry slug
  const iconMap = {
    'functional-food': Droplets,
    'nutraceuticals': Pill,
    'herbal-cosmetics': Leaf,
    'herbal-extracts': Leaf,
    // Legacy mappings
    'functional-beverages': Droplets,
    'beverages': Droplets,
    'cosmetics': Leaf
  };

  // Color mapping based on industry slug
  const colorMap = {
    'functional-food': 'blue',
    'nutraceuticals': 'green',
    'herbal-cosmetics': 'purple',
    'herbal-extracts': 'green',
    // Legacy mappings
    'functional-beverages': 'blue',
    'beverages': 'blue',
    'cosmetics': 'purple'
  };

  useEffect(() => {
    fetchIndustries();
  }, []);

  // ESC closes wizard to dashboard
  useWizardHotkeys({ onEsc: () => navigate('/dashboard'), enabled: true });

  const fetchIndustries = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiRequest('/taxonomies?level=industry&status=active');
      const data = response.data || [];

      // Transform API data to match component structure
      const transformedIndustries = data.map(item => ({
        id: item.slug,
        name: item.name,
        icon: iconMap[item.slug] || Leaf,
        description: item.metadata?.description || item.description || '',
        color: colorMap[item.slug] || 'gray',
        metadata: item.metadata || {}
      }));

      setIndustries(transformedIndustries);
    } catch (err) {
      console.error('Error fetching industries:', err);
      setError('Failed to load industries. MongoDB connection issue or API error.');
      // NO FAKE FALLBACK - show actual error
    } finally {
      setLoading(false);
    }
  };

  const handleSelect = (industryId) => {
    setSelectedIndustry(industryId);
    updateFormData('industry', industryId);
    if (onSelect) {
      onSelect(industryId);
    } else {
      // Auto-advance to categories
      navigate('/category-selector');
    }
  };

  const handleKeyDown = (e) => {
    if (!['ArrowRight', 'ArrowLeft', 'ArrowDown', 'ArrowUp'].includes(e.key)) return;
    e.preventDefault();
    const max = industries.length - 1;
    let next = focusIndex;
    if (e.key === 'ArrowRight' || e.key === 'ArrowDown') next = Math.min(max, focusIndex + 1);
    if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') next = Math.max(0, focusIndex - 1);
    setFocusIndex(next);
    const el = itemRefs.current[next];
    if (el) el.focus();
  };

  // Note: color classes handled by shared card style

  return (
    <div className={embedded ? '' : 'min-h-screen bg-slate-50 px-6 py-3'}>
      <div className="w-full max-w-6xl mx-auto">
        {!embedded && (
          <div className="mb-3">
            <h1 className="text-lg font-semibold text-slate-900">Choose Your Industry</h1>
            <p className="text-xs text-slate-500 mt-0.5">Select the industry that best matches your formulation needs</p>
          </div>
        )}

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
            </div>
          ) : error ? (
            <div className="text-center p-8">
              <p className="text-red-600 mb-4">{error}</p>
              <button
                onClick={fetchIndustries}
                className="px-4 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800"
              >
                Retry
              </button>
            </div>
          ) : industries.length === 0 ? (
            <div className="p-6 bg-white rounded-xl shadow-sm border border-slate-200 text-center mb-12">
              <h2 className="text-lg font-semibold text-slate-900">No industries available</h2>
              <p className="mt-1 text-slate-600">Please check your taxonomy setup or try again.</p>
              <button onClick={fetchIndustries} className="mt-4 px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700">Retry</button>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6" onKeyDown={handleKeyDown}>
              {industries.map((industry, idx) => {
                const Icon = industry.icon;
                const isSelected = selectedIndustry === industry.id;
                return (
                  <OptionCard
                    key={industry.id}
                    title={industry.name}
                    description={industry.description}
                    selected={isSelected}
                    icon={Icon}
                    onClick={() => handleSelect(industry.id)}
                    ref={el => (itemRefs.current[idx] = el)}
                  >
                    {/* Icon rendered via prop */}
                  </OptionCard>
                );
              })}
            </div>
          )}

          {!embedded && (
            <div className="flex justify-between mt-2">
              <button
                onClick={() => navigate('/')}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Back
              </button>
              <button
                onClick={() => navigate('/category-selector')}
                disabled={!selectedIndustry}
                className="px-5 md:px-6 py-2.5 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-slate-300 disabled:cursor-not-allowed flex items-center shadow-sm"
              >
                Continue <ChevronRight className="ml-2 w-4 h-4" />
              </button>
            </div>
          )}
      </div>
    </div>
  );
};

export default IndustrySelection;
