import React, { useState, useEffect } from 'react';
import { ChevronRight, ChevronLeft, Loader2, AlertCircle } from 'lucide-react';
import useWizardHotkeys from '../hooks/useWizardHotkeys';
import { useApp } from '../context/AppContext';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import Logo from '../components/Logo';
import ProgressBar from '../components/ProgressBar';
import DynamicFormField from '../components/DynamicFormField';
import FormSection from '../components/FormSection';
import { mapBindingsToFields, groupFieldsBySection, collectAnswerKeys, hydrateFieldsWithAnswers } from '../utils/paramMapping';
import { getAnswerSetsWithCache } from '../utils/answersCache';

const GoalSetup = () => {
  const { formData, setCurrentStep, updateFormData } = useApp();
  const navigate = useNavigate();
  const { apiRequest } = useAuth();
  const [parameters, setParameters] = useState([]);
  const [formValues, setFormValues] = useState({});
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(true);
  const [validating, setValidating] = useState(false);
  const [apiError, setApiError] = useState(null);
  const [guardrailResults, setGuardrailResults] = useState([]);

  useEffect(() => {
    fetchParameters();
  }, [formData.productType]);

  // ESC returns to SubCategory page; confirm if values entered
  const isDirty = Object.values(formValues).some(v => (v ?? '').toString().trim().length > 0);
  useWizardHotkeys({ onEsc: () => navigate('/subcategory-selector'), enabled: true, isDirty });

  const fetchParameters = async () => {
    try {
      setLoading(true);
      setApiError(null);

      // Determine the target level and slug based on user selection
      let targetLevel, targetSlug;
      
      if (formData.productSubCategory && formData.productSubCategory !== 'custom') {
        targetLevel = 'sub_category';
        targetSlug = formData.productSubCategory;
      } else if (formData.productCategory && formData.productCategory !== 'custom') {
        targetLevel = 'category';
        targetSlug = formData.productCategory;
      } else {
        targetLevel = 'industry';
        targetSlug = formData.industry;
      }

      // Fetch parameter bindings for this target (align with backend route/params)
      const bindingsResponse = await apiRequest(
        `/param-bindings?level=${encodeURIComponent(targetLevel)}&slug=${encodeURIComponent(targetSlug)}`
      );
      let fields = mapBindingsToFields(bindingsResponse.data || []);

      // Fetch and hydrate answer sets (for selects/enums)
      const answerKeys = collectAnswerKeys(fields);
      if (answerKeys.length > 0) {
        const answerMap = await getAnswerSetsWithCache(apiRequest, answerKeys);
        fields = hydrateFieldsWithAnswers(fields, answerMap);
      }
      const sections = groupFieldsBySection(fields);

      setParameters(sections);

      // Initialize form values with initial_value (default_value → guidance.average fallback)
      const initialValues = {};
      fields.forEach(f => {
        if (f.initial_value !== undefined) initialValues[f.key] = f.initial_value;
        if (initialValues[f.key] === undefined) {
          // Fallback defaults to ensure all goals have some value
          if (f.type === 'number') {
            const min = f.widget?.min;
            const max = f.widget?.max;
            const avg = f.guidance?.average;
            initialValues[f.key] = avg !== undefined ? avg : (min !== undefined && max !== undefined ? Math.round((min + max)/2) : (min !== undefined ? min : 0));
          } else if (f.type === 'enum' || f.widget?.type === 'select') {
            initialValues[f.key] = f.options?.[0]?.value ?? '';
          } else if (f.type === 'boolean' || f.widget?.type === 'checkbox') {
            initialValues[f.key] = false;
          } else {
            initialValues[f.key] = '';
          }
        }
      });
      setFormValues(initialValues);

    } catch (err) {
      console.error('Error fetching parameters:', err);
      setApiError('Failed to load configuration. Using default parameters.');
      
      // Fallback to default parameters
      setParameters([
        {
          name: 'Budget & Timeline',
          parameters: [
            {
              key: 'budget',
              label: 'Budget Range',
              type: 'number',
              required: true,
              widget: { type: 'slider', min: 10000, max: 500000, step: 5000 },
              default_value: 50000
            },
            {
              key: 'timeline',
              label: 'Project Timeline',
              type: 'string',
              required: true,
              widget: { type: 'select' },
              options: [
                { value: '1_month', label: '1 Month' },
                { value: '3_months', label: '3 Months' },
                { value: '6_months', label: '6 Months' },
                { value: '1_year', label: '1 Year' }
              ]
            }
          ]
        },
        {
          name: 'Compliance & Certifications',
          parameters: [
            {
              key: 'certifications',
              label: 'Required Certifications',
              type: 'array',
              widget: { type: 'multiselect' },
              options: [
                { value: 'organic', label: 'Organic' },
                { value: 'non_gmo', label: 'Non-GMO' },
                { value: 'fda_approved', label: 'FDA Approved' },
                { value: 'gmp', label: 'GMP Certified' }
              ]
            }
          ]
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = (key, value) => {
    setFormValues(prev => ({
      ...prev,
      [key]: value
    }));
    
    // Clear error for this field
    if (errors[key]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[key];
        return newErrors;
      });
    }
  };

  const validateForm = async () => {
    setValidating(true);
    const newErrors = {};
    
    // Basic validation
    parameters.forEach(section => {
      section.parameters.forEach(param => {
        if (param.required && !formValues[param.key]) {
          newErrors[param.key] = `${param.label} is required`;
        }
        
        // Type-specific validation
        if (param.type === 'number' && formValues[param.key]) {
          const value = parseFloat(formValues[param.key]);
          if (isNaN(value)) {
            newErrors[param.key] = `${param.label} must be a number`;
          } else if (param.widget?.min !== undefined && value < param.widget.min) {
            newErrors[param.key] = `${param.label} must be at least ${param.widget.min}`;
          } else if (param.widget?.max !== undefined && value > param.widget.max) {
            newErrors[param.key] = `${param.label} must be at most ${param.widget.max}`;
          }
        }
      });
    });

    // Guardrail validation
    try {
      const guardrailData = {
        industry: formData.industry,
        category: formData.productCategory,
        sub_category: formData.productSubCategory,
        parameters: formValues
      };
      
      const results = await apiRequest('/guardrails/evaluate', {
        method: 'POST',
        body: JSON.stringify(guardrailData)
      });
      
      // Process guardrail violations
      const violations = results.filter(r => !r.passed);
      if (violations.length > 0) {
        violations.forEach(violation => {
          if (!newErrors[violation.parameter]) {
            newErrors[violation.parameter] = violation.message;
          }
        });
      }
      
      setGuardrailResults(results);
    } catch (err) {
      console.error('Guardrail validation error:', err);
      // Continue without guardrail validation if API fails
    }

    setErrors(newErrors);
    setValidating(false);
    
    return Object.keys(newErrors).length === 0;
  };

  const handleContinue = async () => {
    const isValid = await validateForm();
    if (isValid) {
      updateFormData('goals', formValues);
      updateFormData('guardrailResults', guardrailResults);
      setCurrentStep('formulation');
    }
  };

  const renderParameterSection = (section) => {
    return (
      <FormSection key={section.name} title={section.name} meta={null} className="mb-5">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {section.parameters.map(param => (
            <DynamicFormField
              key={param.key}
              field={param}
              value={formValues[param.key]}
              onChange={handleFieldChange}
              error={errors[param.key]}
              disabled={validating}
            />
          ))}
        </div>
      </FormSection>
    );
  };

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="max-w-6xl mx-auto px-6 py-3">
        <div className="mb-3">
          <button 
            onClick={() => setCurrentStep('product-type')}
            className="flex items-center text-slate-600 hover:text-slate-900 mb-2"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Back to Product Selection
          </button>
          
          <h1 className="text-xl font-semibold text-slate-900 mb-1">
            Set Your Goals & Constraints
          </h1>
          <p className="text-sm text-slate-500 italic">
            Define your objectives and requirements for the formulation
          </p>
        </div>

        {apiError && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg flex items-start gap-2">
            <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div>
              <p className="text-yellow-800">{apiError}</p>
            </div>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
          </div>
        ) : (
          <>
            {parameters.map(section => renderParameterSection(section))}

            {/* Guardrail Results (if any) */}
            {guardrailResults.length > 0 && (
              <div className="bg-blue-50 rounded-lg p-4 mb-6">
                <h3 className="font-medium text-blue-900 mb-2">Compliance Check</h3>
                <ul className="space-y-1">
                  {guardrailResults.map((result, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm">
                      <span className={`w-2 h-2 rounded-full ${result.passed ? 'bg-green-500' : 'bg-yellow-500'}`} />
                      <span className={result.passed ? 'text-green-700' : 'text-yellow-700'}>
                        {result.rule}: {result.message}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Navigation buttons */}
            <div className="flex justify-between mt-8">
              <button 
                onClick={() => setCurrentStep('product-type')}
                className="px-5 py-2.5 border border-slate-300 text-slate-700 rounded-md hover:bg-slate-50"
              >
                Back
              </button>
              <button 
                onClick={handleContinue}
                disabled={validating}
                className="px-6 md:px-7 py-2.5 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 disabled:bg-slate-300 disabled:cursor-not-allowed flex items-center shadow-sm"
              >
                {validating ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    Validating...
                  </>
                ) : (
                  <>
                    Continue to Formulation <ChevronRight className="ml-2 w-4 h-4" />
                  </>
                )}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default GoalSetup;
