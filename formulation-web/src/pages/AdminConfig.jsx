import React, { useEffect, useState } from 'react';
import { Search, Edit3, Copy, Trash2, Sliders, Plus, Package, Settings } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import FormSection from '../components/FormSection';
import TaxonomyTree from '../components/TaxonomyTree';
import Modal from '../components/Modal';
import { filterTaxonomiesByQuery } from '../utils/taxonomyTree';

const TabButton = ({ active, children, onClick }) => (
  <button
    className={`px-3 py-2 rounded-md border text-sm ${active ? 'bg-emerald-600 text-white border-emerald-600' : 'bg-white text-slate-700 border-slate-200 hover:bg-slate-50'}`}
    onClick={onClick}
  >
    {children}
  </button>
);

const AdminConfig = () => {
  const { apiRequest } = useAuth();
  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState([]);
  const [error, setError] = useState(null);
  const [selected, setSelected] = useState(null);
  const [bindings, setBindings] = useState([]);
  const [bindingsQ, setBindingsQ] = useState('');
  const [bindingsPage, setBindingsPage] = useState(1);
  const [bindingsPer, setBindingsPer] = useState(10);
  // Accordions always open per request; no minimize controls
  const [answers, setAnswers] = useState([]);
  const [query, setQuery] = useState('');
  const [modal, setModal] = useState(null);
  const [formulationBlocks, setFormulationBlocks] = useState([]);
  const [blockBindings, setBlockBindings] = useState([]);

  // Shared soft header style for accordions
  const style = headerStyleSelected();

  const load = async () => {
    try {
      setLoading(true);
      setError(null);
      const resp = await apiRequest(`/admin/taxonomies`);
      setItems(resp.data || []);
    } catch (e) {
      setError(e.message || 'Failed to load');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => { load(); }, []);

  const handleCreate = async (payload) => {
    const resp = await apiRequest(`/admin/taxonomies`, { method: 'POST', body: JSON.stringify(payload) });
    setItems(prev => [resp.data, ...prev]);
  };

  const handleDelete = async (id) => {
    await apiRequest(`/admin/taxonomies/${id}`, { method: 'DELETE' });
    setItems(prev => prev.filter(i => i._id !== id));
  };

  const renderCreateForm = () => null;

  // Load bindings + answers for selected node
  const loadSelectionData = async (node) => {
    if (!node) { setBindings([]); setAnswers([]); return; }
    const level = node.level;
    const slug = node.slug;
    try {
      const bResp = await apiRequest(`/admin/bindings?level=${encodeURIComponent(level)}&slug=${encodeURIComponent(slug)}`);
      const raw = bResp.data || [];
      // Supplement with merged names from public param-bindings endpoint
      let nameMap = {};
      try {
        const merged = await apiRequest(`/param-bindings?level=${encodeURIComponent(level)}&slug=${encodeURIComponent(slug)}`);
        const mergedArr = merged?.data || [];
        mergedArr.forEach(m => { if (m.parameter_key) nameMap[m.parameter_key] = m.name || m.parameter_key; });
      } catch (_) { /* best-effort */ }
      const b = raw.map(x => ({ ...x, _paramName: nameMap[x.parameter_key] || x.parameter_key }));
      setBindings(b);
      const keys = Array.from(new Set(b.map(x => x.answers_key).filter(Boolean)));
      if (keys.length) {
        const aResp = await apiRequest(`/param-answers?keys=${encodeURIComponent(keys.join(','))}`);
        const map = aResp?.data || {};
        setAnswers(Object.values(map));
      } else {
        setAnswers([]);
      }
    } catch (e) {
      setBindings([]); setAnswers([]);
    }
  };

  const onTreeSelect = (node) => {
    setSelected(node);
    loadSelectionData(node);
  };

  const refreshTaxonomies = async ()=>{ await load(); };

  const handleTreeAction = (action, node) => {
    if (action === 'view') { onTreeSelect(node); return; }
    if (action === 'add-child') { setModal({ type:'add-child', node }); return; }
    if (action === 'add-sibling') { setModal({ type:'add-sibling', node }); return; }
    if (action === 'rename') { setModal({ type:'rename', node, payload:{ name: node.name, slug: node.slug } }); return; }
    if (action === 'move') { setModal({ type:'move', node }); return; }
    if (action === 'delete') { setModal({ type:'delete', node }); return; }
    // Drag-and-drop move
    if (action === 'drop-move') {
      const { dragId, targetId } = node; // here node is payload object
      const drag = items.find(i => String(i._id) === String(dragId));
      const target = items.find(i => String(i._id) === String(targetId));
      if (!drag || !target) return;
      // Only allow category -> industry, sub_category -> category
      const allowed = (target.level === 'industry' && drag.level === 'category') ||
                      (target.level === 'category' && drag.level === 'sub_category');
      if (!allowed) return;
      apiRequest(`/admin/taxonomies/${drag._id}`, { method:'PUT', body: JSON.stringify({ ...drag, parent_id: String(target._id) }) })
        .then(()=> refreshTaxonomies())
        .catch(()=>{});
      return;
    }
  };

  return (
    <div className="min-h-screen bg-slate-50 px-6 py-0">
      <div className="max-w-6xl mx-auto space-y-3">
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-base md:text-lg font-semibold tracking-tight subpixel-antialiased text-black">Admin Config</h1>
              <p className="text-xs md:text-sm text-slate-500 mt-0.5">Manage taxonomies, parameters, answer sets and guardrails.</p>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-12 gap-4">
            <FormSection title={null} className="lg:col-span-5 border border-slate-300 shadow-xl hover:shadow-2xl transition-all duration-200">
              <div className={`mb-2 px-3 py-1.5 rounded-md ${headerStyleSelected().headerBg} ${headerStyleSelected().headerBorder}`}>
                <div className="text-sm font-semibold text-slate-900">Taxonomy ({items.length})</div>
              </div>
              {/* Search box removed: type-ahead fuzzy search via keyboard when tree is focused */}
              <div className="lg:sticky lg:top-2 lg:max-h-[75vh] overflow-auto">
                <TaxonomyTree items={items} onSelect={onTreeSelect} onAction={handleTreeAction} selectedId={selected?._id} />
              </div>
            </FormSection>
            <FormSection title={null} meta={null} className={`lg:col-span-7 border border-slate-300 shadow-xl hover:shadow-2xl transition-all duration-200 ${headerStyleSelected().container}`}>
              {!selected ? (
                <div className="h-[50vh] flex items-center justify-center">
                  <div className="text-sm text-slate-500">Select a taxonomy node to view details.</div>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Breadcrumb removed per request */}

                  {/* Overview Accordion */}
                  <div className="border rounded-lg border-slate-300 bg-white shadow-xl hover:shadow-2xl transition-all duration-200 overflow-hidden">
                    <div className={`flex items-center justify-between px-4 py-2 border-b ${style.headerBg} ${style.headerBorder}`}>
                      <div className="text-sm font-bold text-slate-900">Overview</div>
                    </div>
                    <div className="p-3 md:p-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div>
                          <label className="text-xs text-slate-500 font-medium">Name</label>
                          <div className="h-10 px-3 rounded-md border border-slate-200 flex items-center bg-slate-50">{selected.name}</div>
                        </div>
                        <div>
                          <label className="text-xs text-slate-500 font-medium">Slug</label>
                          <div className="h-10 px-3 rounded-md border border-slate-200 flex items-center bg-slate-50">{selected.slug}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Parameters (Bindings) Accordion */}
                  <div className="border rounded-lg border-slate-300 bg-white shadow-xl hover:shadow-2xl transition-all duration-200 overflow-hidden">
                    <div className={`flex items-center justify-between px-4 py-2 border-b ${style.headerBg} ${style.headerBorder}`}>
                      <div className="text-sm font-bold text-slate-900">Parameters — Bindings</div>
                      <div className="flex items-center gap-2">
                        <button className="p-1.5 rounded-md bg-emerald-600 text-white hover:bg-emerald-700 transition-colors" title="Add" aria-label="Add" onClick={()=>setModal({ type:'add-binding' })}><Plus className="w-4 h-4" /></button>
                      </div>
                    </div>
                    <div className="p-3 md:p-4">
                        {/* Add button moved to title bar */}
                        {bindings.length > 0 && (
                          <div className="flex items-center justify-between mb-3">
                            <BindingsToolbar q={bindingsQ} setQ={setBindingsQ} page={bindingsPage} setPage={setBindingsPage} per={bindingsPer} setPer={setBindingsPer} total={bindings.filter(b => (bindingsQ? (b.parameter_key||'').toLowerCase().includes(bindingsQ.toLowerCase()) : true)).length} />
                          </div>
                        )}
                        {bindings.length === 0 ? (
                          <div className="text-sm text-slate-500">No bindings.</div>
                        ) : (
                          <div className="overflow-x-auto">
                            <table className="w-full text-sm">
                              <thead><tr className="text-left text-slate-600 border-b">
                                <th className="py-2 pr-4">Parameter</th>
                                <th className="py-2 pr-4">Section · Order</th>
                                <th className="py-2 pr-4">Required</th>
                                <th className="py-2 pr-4">Status</th>
                                <th className="py-2 pr-4 w-20">Actions</th>
                              </tr></thead>
                              <tbody onDragOver={(e)=>e.preventDefault()}>
                                {bindings
                                  .filter(b => (bindingsQ? (b.parameter_key||'').toLowerCase().includes(bindingsQ.toLowerCase()) : true))
                                  .slice((bindingsPage-1)*bindingsPer, (bindingsPage)*bindingsPer)
                                  .map(b => (
                                    <BindingRow key={b._id} b={b} selected={selected} apiRequest={apiRequest} reload={()=>loadSelectionData(selected)} siblings={items.filter(i => i.level===selected.level && i.parent_id===selected.parent_id && i.slug!==selected.slug)} setModal={setModal} onInspect={()=>setSelectedBinding(b)} onReorder={async (dragId, overId)=>{
                                      const idx = bindings.findIndex(x=>x._id===dragId);
                                      const overIdx = bindings.findIndex(x=>x._id===overId);
                                      if (idx<0 || overIdx<0 || idx===overIdx) return;
                                      const updated = [...bindings];
                                      const [moved] = updated.splice(idx,1);
                                      updated.splice(overIdx,0,moved);
                                      const seq = updated.map((x,i)=>({ ...x, display:{ ...(x.display||{}), order:(i+1)*10 }}));
                                      for (const row of seq) { try { await apiRequest(`/admin/bindings/${row._id}`, { method:'PUT', body: JSON.stringify(row) }); } catch(e) {} }
                                      setBindings(seq);
                                    }} />
                                  ))}
                              </tbody>
                            </table>
                          </div>
                        )}
                    </div>
                  </div>

                  {/* Formulation Blocks Accordion */}
                  <div className="border rounded-lg border-slate-300 bg-white shadow-xl hover:shadow-2xl transition-all duration-200 overflow-hidden">
                    <div className={`flex items-center justify-between px-4 py-2 border-b ${style.headerBg} ${style.headerBorder}`}>
                      <div className="flex items-center gap-2">
                        <Package className="w-4 h-4 text-emerald-700" />
                        <div className="text-sm font-bold text-slate-900">Formulation Blocks</div>
                      </div>
                      <button 
                        onClick={() => window.open('/admin/formulation-blocks', '_blank')}
                        className="p-1 text-emerald-600 hover:text-emerald-800 hover:bg-emerald-100 rounded transition-colors"
                        title="Manage Blocks"
                      >
                        <Settings className="w-4 h-4" />
                      </button>
                    </div>
                    <div className="p-2">
                      {selected ? (
                        <FormulationBlocksSection 
                          selected={selected}
                          apiRequest={apiRequest}
                          formulationBlocks={formulationBlocks}
                          setFormulationBlocks={setFormulationBlocks}
                          blockBindings={blockBindings}
                          setBlockBindings={setBlockBindings}
                        />
                      ) : (
                        <div className="text-sm text-slate-500 text-center py-4">
                          Select a taxonomy node to manage formulation block bindings.
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Guardrails Accordion */}
                  <div className="border rounded-lg border-slate-300 bg-white shadow-xl hover:shadow-2xl transition-all duration-200 overflow-hidden">
                    <div className={`flex items-center justify-between px-4 py-2 border-b ${style.headerBg} ${style.headerBorder}`}>
                      <div className="text-sm font-bold text-slate-900">Guardrails</div>
                    </div>
                    <div className="p-3 md:p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-sm font-semibold text-slate-900">Rules</h3>
                        <button className="px-2.5 py-1.5 rounded-md bg-emerald-600 text-white text-sm hover:bg-emerald-700 transition-colors">New Rule</button>
                      </div>
                      <div className="text-sm text-slate-500">Guardrails list and rule wizard will appear here.</div>
                    </div>
                  </div>
                </div>
              )}
            </FormSection>
            {/* Removed right inspector to keep two-pane layout as requested */}
          </div>

        {/* Modals */}
        <AdminModals modal={modal} setModal={setModal} selected={selected} apiRequest={apiRequest} reloadSelection={()=>selected && loadSelectionData(selected)} items={items} load={load} />

        {/* Removed legacy tables to declutter; creation now via tree context menu and right-panel modals */}
      </div>
    </div>
  );
};

const Input = (props) => (
  <input {...props} className={`w-full h-10 px-3 border rounded-md border-slate-200 focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 ${props.className||''}`} />
);

const CreateTaxonomy = ({ onCreate }) => {
  const [form, setForm] = useState({ slug: '', name: '', level: 'industry', parent_id: '' });
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
      <Input placeholder="slug" value={form.slug} onChange={e=>setForm({...form, slug:e.target.value})} />
      <Input placeholder="name" value={form.name} onChange={e=>setForm({...form, name:e.target.value})} />
      <select className="h-10 px-3 border rounded-md border-slate-200" value={form.level} onChange={e=>setForm({...form, level:e.target.value})}>
        <option value="industry">industry</option>
        <option value="category">category</option>
        <option value="sub_category">sub_category</option>
      </select>
      <Input placeholder="parent_id (optional)" value={form.parent_id} onChange={e=>setForm({...form, parent_id:e.target.value})} />
      <div className="md:col-span-4 flex justify-end">
        <button onClick={()=>onCreate({ ...form, parent_id: form.parent_id||null, status:'active' })} className="px-4 py-2 bg-emerald-600 text-white rounded-md">Create</button>
      </div>
    </div>
  );
};

const CreateParameter = ({ onCreate }) => {
  const [form, setForm] = useState({ key:'', name:'', type:'string', answers_key:'' });
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
      <Input placeholder="key" value={form.key} onChange={e=>setForm({...form, key:e.target.value})} />
      <Input placeholder="name" value={form.name} onChange={e=>setForm({...form, name:e.target.value})} />
      <select className="h-10 px-3 border rounded-md border-slate-200" value={form.type} onChange={e=>setForm({...form, type:e.target.value})}>
        <option>string</option>
        <option>number</option>
        <option>enum</option>
        <option>boolean</option>
        <option>array</option>
        <option>table</option>
      </select>
      <Input placeholder="answers_key (optional)" value={form.answers_key} onChange={e=>setForm({...form, answers_key:e.target.value})} />
      <div className="md:col-span-4 flex justify-end">
        <button onClick={()=>onCreate({ ...form, answers_key: form.answers_key||undefined })} className="px-4 py-2 bg-emerald-600 text-white rounded-md">Create</button>
      </div>
    </div>
  );
};

const CreateBinding = ({ onCreate }) => {
  const [form, setForm] = useState({ parameter_key:'', level:'category', slug:'', required:false, status:'active' });
  return (
    <div className="grid grid-cols-1 md:grid-cols-6 gap-3">
      <Input placeholder="parameter_key" value={form.parameter_key} onChange={e=>setForm({...form, parameter_key:e.target.value})} />
      <select className="h-10 px-3 border rounded-md border-slate-200" value={form.level} onChange={e=>setForm({...form, level:e.target.value})}>
        <option>industry</option>
        <option>category</option>
        <option>sub_category</option>
      </select>
      <Input placeholder="target slug" value={form.slug} onChange={e=>setForm({...form, slug:e.target.value})} />
      <select className="h-10 px-3 border rounded-md border-slate-200" value={form.required ? 'yes':'no'} onChange={e=>setForm({...form, required: e.target.value==='yes'})}>
        <option value="no">required: no</option>
        <option value="yes">required: yes</option>
      </select>
      <select className="h-10 px-3 border rounded-md border-slate-200" value={form.status} onChange={e=>setForm({...form, status:e.target.value})}>
        <option value="active">status: active</option>
        <option value="inactive">status: inactive</option>
      </select>
      <div className="md:col-span-1 flex justify-end">
        <button onClick={()=>onCreate({ parameter_key:form.parameter_key, target:{ level:form.level, slug:form.slug }, required:form.required, status:form.status })} className="px-4 py-2 bg-emerald-600 text-white rounded-md">Create</button>
      </div>
    </div>
  );
};

const CreateAnswer = ({ onCreate }) => {
  const [key, setKey] = useState('');
  const [name, setName] = useState('');
  const [options, setOptions] = useState('value:label, value:label');
  const buildOptions = () => options.split(',').map(s=>s.trim()).filter(Boolean).map(pair=>{
    const [value, label] = pair.split(':').map(p=>p?.trim());
    return { value, label: label || value };
  });
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
      <Input placeholder="key" value={key} onChange={e=>setKey(e.target.value)} />
      <Input placeholder="name" value={name} onChange={e=>setName(e.target.value)} />
      <Input placeholder="options (value:label, ...)" value={options} onChange={e=>setOptions(e.target.value)} />
      <div className="md:col-span-3 flex justify-end">
        <button onClick={()=>onCreate({ key, name, options: buildOptions() })} className="px-4 py-2 bg-emerald-600 text-white rounded-md">Create</button>
      </div>
    </div>
  );
};

// FormulationBlocksSection component
const FormulationBlocksSection = ({ selected, apiRequest, formulationBlocks, setFormulationBlocks, blockBindings, setBlockBindings }) => {
  const [allBlocks, setAllBlocks] = useState([]);
  const [boundBlocks, setBoundBlocks] = useState([]);
  const [loading, setLoading] = useState(false);

  // Load available blocks and current bindings
  useEffect(() => {
    if (selected) {
      loadBlocksAndBindings();
    }
  }, [selected]);

  const loadBlocksAndBindings = async () => {
    setLoading(true);
    try {
      // Load all formulation blocks
      const blocksResponse = await apiRequest('/formulation-blocks');
      setAllBlocks(blocksResponse.data || []);

      // Load bindings for the selected taxonomy node
      const bindingsResponse = await apiRequest(`/formulation-blocks/bindings?level=${selected.level}&slug=${selected.slug}`);
      setBoundBlocks(bindingsResponse.data || []);
    } catch (error) {
      console.error('Error loading blocks and bindings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleBlock = async (block, isCurrentlyBound) => {
    try {
      if (isCurrentlyBound) {
        // Find the binding and remove it
        const binding = boundBlocks.find(b => b.block_key === block.key);
        if (binding) {
          await apiRequest(`/formulation-blocks/bindings/${binding._id}`, {
            method: 'DELETE'
          });
        }
      } else {
        // Create new binding
        await apiRequest('/formulation-blocks/bindings', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            level: selected.level,
            slug: selected.slug,
            block_key: block.key,
            is_included: true,
            is_required: false,
            display_order: 0
          })
        });
      }
      await loadBlocksAndBindings();
    } catch (error) {
      console.error('Error toggling block binding:', error);
    }
  };

  if (loading) {
    return <div className="text-sm text-slate-500 text-center py-2">Loading blocks...</div>;
  }

  const boundBlockKeys = boundBlocks.map(b => b.block_key);
  const boundCount = boundBlocks.length;
  const availableCount = allBlocks.length - boundCount;

  return (
    <div className="space-y-2">
      {/* Summary */}
      <div className="flex items-center justify-between text-xs text-slate-600 font-medium px-1">
        <span>Bound Blocks ({boundCount})</span>
        <span>Available Blocks ({availableCount})</span>
      </div>

      {/* Blocks Grid */}
      {allBlocks.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-64 overflow-y-auto">
          {allBlocks.map((block) => {
            const isCurrentlyBound = boundBlockKeys.includes(block.key);
            return (
              <div 
                key={block.key} 
                className={`flex items-start gap-2 p-2 border rounded-md transition-colors ${
                  isCurrentlyBound 
                    ? 'bg-emerald-50 border-emerald-200' 
                    : 'bg-slate-50 border-slate-200 hover:bg-emerald-50 hover:border-emerald-200'
                }`}
              >
                <input
                  type="checkbox"
                  checked={isCurrentlyBound}
                  onChange={() => handleToggleBlock(block, isCurrentlyBound)}
                  className="mt-0.5 w-4 h-4 text-emerald-600 border-slate-300 rounded focus:ring-emerald-500 focus:ring-1"
                />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-slate-900 leading-tight truncate">{block.name}</div>
                  <div className="text-xs text-slate-500 mt-0.5 leading-tight line-clamp-2">{block.description}</div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-xs text-slate-500 italic text-center py-3">No formulation blocks available.</div>
      )}
    </div>
  );
};

export default AdminConfig;

// Toolbar: search + pagination for bindings
const BindingsToolbar = ({ q, setQ, page, setPage, per, setPer, total }) => {
  const totalPages = Math.max(1, Math.ceil(total / per));
  useEffect(()=>{ setPage(1); }, [q, per, total]);
  return (
    <div className="w-full flex flex-col gap-2">
      <div className="flex items-center gap-2">
        <input value={q} onChange={e=>setQ(e.target.value)} placeholder="Search bindings" className="h-9 px-3 border rounded-md border-slate-300" />
        <span className="text-xs text-slate-500">{total} items</span>
        <div className="ml-auto flex items-center gap-2">
          <button disabled={page<=1} onClick={()=>setPage(p=>Math.max(1,p-1))} className="px-2 py-1 text-sm rounded-md border border-slate-300 disabled:opacity-50">Prev</button>
          <span className="text-xs text-slate-600">{page}/{totalPages}</span>
          <button disabled={page>=totalPages} onClick={()=>setPage(p=>Math.min(totalPages,p+1))} className="px-2 py-1 text-sm rounded-md border border-slate-300 disabled:opacity-50">Next</button>
          <select value={per} onChange={e=>setPer(Number(e.target.value)||10)} className="h-9 px-2 border rounded-md border-slate-300">
            <option>10</option>
            <option>20</option>
            <option>50</option>
          </select>
        </div>
      </div>
    </div>
  );
};

// Inline binding row with edit/duplicate/delete
const BindingRow = ({ b, selected, apiRequest, reload, siblings, setModal, onInspect, onReorder }) => {
  const [edit, setEdit] = useState(false);
  const [section, setSection] = useState(b.display?.section || 'General');
  const [order, setOrder] = useState(b.display?.order ?? 0);
  const [required, setRequired] = useState(!!b.required);
  const [status, setStatus] = useState(b.status || 'active');
  const [dupOpen, setDupOpen] = useState(false);
  const [targetSlug, setTargetSlug] = useState('');

  const save = async () => {
    await apiRequest(`/admin/bindings/${b._id}`, { method:'PUT', body: JSON.stringify({ ...b, required, status, display:{ ...(b.display||{}), section, order } })});
    setEdit(false);
    await reload();
  };

  const duplicate = async () => {
    if (!targetSlug) return;
    await apiRequest('/admin/bindings', { method:'POST', body: JSON.stringify({ ...b, target:{ level:selected.level, slug: targetSlug }, _id: undefined, created_at: undefined, updated_at: undefined }) });
    setDupOpen(false); setTargetSlug('');
  };

  return (
    <tr className="border-b last:border-0" draggable onDragStart={(e)=>{ e.dataTransfer.setData('text/binding-id', b._id); }} onDragOver={(e)=>{ e.preventDefault(); e.dataTransfer.setData('text/binding-over', b._id); }}>
      <td className="py-2 pr-4">
        <button className="px-2 py-1 rounded-md bg-slate-100 text-slate-800 hover:bg-slate-200" onClick={onInspect} title="Inspect Binding">
          {(b._paramName || b.parameter_key || '').replace(/_/g,' ').replace(/\b\w/g, c=>c.toUpperCase())}
        </button>
      </td>
      <td className="py-2 pr-4">
        {edit ? (
          <div className="flex items-center gap-2">
            <input value={section} onChange={e=>setSection(e.target.value)} className="h-8 px-2 border rounded-md border-slate-200 w-40" />
            <input type="number" value={order} onChange={e=>setOrder(Number(e.target.value)||0)} className="h-8 px-2 border rounded-md border-slate-200 w-20" />
          </div>
        ) : (
          <span>{section} · {order}</span>
        )}
      </td>
      <td className="py-2 pr-4">
        {edit ? (
          <select value={required ? 'yes':'no'} onChange={e=>setRequired(e.target.value==='yes')} className="h-8 px-2 border rounded-md border-slate-200">
            <option value="no">no</option>
            <option value="yes">yes</option>
          </select>
        ) : (
          <span>{required ? 'yes':'no'}</span>
        )}
      </td>
      <td className="py-2 pr-4">
        {edit ? (
          <select value={status} onChange={e=>setStatus(e.target.value)} className="h-8 px-2 border rounded-md border-slate-200">
            <option value="active">active</option>
            <option value="inactive">inactive</option>
          </select>
        ) : (
          <button 
            onClick={async () => {
              const newStatus = status === 'active' ? 'inactive' : 'active';
              setStatus(newStatus);
              await apiRequest(`/admin/bindings/${b._id}`, { 
                method:'PUT', 
                body: JSON.stringify({ ...b, status: newStatus })
              });
              await reload();
            }}
            className={`px-2 py-1 rounded-full text-xs font-medium cursor-pointer hover:opacity-80 ${
              status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}
            title={`Click to ${status === 'active' ? 'deactivate' : 'activate'}`}
          >
            {status}
          </button>
        )}
      </td>
      <td className="py-2 pr-4">
        {edit ? (
          <div className="flex gap-2">
            <button className="text-emerald-700 hover:underline" onClick={save}>Save</button>
            <button className="text-slate-600 hover:underline" onClick={()=>setEdit(false)}>Cancel</button>
          </div>
        ) : (
          <div className="flex gap-1.5">
            <button className="p-1.5 rounded-md hover:bg-slate-100 text-slate-700" title="Quick Edit" aria-label="Quick Edit" onClick={()=>setEdit(true)}>
              <Edit3 className="w-4 h-4" />
            </button>
            <button className="p-1.5 rounded-md hover:bg-slate-100 text-slate-700" title="Advanced" aria-label="Advanced" onClick={()=>setModal({ type:'edit-binding', binding: b })}>
              <Sliders className="w-4 h-4" />
            </button>
            <button className="p-1.5 rounded-md hover:bg-slate-100 text-slate-700" title="Duplicate" aria-label="Duplicate" onClick={()=>setDupOpen(true)}>
              <Copy className="w-4 h-4" />
            </button>
            <button className="p-1.5 rounded-md hover:bg-slate-100 text-red-600" title="Delete" aria-label="Delete" onClick={()=>setModal({ type:'delete-binding', binding: b })}>
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        )}
        <Modal open={dupOpen} title="Duplicate Binding" onClose={()=>setDupOpen(false)} onSubmit={duplicate} submitLabel="Duplicate">
          <div className="space-y-3">
            <div className="text-sm text-slate-600">Duplicate to sibling {selected.level}:</div>
            <select value={targetSlug} onChange={e=>setTargetSlug(e.target.value)} className="w-full h-10 px-3 border rounded-md border-slate-200">
              <option value="">Select target</option>
              {siblings.map(s => <option key={s.slug} value={s.slug}>{s.name} ({s.slug})</option>)}
            </select>
          </div>
        </Modal>
      </td>
    </tr>
  );
};

// Right Pane: Binding Inspector (focused editor)
const BindingInspector = ({ binding, setBinding, apiRequest }) => {
  const [model, setModel] = useState(() => ({
    section: binding.display?.section || 'General',
    order: binding.display?.order ?? 0,
    required: !!binding.required,
    status: binding.status || 'active',
    widget_type: binding.widget?.type || 'text',
    min: binding.validators?.min ?? '',
    max: binding.validators?.max ?? '',
    unit: binding.guidance?.unit ?? '',
    average: binding.guidance?.average ?? '',
    default_value: binding.default_value ?? '',
    answers_key: binding.answers_key || ''
  }));

  // Debounced autosave
  useEffect(() => {
    const t = setTimeout(async () => {
      const updated = {
        ...binding,
        required: model.required,
        status: model.status,
        default_value: model.default_value,
        answers_key: model.answers_key || undefined,
        display: { ...(binding.display||{}), section: model.section, order: Number(model.order||0) },
        widget: { ...(binding.widget||{}), type: model.widget_type },
        guidance: { ...(binding.guidance||{}), unit: model.unit || undefined, average: model.average || undefined },
        validators: { ...(binding.validators||{}), min: model.min === '' ? undefined : Number(model.min), max: model.max === '' ? undefined : Number(model.max) }
      };
      await apiRequest(`/admin/bindings/${binding._id}`, { method:'PUT', body: JSON.stringify(updated) });
      setBinding(updated);
    }, 600);
    return () => clearTimeout(t);
  }, [model]);

  const Field = ({ label, children }) => (
    <div>
      <label className="text-xs text-slate-500">{label}</label>
      {children}
    </div>
  );

  return (
    <div className="space-y-3 text-sm">
      <div className="text-slate-900 font-semibold">Binding Inspector</div>
      <div className="grid grid-cols-2 gap-3">
        <Field label="Parameter">
          <div className="h-9 px-3 rounded-md border border-slate-200 flex items-center bg-slate-50">{binding.parameter_key}</div>
        </Field>
        <Field label="Widget Type">
          <select value={model.widget_type} onChange={e=>setModel(m=>({ ...m, widget_type:e.target.value }))} className="h-9 px-2 border rounded-md border-slate-200">
            <option>text</option>
            <option>number</option>
            <option>slider</option>
            <option>select</option>
            <option>table</option>
          </select>
        </Field>
        <Field label="Section">
          <input value={model.section} onChange={e=>setModel(m=>({ ...m, section:e.target.value }))} className="h-9 px-2 border rounded-md border-slate-200" />
        </Field>
        <Field label="Order">
          <input type="number" value={model.order} onChange={e=>setModel(m=>({ ...m, order:e.target.value }))} className="h-9 px-2 border rounded-md border-slate-200" />
        </Field>
        <Field label="Required">
          <select value={model.required ? 'yes':'no'} onChange={e=>setModel(m=>({ ...m, required: e.target.value==='yes' }))} className="h-9 px-2 border rounded-md border-slate-200">
            <option value="no">no</option>
            <option value="yes">yes</option>
          </select>
        </Field>
        <Field label="Status">
          <select value={model.status} onChange={e=>setModel(m=>({ ...m, status: e.target.value }))} className="h-9 px-2 border rounded-md border-slate-200">
            <option value="active">active</option>
            <option value="inactive">inactive</option>
          </select>
        </Field>
        <Field label="Answers Key">
          <input value={model.answers_key} onChange={e=>setModel(m=>({ ...m, answers_key:e.target.value }))} className="h-9 px-2 border rounded-md border-slate-200" />
        </Field>
        <Field label="Min">
          <input type="number" value={model.min} onChange={e=>setModel(m=>({ ...m, min:e.target.value }))} className="h-9 px-2 border rounded-md border-slate-200" />
        </Field>
        <Field label="Max">
          <input type="number" value={model.max} onChange={e=>setModel(m=>({ ...m, max:e.target.value }))} className="h-9 px-2 border rounded-md border-slate-200" />
        </Field>
        <Field label="Unit">
          <input value={model.unit} onChange={e=>setModel(m=>({ ...m, unit:e.target.value }))} className="h-9 px-2 border rounded-md border-slate-200" />
        </Field>
        <Field label="Recommended (avg)">
          <input type="number" value={model.average} onChange={e=>setModel(m=>({ ...m, average:e.target.value }))} className="h-9 px-2 border rounded-md border-slate-200" />
        </Field>
        <Field label="Default Value">
          <input value={model.default_value} onChange={e=>setModel(m=>({ ...m, default_value:e.target.value }))} className="h-9 px-2 border rounded-md border-slate-200" />
        </Field>
      </div>
      <div className="text-xs text-slate-500">Changes auto‑save (0.6s debounce).</div>
    </div>
  );
};

// Header color styles mapped to taxonomy level (safelisted classes)
// Neutral header style (no emerald tint)
const headerStyleSelected = () => ({ headerBg: 'bg-slate-100', headerBorder: 'border-slate-200', container: '' });

// Left tree header helper for same background
const headerHighlightBg = () => 'bg-emerald-200 border-emerald-300';
const AdminModals = ({ modal, setModal, selected, apiRequest, reloadSelection, items, load }) => {
  if (!modal) return null;
  const close = () => setModal(null);
  const node = modal.node;

  const submit = async () => {
    if (modal.type === 'add-binding') {
      const { parameter_key, section, order } = modal.payload || {};
      if (!parameter_key || !selected) return;
      await apiRequest('/admin/bindings', { method:'POST', body: JSON.stringify({ parameter_key, target:{ level:selected.level, slug:selected.slug }, display:{ section: section || 'General', order: Number(order)||1 } })});
      await reloadSelection();
    }
    if (modal.type === 'edit-binding') {
      const { binding, payload } = modal;
      if (!binding) return;
      const updated = {
        ...binding,
        required: payload?.required ?? binding.required,
        default_value: payload?.default_value ?? binding.default_value,
        answers_key: payload?.answers_key ?? binding.answers_key,
        display: { ...(binding.display||{}), section: payload?.section ?? binding.display?.section, order: Number(((payload?.order ?? binding.display?.order) || 0)) },
        widget: { ...(binding.widget||{}), type: payload?.widget_type || binding.widget?.type },
        guidance: { ...(binding.guidance||{}), unit: payload?.unit ?? binding.guidance?.unit, average: payload?.average ?? binding.guidance?.average },
        validators: { ...(binding.validators||{}), min: payload?.min ?? binding.validators?.min, max: payload?.max ?? binding.validators?.max }
      };
      await apiRequest(`/admin/bindings/${binding._id}`, { method:'PUT', body: JSON.stringify(updated) });
      await reloadSelection();
    }
    if (modal.type === 'delete-binding') {
      const { binding } = modal;
      if (!binding) return;
      await apiRequest(`/admin/bindings/${binding._id}`, { method:'DELETE' });
      await reloadSelection();
    }
    if (modal.type === 'add-child') {
      const nextLevel = node.level === 'industry' ? 'category' : node.level === 'category' ? 'sub_category' : null;
      const { name, slug } = modal.payload || {};
      if (!nextLevel || !name || !slug) return;
      await apiRequest('/admin/taxonomies', { method:'POST', body: JSON.stringify({ name, slug, level: nextLevel, parent_id: String(node._id), status:'active' })});
      await load();
    }
    if (modal.type === 'add-sibling') {
      const { name, slug } = modal.payload || {};
      if (!name || !slug) return;
      await apiRequest('/admin/taxonomies', { method:'POST', body: JSON.stringify({ name, slug, level: node.level, parent_id: node.parent_id || null, status:'active' })});
      await load();
    }
    if (modal.type === 'rename') {
      const { name, slug } = modal.payload || {};
      await apiRequest(`/admin/taxonomies/${node._id}`, { method:'PUT', body: JSON.stringify({ ...node, name, slug })});
      await load();
    }
    if (modal.type === 'move') {
      const { parent_id } = modal.payload || {};
      await apiRequest(`/admin/taxonomies/${node._id}`, { method:'PUT', body: JSON.stringify({ ...node, parent_id: parent_id || null })});
      await load();
    }
    if (modal.type === 'delete') {
      await apiRequest(`/admin/taxonomies/${node._id}`, { method:'DELETE' });
      await load();
    }
    close();
  };

  const candidateParents = () => {
    if (!node || modal.type !== 'move') return [];
    if (node.level === 'category') return items.filter(i => i.level==='industry');
    if (node.level === 'sub_category') return items.filter(i => i.level==='category');
    return [];
  };

  const titleMap = {
    'add-binding': 'Add Binding',
    'edit-binding': 'Edit Binding',
    'delete-binding': 'Delete Binding',
    'add-child': `Add child under ${node?.name}`,
    'add-sibling': `Add sibling next to ${node?.name}`,
    'rename': `Rename ${node?.name}`,
    'move': `Move ${node?.name}`,
    'delete': `Delete ${node?.name}`
  };

  return (
    <>
      {modal.type === 'add-binding' && (
        <Modal open title={titleMap[modal.type]} onClose={close} onSubmit={submit} submitLabel="Add">
          <div className="space-y-3">
            <input placeholder="parameter_key" className="w-full h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.parameter_key||''} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), parameter_key: e.target.value } })} />
            <div className="grid grid-cols-2 gap-3">
              <input placeholder="section (e.g., formulation)" className="h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.section||''} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), section: e.target.value } })} />
              <input type="number" placeholder="order" className="h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.order||''} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), order: e.target.value } })} />
            </div>
          </div>
        </Modal>
      )}
      {modal.type === 'edit-binding' && (
        <Modal open title={titleMap[modal.type]} onClose={close} onSubmit={submit} submitLabel="Save">
          <div className="grid grid-cols-2 gap-3">
            <input placeholder="section" className="h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.section ?? modal.binding?.display?.section ?? ''} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), section:e.target.value } })} />
            <input type="number" placeholder="order" className="h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.order ?? modal.binding?.display?.order ?? 0} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), order:e.target.value } })} />
            <select className="h-10 px-3 border rounded-md border-slate-200" value={(modal.payload?.required ?? modal.binding?.required) ? 'yes':'no'} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), required: e.target.value==='yes' } })}>
              <option value="no">required: no</option>
              <option value="yes">required: yes</option>
            </select>
            <input placeholder="answers_key (optional)" className="h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.answers_key ?? modal.binding?.answers_key ?? ''} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), answers_key:e.target.value } })} />
            <input placeholder="widget.type (text/select/slider)" className="h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.widget_type ?? modal.binding?.widget?.type ?? ''} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), widget_type:e.target.value } })} />
            <input placeholder="default_value" className="h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.default_value ?? (modal.binding?.default_value ?? '')} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), default_value:e.target.value } })} />
            <input placeholder="validators.min" className="h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.min ?? (modal.binding?.validators?.min ?? '')} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), min:e.target.value } })} />
            <input placeholder="validators.max" className="h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.max ?? (modal.binding?.validators?.max ?? '')} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), max:e.target.value } })} />
            <input placeholder="guidance.unit" className="h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.unit ?? (modal.binding?.guidance?.unit ?? '')} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), unit:e.target.value } })} />
            <input placeholder="guidance.average" className="h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.average ?? (modal.binding?.guidance?.average ?? '')} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), average:e.target.value } })} />
          </div>
        </Modal>
      )}

      {modal.type !== 'add-binding' && modal.type !== 'edit-binding' && (
        <Modal open title={titleMap[modal.type]} onClose={close} onSubmit={submit} submitLabel={modal.type==='delete' ? 'Delete' : 'Save'} destructive={modal.type==='delete'}>
          {(modal.type === 'add-child' || modal.type === 'add-sibling') && (
            <div className="space-y-3">
              <input placeholder="Name" className="w-full h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.name||''} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), name:e.target.value, slug:(modal.payload?.slug)||e.target.value?.toLowerCase().replace(/\s+/g,'-') } })} />
              <input placeholder="Slug (kebab-case)" className="w-full h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.slug||''} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), slug:e.target.value } })} />
            </div>
          )}
          {modal.type === 'rename' && (
            <div className="space-y-3">
              <input placeholder="Name" className="w-full h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.name||''} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), name:e.target.value } })} />
              <input placeholder="Slug (kebab-case)" className="w-full h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.slug||''} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), slug:e.target.value } })} />
            </div>
          )}
          {modal.type === 'move' && (
            <div className="space-y-2">
              <div className="text-sm text-slate-600">Select new parent</div>
              <select className="w-full h-10 px-3 border rounded-md border-slate-200" value={modal.payload?.parent_id||''} onChange={e=>setModal({ ...modal, payload:{ ...(modal.payload||{}), parent_id:e.target.value } })}>
                <option value="">(root)</option>
                {candidateParents().map(p => <option key={p._id} value={p._id}>{p.name} ({p.slug})</option>)}
              </select>
              {node?.level!=='industry' && (
                <p className="text-xs text-slate-500">Only industries can be at root for categories; categories can be parents for sub-categories.</p>
              )}
            </div>
          )}
          {(modal.type === 'delete' || modal.type === 'delete-binding') && (
            <div className="text-sm text-slate-700">Are you sure you want to delete <span className="font-semibold">{node?.name}</span>? This cannot be undone.</div>
          )}
        </Modal>
      )}
    </>
  );
};
