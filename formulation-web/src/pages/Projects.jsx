import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FolderOpen, 
  Plus, 
  Search, 
  Filter,
  Calendar,
  Clock,
  MoreVertical,
  Edit,
  Trash2,
  Play,
  Grid,
  List,
  ChevronDown,
  TrendingUp,
  Package,
  Layers,
  FileText,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  X
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useApp } from '../context/AppContext';
import FormulationWizardModal from '../components/FormulationWizardModal';
import { mapStatus } from '../utils/colorUtils';
import { formatDate, formatTimeAgo, getStatusBadgeClass } from '../utils/dateUtils';
import { projectApi } from '../utils/apiUtils';

const Projects = () => {
  const { user, apiRequest } = useAuth();
  const { setCurrentStep, updateFormData } = useApp();
  const navigate = useNavigate();
  const [projects, setProjects] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [viewMode, setViewMode] = useState('grid');
  const [showWizardModal, setShowWizardModal] = useState(false);
  const [sortBy, setSortBy] = useState('recent');
  const [deleteConfirm, setDeleteConfirm] = useState({ show: false, project: null });
  const [retryConfirm, setRetryConfirm] = useState({ show: false, project: null });

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      setIsLoading(true);
      const response = await projectApi.getAll(1000);
      if (response.success) {
        setProjects((response.data || []).map(p => ({ ...p, status: mapStatus(p.status) })));
      } else {
        console.error('Failed to fetch projects:', response.error);
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteProject = async (projectId) => {
    try {
      const response = await projectApi.delete(projectId);
      if (response.success) {
        setDeleteConfirm({ show: false, project: null });
        await fetchProjects();
      } else {
        alert('Failed to delete project');
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      alert('Failed to delete project');
    }
  };

  const showDeleteDialog = (project) => {
    setDeleteConfirm({ show: true, project });
  };

  const handleOpenProject = (project) => {
    const projectId = project._id || project.id;
    updateFormData('selectedProject', { id: projectId, name: project.name, description: project.description, industry: project.industry, product_type: project.product_type });
    updateFormData('projectIdToLoad', projectId);
    updateFormData('loadFromDatabase', true);
    updateFormData('forceOverlay', true);
    navigate(`/playground/${projectId}`);
  };

  const handleRetryProject = (project, event) => {
    event.stopPropagation(); // Prevent card click
    setRetryConfirm({ show: true, project });
  };

  const confirmRetryProject = async () => {
    if (!retryConfirm.project) return;

    try {
      const response = await projectApi.retry(retryConfirm.project._id);
      if (response.success) {
        // Refresh the projects list to show updated status
        await fetchProjects();
        alert('Project has been retried successfully and added to the queue.');
      }
      setRetryConfirm({ show: false, project: null });
    } catch (error) {
      console.error('❌ Error retrying project:', error);
      alert('Failed to retry project. Please try again.');
      setRetryConfirm({ show: false, project: null });
    }
  };

  const handleStartNewProject = () => {
    navigate('/new-project');
  };

  const handleWizardComplete = async (generatedFormulation) => {
    console.log('Project created:', generatedFormulation);
    
    try {
      // Store the generated formulation in context
      updateFormData('generatedFormulation', generatedFormulation);
      
      // Refresh projects list to include new project
      await fetchProjects();
      
      // Close modal
      setShowWizardModal(false);
      
      // Navigate to playground if a project was created
      if (generatedFormulation?.projectId) {
        navigate(`/playground/${generatedFormulation.projectId}`);
      } else {
        setCurrentStep('playground');
      }
      
      console.log('✅ Project creation completed successfully');
    } catch (error) {
      console.error('❌ Error handling completed formulation:', error);
      alert('An error occurred while processing the formulation. Please try again.');
    }
  };

  // getStatusBadgeClass moved to shared dateUtils.js

  const getStatusLabel = (status) => {
    switch (status) {
      case 'queued':
        return 'Queued';
      case 'generating':
        return 'Generating';
      case 'ready':
        return 'Ready';
      case 'failed':
        return 'Failed';
      case 'closed':
        return 'Closed';
      case 'archived':
        return 'Archived';
      default:
        return status || 'Queued';
    }
  };

  const getIndustryIcon = (industry) => {
    switch (industry) {
      case 'beverages':
        return '🥤';
      case 'nutraceuticals':
        return '💊';
      case 'cosmetics':
        return '✨';
      default:
        return '📦';
    }
  };

  // formatDate and formatTimeAgo moved to shared dateUtils.js

  // Sort projects
  const sortedProjects = [...projects].sort((a, b) => {
    switch (sortBy) {
      case 'recent':
        return new Date(b.updated_at || b.created_at) - new Date(a.updated_at || a.created_at);
      case 'name':
        return a.name.localeCompare(b.name);
      case 'status':
        return (a.status || '').localeCompare(b.status || '');
      default:
        return 0;
    }
  });

  const filteredProjects = sortedProjects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          project.description?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterStatus === 'all' || project.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const stats = {
    total: projects.length,
    active: projects.filter(p => p.status === 'in_progress').length,
    draft: projects.filter(p => p.status === 'draft').length,
    completed: projects.filter(p => p.status === 'completed').length
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page Header (compact) */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-3">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg font-semibold text-gray-900">Projects</h1>
              <p className="mt-0.5 text-xs text-gray-500">
                Manage your formulation projects and track progress
              </p>
            </div>
            <button
              onClick={handleStartNewProject}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center transition-all shadow-sm hover:shadow-md"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Project
            </button>
          </div>
          {/* Stats Bar */}
          <div className="mt-3 grid grid-cols-4 gap-3">
            <div className="bg-gray-50 rounded-lg px-4 py-3">
              <div className="text-2xl font-semibold text-gray-900">{stats.total}</div>
              <div className="text-xs text-gray-500 uppercase tracking-wide">Total Projects</div>
            </div>
            <div className="bg-blue-50 rounded-lg px-4 py-3">
              <div className="text-2xl font-semibold text-blue-700">{stats.active}</div>
              <div className="text-xs text-blue-600 uppercase tracking-wide">In Progress</div>
            </div>
            <div className="bg-gray-50 rounded-lg px-4 py-3">
              <div className="text-2xl font-semibold text-gray-600">{stats.draft}</div>
              <div className="text-xs text-gray-500 uppercase tracking-wide">Drafts</div>
            </div>
            <div className="bg-green-50 rounded-lg px-4 py-3">
              <div className="text-2xl font-semibold text-green-700">{stats.completed}</div>
              <div className="text-xs text-green-600 uppercase tracking-wide">Completed</div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters Bar */}
      <div className="bg-white border-b border-gray-200 px-8 py-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search projects..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-gray-50"
            />
          </div>
          
          {/* Filter Controls */}
          <div className="flex gap-3">
            {/* Status Filter */}
            <div className="relative">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="appearance-none px-4 py-2 pr-10 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white cursor-pointer"
              >
                <option value="all">All Status</option>
                <option value="queued">Queued</option>
                <option value="generating">Generating</option>
                <option value="ready">Ready</option>
                <option value="failed">Failed</option>
                <option value="closed">Closed</option>
                <option value="archived">Archived</option>
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
            </div>

            {/* Sort By */}
            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="appearance-none px-4 py-2 pr-10 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white cursor-pointer"
              >
                <option value="recent">Most Recent</option>
                <option value="name">Name</option>
                <option value="status">Status</option>
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
            </div>
            
            {/* View Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded transition-all ${
                  viewMode === 'grid' 
                    ? 'bg-white shadow-sm text-gray-700' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="Grid view"
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded transition-all ${
                  viewMode === 'list' 
                    ? 'bg-white shadow-sm text-gray-700' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="List view"
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="px-6 py-4">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
          </div>
        ) : filteredProjects.length === 0 ? (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-10 text-center">
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FolderOpen className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
              <p className="text-gray-500 mb-6">
                {searchQuery || filterStatus !== 'all' 
                  ? 'Try adjusting your search or filters'
                  : 'Get started by creating your first formulation project'}
              </p>
              {!searchQuery && filterStatus === 'all' && (
                <button
                  onClick={handleStartNewProject}
                  className="bg-green-600 text-white px-6 py-2.5 rounded-lg hover:bg-green-700 inline-flex items-center transition-all shadow-sm hover:shadow-md"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create First Project
                </button>
              )}
            </div>
          </div>
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
            {filteredProjects.map((project) => (
              <div
                key={project._id}
                className={`relative bg-white rounded-lg border-2 cursor-pointer transition-all hover:border-green-400 group ${
                  project.industry === 'beverages' ? 'border-blue-300 bg-blue-50 hover:bg-blue-100' :
                  project.industry === 'nutraceuticals' ? 'border-green-300 bg-green-50 hover:bg-green-100' :
                  project.industry === 'cosmetics' ? 'border-purple-300 bg-purple-50 hover:bg-purple-100' :
                  'border-gray-300 bg-gray-50 hover:bg-gray-100'
                }`}
                onClick={() => handleOpenProject(project)}
              >
                {/* Status Icon */}
                <div className="absolute top-2 right-2">
                  {project.status === 'completed' ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : project.status === 'in_progress' ? (
                    <Clock className="w-4 h-4 text-blue-600" />
                  ) : project.status === 'queued' ? (
                    <Clock className="w-4 h-4 text-blue-600" />
                  ) : project.status === 'failed' ? (
                    <AlertTriangle className="w-4 h-4 text-red-600" />
                  ) : (
                    <FileText className="w-4 h-4 text-gray-400" />
                  )}
                </div>
                
                <div className="p-4">
                  {/* Header */}
                  <div className="mb-3">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-lg">{getIndustryIcon(project.industry)}</span>
                      <h3 className="font-semibold text-gray-900 group-hover:text-green-600 transition-colors line-clamp-1 text-sm">
                        {project.name.split(' ').map(word => 
                          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                        ).join(' ')}
                      </h3>
                      <span className={`ml-auto inline-flex items-center px-2 py-0.5 rounded text-[10px] font-medium border ${getStatusBadgeClass(project.status)}`}>
                        {getStatusLabel(project.status)}
                      </span>
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-1 mb-3">
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium text-gray-600 bg-white/60">
                      {project.industry.charAt(0).toUpperCase() + project.industry.slice(1)}
                    </span>
                    {project.product_type && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium text-gray-600 bg-white/60">
                        {project.product_type.split('-').map(word => 
                          word.charAt(0).toUpperCase() + word.slice(1)
                        ).join(' ')}
                      </span>
                    )}
                  </div>

                  {/* Footer */}
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {formatTimeAgo(project.updated_at || project.created_at)}
                    </span>
                    <div className="flex items-center gap-1">
                      {project.status === 'failed' && (
                        <button
                          onClick={(e) => handleRetryProject(project, e)}
                          className="opacity-0 group-hover:opacity-100 p-1 text-blue-500 hover:bg-blue-50 rounded transition-all"
                          title="Retry project"
                        >
                          <RefreshCw className="w-3 h-3" />
                        </button>
                      )}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          showDeleteDialog(project);
                        }}
                        className="opacity-0 group-hover:opacity-100 p-1 text-red-500 hover:bg-red-50 rounded transition-all"
                        title="Delete project"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-2">
            {filteredProjects.map((project) => (
              <div
                key={project._id}
                className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all hover:border-green-400 group flex items-center justify-between ${
                  project.industry === 'beverages' ? 'border-blue-300 bg-blue-50 hover:bg-blue-100' :
                  project.industry === 'nutraceuticals' ? 'border-green-300 bg-green-50 hover:bg-green-100' :
                  project.industry === 'cosmetics' ? 'border-purple-300 bg-purple-50 hover:bg-purple-100' :
                  'border-gray-300 bg-gray-50 hover:bg-gray-100'
                }`}
                onClick={() => handleOpenProject(project)}
              >
                {/* Status Icon */}
                <div className="absolute top-2 right-2">
                  {project.status === 'completed' ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : project.status === 'in_progress' ? (
                    <Clock className="w-4 h-4 text-blue-600" />
                  ) : project.status === 'queued' ? (
                    <Clock className="w-4 h-4 text-blue-600" />
                  ) : project.status === 'failed' ? (
                    <AlertTriangle className="w-4 h-4 text-red-600" />
                  ) : (
                    <FileText className="w-4 h-4 text-gray-400" />
                  )}
                </div>

                {/* Left Content */}
                <div className="flex items-center space-x-4 flex-1">
                  <span className="text-xl">{getIndustryIcon(project.industry)}</span>
                  <div>
                    <h3 className="font-semibold text-sm text-gray-900 group-hover:text-green-600 transition-colors">
                      {project.name.split(' ').map(word => 
                        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                      ).join(' ')}
                    </h3>
                    <div className="flex gap-2 mt-1">
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium text-gray-600 bg-white/60">
                        {project.industry.charAt(0).toUpperCase() + project.industry.slice(1)}
                      </span>
                      {project.product_type && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium text-gray-600 bg-white/60">
                          {project.product_type.split('-').map(word => 
                            word.charAt(0).toUpperCase() + word.slice(1)
                          ).join(' ')}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Right Content */}
                <div className="flex items-center space-x-4">
                  <span className="text-xs text-gray-500">
                    {formatTimeAgo(project.updated_at || project.created_at)}
                  </span>
                  <div className="flex items-center gap-1">
                    {project.status === 'failed' && (
                      <button
                        onClick={(e) => handleRetryProject(project, e)}
                        className="opacity-0 group-hover:opacity-100 p-1 text-blue-500 hover:bg-blue-50 rounded transition-all"
                        title="Retry project"
                      >
                        <RefreshCw className="w-3 h-3" />
                      </button>
                    )}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        showDeleteDialog(project);
                      }}
                      className="opacity-0 group-hover:opacity-100 p-1 text-red-500 hover:bg-red-50 rounded transition-all"
                      title="Delete project"
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Formulation Wizard Modal */}
      <FormulationWizardModal
        isOpen={showWizardModal}
        onComplete={handleWizardComplete}
        onClose={() => setShowWizardModal(false)}
      />

      {/* Delete Confirmation Dialog */}
      {deleteConfirm.show && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={() => setDeleteConfirm({ show: false, project: null })}>
          <div className="bg-white rounded-lg border-2 border-gray-200 p-6 max-w-sm mx-4 shadow-xl" onClick={(e) => e.stopPropagation()}>
            <div className="flex items-center mb-4">
              <div className="p-2 bg-red-100 rounded-lg mr-3">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Delete Project</h3>
              <button
                onClick={() => setDeleteConfirm({ show: false, project: null })}
                className="ml-auto p-1 text-gray-400 hover:text-gray-600 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <p className="text-sm text-gray-600 mb-6">
              Are you sure you want to delete "<strong>{deleteConfirm.project?.name.split(' ').map(word => 
                word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
              ).join(' ')}</strong>"? This action cannot be undone.
            </p>
            
            <div className="flex gap-3">
              <button
                onClick={() => setDeleteConfirm({ show: false, project: null })}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDeleteProject(deleteConfirm.project._id)}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Retry Confirmation Dialog */}
      {retryConfirm.show && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={() => setRetryConfirm({ show: false, project: null })}>
          <div className="bg-white rounded-lg border-2 border-gray-200 p-6 max-w-sm mx-4 shadow-xl" onClick={(e) => e.stopPropagation()}>
            <div className="flex items-center mb-4">
              <div className="p-2 bg-blue-100 rounded-lg mr-3">
                <RefreshCw className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Retry Project</h3>
              <button
                onClick={() => setRetryConfirm({ show: false, project: null })}
                className="ml-auto p-1 text-gray-400 hover:text-gray-600 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <p className="text-sm text-gray-600 mb-6">
              Are you sure you want to retry generating the formulation for "<strong>{retryConfirm.project?.name.split(' ').map(word => 
                word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
              ).join(' ')}</strong>"? This will requeue the project for processing.
            </p>
            
            <div className="flex gap-3">
              <button
                onClick={() => setRetryConfirm({ show: false, project: null })}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={confirmRetryProject}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Projects;
