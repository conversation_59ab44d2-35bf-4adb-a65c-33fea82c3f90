import React, { useEffect, useRef, useState } from 'react';
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import OptionCard from '../components/OptionCard';
import PageHeader from '../components/PageHeader';
import useWizardHotkeys from '../hooks/useWizardHotkeys';

const SubCategorySelection = ({ embedded = false, onSelectSubcategory, onContinueCustom }) => {
  const { formData, updateFormData } = useApp();
  const { apiRequest } = useAuth();
  const navigate = useNavigate();

  const [subCategories, setSubCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selected, setSelected] = useState('');
  const [useCustom, setUseCustom] = useState(false);
  const [customDescription, setCustomDescription] = useState('');

  const itemRefs = useRef([]);
  const [focusIndex, setFocusIndex] = useState(0);

  useEffect(() => {
    if (!formData.productCategory) {
      navigate('/category-selector');
      return;
    }
    fetchSubCategories(formData.productCategory);
  }, [formData.productCategory]);

  // ESC returns to Category page
  useWizardHotkeys({ onEsc: () => navigate('/category-selector'), enabled: !embedded, isDirty: useCustom && !!customDescription.trim() });

  const fetchSubCategories = async (categorySlug) => {
    try {
      setLoading(true);
      setError(null);
      const categoryResponse = await apiRequest(`/taxonomies/${categorySlug}`);
      const categoryData = categoryResponse.data;
      const resp = await apiRequest(`/taxonomies?level=sub_category&parent_id=${categoryData?._id}`);
      const data = resp.data || [];
      const transformed = data.map(item => ({
        id: item.slug,
        name: item.name,
        description: item.description || '',
        parentId: item.parent_id
      }));
      // add custom
      transformed.push({ id: 'custom', name: 'Other', description: 'Specify your product type', isCustom: true });
      setSubCategories(transformed);
    } catch (err) {
      console.error('Error fetching subcategories:', err);
      setError('Failed to load product types.');
      setSubCategories([{ id: 'custom', name: 'Other', description: 'Specify your product type', isCustom: true }]);
    } finally {
      setLoading(false);
    }
  };

  const handleSelect = (id) => {
    setSelected(id);
    const isCustom = id === 'custom';
    setUseCustom(isCustom);
    if (!isCustom) {
      updateFormData('productSubCategory', id);
      updateFormData('productType', id);
      updateFormData('productDescription', '');
      if (onSelectSubcategory) {
        onSelectSubcategory(id);
      } else {
        navigate('/goal-setup');
      }
    }
  };

  const handleKeyDown = (e) => {
    if (!['ArrowRight', 'ArrowLeft', 'ArrowDown', 'ArrowUp'].includes(e.key)) return;
    e.preventDefault();
    const max = subCategories.length - 1;
    let next = focusIndex;
    if (e.key === 'ArrowRight' || e.key === 'ArrowDown') next = Math.min(max, focusIndex + 1);
    if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') next = Math.max(0, focusIndex - 1);
    setFocusIndex(next);
    const el = itemRefs.current[next];
    if (el) el.focus();
  };

  const handleContinue = () => {
    if (useCustom && customDescription.trim()) {
      updateFormData('productSubCategory', 'custom');
      updateFormData('productType', 'custom');
      updateFormData('productDescription', customDescription);
      if (onContinueCustom) {
        onContinueCustom({ description: customDescription });
      } else {
        navigate('/goal-setup');
      }
    }
  };

  return (
    <div className="min-h-screen bg-slate-50 px-6 py-3">
      {!embedded && (
        <div className="mb-3 max-w-6xl mx-auto">
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-lg font-semibold text-slate-900">Select Specific Product Type</h1>
              <p className="text-xs text-slate-500 mt-0.5">Choose the exact product you want to formulate</p>
            </div>
            {formData.industry && (
              <span className="px-2.5 py-1 rounded-full bg-emerald-100 text-emerald-800 text-xs font-medium capitalize">
                {formData.industry.replace(/-/g, ' ')}
              </span>
            )}
          </div>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
        </div>
      ) : error ? (
        <div className="text-center p-8">
          <p className="text-red-600 mb-4">{error}</p>
          <button onClick={() => fetchSubCategories(formData.productCategory)} className="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700">Retry</button>
        </div>
      ) : (
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6" onKeyDown={handleKeyDown}>
            {subCategories.map((item, idx) => (
              <OptionCard
                key={item.id}
                title={item.name}
                description={item.description}
                selected={selected === item.id}
                variant="subcategory"
                badge={item.isCustom ? 'Custom' : undefined}
                onClick={() => handleSelect(item.id)}
                ref={el => (itemRefs.current[idx] = el)}
              />
            ))}
          </div>

          {useCustom && (
            <div className="p-4 md:p-5 bg-white rounded-xl shadow-sm border border-slate-200 mb-6">
              <label className="block text-sm md:text-base font-semibold text-slate-900 mb-2">Describe Your Product</label>
              <textarea
                value={customDescription}
                onChange={(e) => setCustomDescription(e.target.value)}
                placeholder="E.g., Eye cream with peptides and hyaluronic acid"
                className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400"
                rows={3}
              />
              <div className="flex justify-end mt-2">
                <button
                  onClick={handleContinue}
                  disabled={!customDescription.trim()}
                  className="px-5 md:px-6 py-2.5 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-slate-300 disabled:cursor-not-allowed flex items-center shadow-sm"
                >
                  Continue to Goals <ChevronRight className="ml-2 w-4 h-4" />
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SubCategorySelection;
