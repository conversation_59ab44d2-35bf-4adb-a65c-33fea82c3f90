import React, { useEffect, useState } from 'react';
import { Search, Edit3, Trash2, Plus, Save, X, Package, Settings } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import FormSection from '../components/FormSection';
import Modal from '../components/Modal';

const FormulationBlocks = () => {
  const { apiRequest } = useAuth();
  const [loading, setLoading] = useState(false);
  const [blocks, setBlocks] = useState([]);
  const [error, setError] = useState(null);
  const [query, setQuery] = useState('');
  const [modal, setModal] = useState(null);
  const [editingBlock, setEditingBlock] = useState(null);

  const loadBlocks = async () => {
    setLoading(true);
    setError(null);
    try {
      const resp = await apiRequest('/formulation-blocks');
      setBlocks(resp.data || []);
    } catch (e) {
      setError(e.message || 'Failed to load blocks');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadBlocks();
  }, []);

  const handleCreate = async (payload) => {
    try {
      const resp = await apiRequest('/formulation-blocks', {
        method: 'POST',
        body: JSON.stringify(payload)
      });
      setBlocks(prev => [...prev, resp.data]);
    } catch (e) {
      throw new Error(e.message || 'Failed to create block');
    }
  };

  const handleUpdate = async (id, payload) => {
    try {
      const resp = await apiRequest(`/formulation-blocks/${id}`, {
        method: 'PUT',
        body: JSON.stringify(payload)
      });
      setBlocks(prev => prev.map(c => c._id === id ? resp.data : c));
      setEditingBlock(null);
    } catch (e) {
      throw new Error(e.message || 'Failed to update block');
    }
  };

  const handleDelete = async (id) => {
    try {
      await apiRequest(`/formulation-blocks/${id}`, { method: 'DELETE' });
      setBlocks(prev => prev.filter(c => c._id !== id));
    } catch (e) {
      throw new Error(e.message || 'Failed to delete block');
    }
  };

  const filteredBlocks = blocks.filter(block => {
    if (!query) return true;
    const searchTerm = query.toLowerCase();
    return (
      block.name.toLowerCase().includes(searchTerm) ||
      block.key.toLowerCase().includes(searchTerm) ||
      block.description.toLowerCase().includes(searchTerm) ||
      (block.category || '').toLowerCase().includes(searchTerm)
    );
  });

  return (
    <div className="min-h-screen bg-slate-50 px-6 py-4">
      <div className="max-w-6xl mx-auto space-y-4">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-lg font-semibold tracking-tight text-black">
              Formulation Blocks
            </h1>
            <p className="text-sm text-slate-500 mt-0.5">
              Manage dynamic formulation schema blocks and their bindings.
            </p>
          </div>
          <button
            onClick={() => setModal({ type: 'create' })}
            className="px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            New Block
          </button>
        </div>

        {/* Search and Filters */}
        <FormSection title={null} className="shadow-md ring-1 ring-slate-100">
          <div className="flex items-center gap-4 p-4">
            <div className="flex-1 relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
              <input
                type="text"
                placeholder="Search blocks by name, key, description, or category..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              />
            </div>
            <div className="text-sm text-slate-500">
              {filteredBlocks.length} of {blocks.length} blocks
            </div>
          </div>
        </FormSection>

        {/* Blocks List */}
        <FormSection title={null} className="shadow-md ring-1 ring-slate-100">
          {loading ? (
            <div className="p-8 text-center">
              <div className="text-slate-500">Loading blocks...</div>
            </div>
          ) : error ? (
            <div className="p-8 text-center">
              <div className="text-red-600">{error}</div>
              <button
                onClick={loadBlocks}
                className="mt-2 px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700"
              >
                Retry
              </button>
            </div>
          ) : filteredBlocks.length === 0 ? (
            <div className="p-8 text-center">
              <Package className="w-12 h-12 text-slate-300 mx-auto mb-4" />
              <div className="text-slate-500">
                {query ? 'No blocks match your search.' : 'No blocks found.'}
              </div>
              {!query && (
                <button
                  onClick={() => setModal({ type: 'create' })}
                  className="mt-4 px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700"
                >
                  Create your first block
                </button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-slate-200 text-left">
                    <th className="px-4 py-3 text-sm font-medium text-slate-600">Block</th>
                    <th className="px-4 py-3 text-sm font-medium text-slate-600">Key</th>
                    <th className="px-4 py-3 text-sm font-medium text-slate-600">Category</th>
                    <th className="px-4 py-3 text-sm font-medium text-slate-600">Schema Path</th>
                    <th className="px-4 py-3 text-sm font-medium text-slate-600">Status</th>
                    <th className="px-4 py-3 text-sm font-medium text-slate-600 w-24">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredBlocks.map((block) => (
                    <BlockRow
                      key={block._id}
                      block={block}
                      isEditing={editingBlock?._id === block._id}
                      onEdit={() => setEditingBlock(block)}
                      onSave={(payload) => handleUpdate(block._id, payload)}
                      onCancel={() => setEditingBlock(null)}
                      onDelete={() => setModal({ type: 'delete', block: block })}
                    />
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </FormSection>

        {/* Modals */}
        {modal && (
          <BlockModals
            modal={modal}
            setModal={setModal}
            onCreate={handleCreate}
            onDelete={handleDelete}
          />
        )}
      </div>
    </div>
  );
};

const BlockRow = ({ block, isEditing, onEdit, onSave, onCancel, onDelete }) => {
  const [formData, setFormData] = useState({
    name: block.name,
    key: block.key,
    description: block.description,
    category: block.category || '',
    schema_path: block.schema_path,
    rules: block.rules || '',
    is_required: block.is_required || false,
    display_order: block.display_order || 0,
    is_active: block.is_active !== false
  });
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    try {
      setSaving(true);
      await onSave(formData);
    } catch (e) {
      alert(e.message);
    } finally {
      setSaving(false);
    }
  };

  if (isEditing) {
    return (
      <tr className="border-b border-slate-200 bg-slate-50">
        <td className="px-4 py-3">
          <div className="space-y-2">
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-2 py-1 text-sm border border-slate-200 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500"
              placeholder="Component name"
            />
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-2 py-1 text-xs border border-slate-200 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500"
              placeholder="Description"
              rows={2}
            />
          </div>
        </td>
        <td className="px-4 py-3">
          <input
            type="text"
            value={formData.key}
            onChange={(e) => setFormData(prev => ({ ...prev, key: e.target.value }))}
            className="w-full px-2 py-1 text-sm border border-slate-200 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500"
            placeholder="component_key"
          />
        </td>
        <td className="px-4 py-3">
          <input
            type="text"
            value={formData.category}
            onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
            className="w-full px-2 py-1 text-sm border border-slate-200 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500"
            placeholder="Category"
          />
        </td>
        <td className="px-4 py-3">
          <input
            type="text"
            value={formData.schema_path}
            onChange={(e) => setFormData(prev => ({ ...prev, schema_path: e.target.value }))}
            className="w-full px-2 py-1 text-sm border border-slate-200 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500"
            placeholder="path.to.property"
          />
        </td>
        <td className="px-4 py-3">
          <div className="space-y-1">
            <label className="flex items-center gap-2 text-xs">
              <input
                type="checkbox"
                checked={formData.is_active}
                onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                className="rounded"
              />
              Active
            </label>
            <label className="flex items-center gap-2 text-xs">
              <input
                type="checkbox"
                checked={formData.is_required}
                onChange={(e) => setFormData(prev => ({ ...prev, is_required: e.target.checked }))}
                className="rounded"
              />
              Required
            </label>
          </div>
        </td>
        <td className="px-4 py-3">
          <div className="flex items-center gap-1">
            <button
              onClick={handleSave}
              disabled={saving}
              className="p-1 text-emerald-600 hover:bg-emerald-50 rounded"
              title="Save"
            >
              <Save className="w-4 h-4" />
            </button>
            <button
              onClick={onCancel}
              disabled={saving}
              className="p-1 text-slate-400 hover:bg-slate-50 rounded"
              title="Cancel"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </td>
      </tr>
    );
  }

  return (
    <tr className="border-b border-slate-200 hover:bg-slate-50">
      <td className="px-4 py-3">
        <div>
          <div className="font-medium text-sm text-slate-900">{block.name}</div>
          <div className="text-xs text-slate-500 mt-1">{block.description}</div>
        </div>
      </td>
      <td className="px-4 py-3">
        <code className="text-xs bg-slate-100 px-2 py-1 rounded">{block.key}</code>
      </td>
      <td className="px-4 py-3">
        <span className="text-sm text-slate-600">{block.category || '—'}</span>
      </td>
      <td className="px-4 py-3">
        <code className="text-xs text-slate-600">{block.schema_path}</code>
      </td>
      <td className="px-4 py-3">
        <div className="flex items-center gap-2">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            block.is_active !== false
              ? 'bg-emerald-100 text-emerald-800'
              : 'bg-slate-100 text-slate-800'
          }`}>
            {block.is_active !== false ? 'Active' : 'Inactive'}
          </span>
          {block.is_required && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Required
            </span>
          )}
        </div>
      </td>
      <td className="px-4 py-3">
        <div className="flex items-center gap-1">
          <button
            onClick={onEdit}
            className="p-1 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded"
            title="Edit"
          >
            <Edit3 className="w-4 h-4" />
          </button>
          <button
            onClick={onDelete}
            className="p-1 text-slate-400 hover:text-red-600 hover:bg-red-50 rounded"
            title="Delete"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </td>
    </tr>
  );
};

const BlockModals = ({ modal, setModal, onCreate, onDelete }) => {
  if (!modal) return null;

  if (modal.type === 'create') {
    return (
      <CreateBlockModal
        onClose={() => setModal(null)}
        onCreate={onCreate}
      />
    );
  }

  if (modal.type === 'delete') {
    return (
      <DeleteBlockModal
        block={modal.block}
        onClose={() => setModal(null)}
        onDelete={onDelete}
      />
    );
  }

  return null;
};

const CreateBlockModal = ({ onClose, onCreate }) => {
  const [formData, setFormData] = useState({
    name: '',
    key: '',
    description: '',
    category: '',
    schema_path: '',
    rules: '',
    is_required: false,
    display_order: 0,
    is_active: true
  });
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!formData.name || !formData.key || !formData.description || !formData.schema_path) {
      setError('Name, key, description, and schema path are required.');
      return;
    }

    try {
      setCreating(true);
      setError(null);
      await onCreate(formData);
    } catch (e) {
      setError(e.message);
    } finally {
      setCreating(false);
    }
  };

  return (
    <Modal onClose={onClose} title="Create Formulation Block">
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md text-red-700 text-sm">
            {error}
          </div>
        )}
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              placeholder="e.g., Nutritional Profile"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Key *
            </label>
            <input
              type="text"
              value={formData.key}
              onChange={(e) => setFormData(prev => ({ ...prev, key: e.target.value }))}
              className="w-full px-3 py-2 border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              placeholder="e.g., nutritional_profile"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-1">
            Description *
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            className="w-full px-3 py-2 border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
            placeholder="Describe what this block represents in the formulation"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Category
            </label>
            <input
              type="text"
              value={formData.category}
              onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              className="w-full px-3 py-2 border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              placeholder="e.g., Analysis, Production, Compliance"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Schema Path *
            </label>
            <input
              type="text"
              value={formData.schema_path}
              onChange={(e) => setFormData(prev => ({ ...prev, schema_path: e.target.value }))}
              className="w-full px-3 py-2 border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              placeholder="e.g., nutritional_profile"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-1">
            Rules (for LLM)
          </label>
          <textarea
            value={formData.rules}
            onChange={(e) => setFormData(prev => ({ ...prev, rules: e.target.value }))}
            className="w-full px-3 py-2 border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
            placeholder="Special instructions or rules for LLM when processing this block"
            rows={2}
          />
        </div>

        <div className="grid grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Display Order
            </label>
            <input
              type="number"
              value={formData.display_order}
              onChange={(e) => setFormData(prev => ({ ...prev, display_order: parseInt(e.target.value) || 0 }))}
              className="w-full px-3 py-2 border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              min="0"
            />
          </div>
          <div className="flex items-center">
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={formData.is_required}
                onChange={(e) => setFormData(prev => ({ ...prev, is_required: e.target.checked }))}
                className="rounded"
              />
              Required by default
            </label>
          </div>
          <div className="flex items-center">
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={formData.is_active}
                onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                className="rounded"
              />
              Active
            </label>
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-4 border-t">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-slate-600 border border-slate-200 rounded-md hover:bg-slate-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={creating}
            className="px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 disabled:opacity-50"
          >
            {creating ? 'Creating...' : 'Create Block'}
          </button>
        </div>
      </form>
    </Modal>
  );
};

const DeleteBlockModal = ({ block, onClose, onDelete }) => {
  const [deleting, setDeleting] = useState(false);
  const [error, setError] = useState(null);

  const handleDelete = async () => {
    try {
      setDeleting(true);
      setError(null);
      await onDelete(block._id);
      onClose();
    } catch (e) {
      setError(e.message);
    } finally {
      setDeleting(false);
    }
  };

  return (
    <Modal onClose={onClose} title="Delete Block">
      <div className="space-y-4">
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md text-red-700 text-sm">
            {error}
          </div>
        )}
        
        <div className="text-sm text-slate-600">
          Are you sure you want to delete the block <strong>{block.name}</strong>?
          This action cannot be undone.
        </div>
        
        <div className="bg-slate-50 p-3 rounded-md">
          <div className="text-xs text-slate-500 mb-1">Block Details:</div>
          <div className="text-sm">
            <div><strong>Key:</strong> {block.key}</div>
            <div><strong>Schema Path:</strong> {block.schema_path}</div>
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-4 border-t">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-slate-600 border border-slate-200 rounded-md hover:bg-slate-50"
          >
            Cancel
          </button>
          <button
            onClick={handleDelete}
            disabled={deleting}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            {deleting ? 'Deleting...' : 'Delete Block'}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default FormulationBlocks;