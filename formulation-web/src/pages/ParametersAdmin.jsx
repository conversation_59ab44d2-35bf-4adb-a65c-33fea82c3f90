import React, { useEffect, useState } from 'react';
import { useAuth } from '../context/AuthContext';
import FormSection from '../components/FormSection';
import Modal from '../components/Modal';
import { Search, Edit3, Trash2, Plus } from 'lucide-react';

const pageSlice = (items, page, per) => items.slice((page-1)*per, (page)*per);

const ParametersAdmin = () => {
  const { apiRequest } = useAuth();
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [q, setQ] = useState('');
  const [page, setPage] = useState(1);
  const [per, setPer] = useState(10);
  const [modal, setModal] = useState(null); // {type, param}
  const [view, setView] = useState('parameters'); // 'parameters' | 'answers'
  // Answer sets state
  const [answers, setAnswers] = useState([]);
  const [aLoading, setALoading] = useState(false);
  const [aError, setAError] = useState(null);
  const [aQ, setAQ] = useState('');
  const [aPage, setAPage] = useState(1);
  const [aPer, setAPer] = useState(10);
  const [aModal, setAModal] = useState(null); // {type, item, payload}

  const load = async () => {
    try {
      setLoading(true);
      setError(null);
      const url = q ? `/admin/parameters?q=${encodeURIComponent(q)}` : '/admin/parameters';
      const resp = await apiRequest(url);
      setItems(resp.data || []);
      setPage(1);
    } catch (e) {
      setError(e.message || 'Failed to load');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => { load(); }, []);

  const loadAnswers = async () => {
    try {
      setALoading(true);
      setAError(null);
      const url = aQ ? `/admin/answers?q=${encodeURIComponent(aQ)}` : '/admin/answers';
      const resp = await apiRequest(url);
      setAnswers(resp.data || []);
      setAPage(1);
    } catch (e) {
      setAError(e.message || 'Failed to load');
    } finally {
      setALoading(false);
    }
  };
  useEffect(() => { loadAnswers(); }, []);

  const create = async (payload) => {
    const resp = await apiRequest('/admin/parameters', { method:'POST', body: JSON.stringify(payload) });
    setItems(prev => [resp.data, ...prev]);
  };

  const save = async () => {
    if (!modal) return;
    if (modal.type === 'create') {
      await create(modal.payload);
    }
    if (modal.type === 'edit') {
      const id = modal.param._id;
      await apiRequest(`/admin/parameters/${id}`, { method:'PUT', body: JSON.stringify(modal.payload) });
      await load();
    }
    if (modal.type === 'delete') {
      const id = modal.param._id;
      await apiRequest(`/admin/parameters/${id}`, { method:'DELETE' });
      await load();
    }
    setModal(null);
  };

  const saveAnswers = async () => {
    if (!aModal) return;
    if (aModal.type === 'create') {
      const payload = aModal.payload;
      await apiRequest('/admin/answers', { method:'POST', body: JSON.stringify({ key: payload.key, name: payload.name, options: parseOptions(payload.options) }) });
      await loadAnswers();
    }
    if (aModal.type === 'edit') {
      const payload = aModal.payload;
      await apiRequest(`/admin/answers/${aModal.item._id}`, { method:'PUT', body: JSON.stringify({ key: payload.key, name: payload.name, options: parseOptions(payload.options) }) });
      await loadAnswers();
    }
    if (aModal.type === 'delete') {
      await apiRequest(`/admin/answers/${aModal.item._id}`, { method:'DELETE' });
      await loadAnswers();
    }
    setAModal(null);
  };

  const paged = pageSlice(items, page, per);
  const totalPages = Math.max(1, Math.ceil(items.length / per));

  return (
    <div className="min-h-screen bg-slate-50 px-6 py-8">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-slate-900">Parameters</h1>
          <div className="flex items-center gap-2">
            <button className={`px-3 py-2 rounded-md border ${view==='parameters' ? 'bg-emerald-600 text-white border-emerald-600' : 'bg-white text-slate-700 border-slate-300'}`} onClick={()=>setView('parameters')}>Parameters</button>
            <button className={`px-3 py-2 rounded-md border ${view==='answers' ? 'bg-emerald-600 text-white border-emerald-600' : 'bg-white text-slate-700 border-slate-300'}`} onClick={()=>setView('answers')}>Answer Sets</button>
          </div>
        </div>

        {view==='parameters' && (
        <FormSection title={null} meta={null}>
          <div className="flex items-center justify-between mb-3">
            <div className="text-sm font-semibold text-slate-900">Parameters ({items.length})</div>
            <div className="flex items-center gap-2">
              <div className="relative w-64">
                <Search className="w-4 h-4 text-slate-400 absolute left-3 top-1/2 -translate-y-1/2" />
                <input value={q} onChange={e=>setQ(e.target.value)} onKeyUp={(e)=>{ if (e.key==='Enter') load(); }} placeholder="Search..." className="w-full h-10 pl-9 pr-3 border rounded-md border-slate-200 focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500" />
              </div>
              <button className="inline-flex items-center gap-1 px-3 py-2 rounded-md bg-emerald-600 text-white" onClick={()=>setModal({ type:'create', payload:{ key:'', name:'', type:'string', answers_key:'' } })}><Plus className="w-4 h-4" />New</button>
            </div>
          </div>
          {loading ? <div className="text-slate-500">Loading...</div> : error ? <div className="text-red-600">{error}</div> : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="text-left text-slate-600 border-b">
                      <th className="py-2 pr-4">Key</th>
                      <th className="py-2 pr-4">Name</th>
                      <th className="py-2 pr-4">Type</th>
                      <th className="py-2 pr-4">Answers</th>
                      <th className="py-2 pr-4 w-32">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paged.map(p => (
                      <tr key={p._id} className="border-b last:border-0">
                        <td className="py-2 pr-4">{p.key}</td>
                        <td className="py-2 pr-4">{p.name}</td>
                        <td className="py-2 pr-4">{p.type}</td>
                        <td className="py-2 pr-4">{p.answers_key || '-'}</td>
                        <td className="py-2 pr-4">
                          <div className="flex gap-1.5">
                            <button className="p-1.5 rounded-md hover:bg-slate-100 text-slate-700" title="Edit" aria-label="Edit" onClick={()=>setModal({ type:'edit', param:p, payload:{ key:p.key, name:p.name, type:p.type, answers_key:p.answers_key||'' } })}><Edit3 className="w-4 h-4" /></button>
                            <button className="p-1.5 rounded-md hover:bg-slate-100 text-red-600" title="Delete" aria-label="Delete" onClick={()=>setModal({ type:'delete', param:p })}><Trash2 className="w-4 h-4" /></button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              {items.length > 0 && (
              <div className="flex items-center justify-between mt-3">
                <div className="text-sm text-slate-600">Page {page} of {totalPages}</div>
                <div className="flex items-center gap-2">
                  <button disabled={page<=1} onClick={()=>setPage(p=>Math.max(1,p-1))} className="px-3 py-1.5 rounded-md border border-slate-300 disabled:opacity-50">Prev</button>
                  <button disabled={page>=totalPages} onClick={()=>setPage(p=>Math.min(totalPages,p+1))} className="px-3 py-1.5 rounded-md border border-slate-300 disabled:opacity-50">Next</button>
                  <select value={per} onChange={e=>{ setPer(Number(e.target.value)||10); setPage(1); }} className="h-9 px-2 border rounded-md border-slate-300">
                    <option>10</option>
                    <option>20</option>
                    <option>50</option>
                  </select>
                </div>
              </div>
              )}
            </>
          )}
        </FormSection>
        )}

        {view==='answers' && (
        <FormSection title={null} meta={null}>
          <div className="flex items-center justify-between mb-3">
            <div className="text-sm font-semibold text-slate-900">Answer Sets ({answers.length})</div>
            <div className="flex items-center gap-2">
              <div className="relative w-64">
                <Search className="w-4 h-4 text-slate-400 absolute left-3 top-1/2 -translate-y-1/2" />
                <input value={aQ} onChange={e=>setAQ(e.target.value)} onKeyUp={(e)=>{ if (e.key==='Enter') loadAnswers(); }} placeholder="Search..." className="w-full h-10 pl-9 pr-3 border rounded-md border-slate-200 focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500" />
              </div>
              <button className="inline-flex items-center gap-1 px-3 py-2 rounded-md bg-emerald-600 text-white" onClick={()=>setAModal({ type:'create', payload:{ key:'', name:'', options:'' } })}><Plus className="w-4 h-4" />New</button>
            </div>
          </div>
          {aLoading ? <div className="text-slate-500">Loading...</div> : aError ? <div className="text-red-600">{aError}</div> : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="text-left text-slate-600 border-b">
                      <th className="py-2 pr-4">Key</th>
                      <th className="py-2 pr-4">Name</th>
                      <th className="py-2 pr-4">Options</th>
                      <th className="py-2 pr-4 w-32">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {pageSlice(answers, aPage, aPer).map(a => (
                      <tr key={a._id} className="border-b last:border-0">
                        <td className="py-2 pr-4">{a.key}</td>
                        <td className="py-2 pr-4">{a.name}</td>
                        <td className="py-2 pr-4">{a.options?.length || 0}</td>
                        <td className="py-2 pr-4">
                          <div className="flex gap-1.5">
                            <button className="p-1.5 rounded-md hover:bg-slate-100 text-slate-700" title="Edit" aria-label="Edit" onClick={()=>setAModal({ type:'edit', item:a, payload:{ key:a.key, name:a.name, options: optionsToString(a.options) } })}><Edit3 className="w-4 h-4" /></button>
                            <button className="p-1.5 rounded-md hover:bg-slate-100 text-red-600" title="Delete" aria-label="Delete" onClick={()=>setAModal({ type:'delete', item:a })}><Trash2 className="w-4 h-4" /></button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              {answers.length > 0 && (
              <div className="flex items-center justify-between mt-3">
                <div className="text-sm text-slate-600">Page {aPage} of {Math.max(1, Math.ceil(answers.length / aPer))}</div>
                <div className="flex items-center gap-2">
                  <button disabled={aPage<=1} onClick={()=>setAPage(p=>Math.max(1,p-1))} className="px-3 py-1.5 rounded-md border border-slate-300 disabled:opacity-50">Prev</button>
                  <button disabled={aPage>=Math.max(1, Math.ceil(answers.length / aPer))} onClick={()=>setAPage(p=>Math.min(Math.max(1, Math.ceil(answers.length / aPer)),p+1))} className="px-3 py-1.5 rounded-md border border-slate-300 disabled:opacity-50">Next</button>
                  <select value={aPer} onChange={e=>{ setAPer(Number(e.target.value)||10); setAPage(1); }} className="h-9 px-2 border rounded-md border-slate-300">
                    <option>10</option>
                    <option>20</option>
                    <option>50</option>
                  </select>
                </div>
              </div>
              )}
            </>
          )}
        </FormSection>
        )}

        <Modal
          open={!!modal}
          title={modal?.type === 'create' ? 'Create Parameter' : modal?.type === 'edit' ? 'Edit Parameter' : modal?.type === 'delete' ? 'Delete Parameter' : ''}
          onClose={()=>setModal(null)}
          onSubmit={save}
          submitLabel={modal?.type === 'delete' ? 'Delete' : 'Save'}
          destructive={modal?.type === 'delete'}
        >
          {modal?.type === 'delete' ? (
            <div className="text-sm text-slate-700">Delete <span className="font-semibold">{modal?.param?.name}</span>?</div>
          ) : (
            <div className="grid grid-cols-2 gap-3">
              <input placeholder="key" className="h-10 px-3 border rounded-md border-slate-200" value={modal?.payload?.key||''} onChange={e=>setModal({...modal, payload:{ ...(modal?.payload||{}), key:e.target.value }})} />
              <input placeholder="name" className="h-10 px-3 border rounded-md border-slate-200" value={modal?.payload?.name||''} onChange={e=>setModal({...modal, payload:{ ...(modal?.payload||{}), name:e.target.value }})} />
              <select className="h-10 px-3 border rounded-md border-slate-200" value={modal?.payload?.type||'string'} onChange={e=>setModal({...modal, payload:{ ...(modal?.payload||{}), type:e.target.value }})}>
                <option>string</option>
                <option>number</option>
                <option>enum</option>
                <option>boolean</option>
                <option>array</option>
                <option>table</option>
              </select>
              <input placeholder="answers_key (optional)" className="h-10 px-3 border rounded-md border-slate-200" value={modal?.payload?.answers_key||''} onChange={e=>setModal({...modal, payload:{ ...(modal?.payload||{}), answers_key:e.target.value }})} />
            </div>
          )}
        </Modal>

        {/* Answer Sets modal */}
        <Modal
          open={!!aModal}
          title={aModal?.type === 'create' ? 'Create Answer Set' : aModal?.type === 'edit' ? 'Edit Answer Set' : aModal?.type === 'delete' ? 'Delete Answer Set' : ''}
          onClose={()=>setAModal(null)}
          onSubmit={saveAnswers}
          submitLabel={aModal?.type === 'delete' ? 'Delete' : 'Save'}
          destructive={aModal?.type === 'delete'}
        >
          {aModal?.type === 'delete' ? (
            <div className="text-sm text-slate-700">Delete <span className="font-semibold">{aModal?.item?.name}</span>?</div>
          ) : (
            <div className="space-y-3">
              <input placeholder="key" className="w-full h-10 px-3 border rounded-md border-slate-200" value={aModal?.payload?.key||''} onChange={e=>setAModal({...aModal, payload:{ ...(aModal?.payload||{}), key:e.target.value }})} />
              <input placeholder="name" className="w-full h-10 px-3 border rounded-md border-slate-200" value={aModal?.payload?.name||''} onChange={e=>setAModal({...aModal, payload:{ ...(aModal?.payload||{}), name:e.target.value }})} />
              <textarea placeholder="value:label per line" rows={6} className="w-full px-3 py-2 border rounded-md border-slate-200" value={aModal?.payload?.options || ''} onChange={e=>setAModal({...aModal, payload:{ ...(aModal?.payload||{}), options:e.target.value }})} />
            </div>
          )}
        </Modal>
      </div>
    </div>
  );
};

const optionsToString = (opts=[]) => opts.map(o => `${o.value}:${o.label}`).join('\n');
const parseOptions = (text='') => text.split(/\r?\n/).map(s=>s.trim()).filter(Boolean).map(line => {
  const [value, label] = line.split(':').map(x=>x?.trim());
  return { value, label: label || value };
});

export default ParametersAdmin;
