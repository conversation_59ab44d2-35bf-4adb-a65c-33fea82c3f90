import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  ArrowLeft,
  Send,
  Sparkles,
  Package,
  FlaskConical,
  DollarSign,
  Leaf,
  Shield,
  TrendingUp,
  Users,
  AlertCircle,
  ChevronDown,
  ChevronUp,
  Download,
  Printer,
  Share2,
  MoreVertical,
  Check,
  X,
  Info,
  RefreshCw,
  Loader2,
  Factory,
  CheckCircle,
  Clock,
  AlertTriangle,
  Timer,
  Trash2,
  Archive,
  Copy,
  Plus
} from 'lucide-react';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import ContextToolbar from '../components/ContextToolbar';
import ProcessingModal from '../components/ProcessingModal';
import GenerationOverlay from '../components/GenerationOverlay';
import Breadcrumbs from '../components/Breadcrumbs';
import { getStatusConfig } from '../utils/colorUtils';

// Variation Tab Component
const VariationTab = ({ variation, isActive, onClick, index }) => {
  const getVariationColor = () => {
    const colors = ['emerald', 'blue', 'purple', 'amber'];
    return colors[index % colors.length];
  };
  
  const color = getVariationColor();
  
  return (
    <button
      onClick={onClick}
      className={`
        relative px-6 py-3 text-sm font-medium transition-all duration-200
        ${isActive 
          ? `text-${color}-600 bg-white border-b-2 border-${color}-500 shadow-sm` 
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
        }
      `}
    >
      <div className="flex items-center gap-2">
        <span className={`w-2 h-2 rounded-full bg-${color}-500 ${isActive ? 'animate-pulse' : ''}`} />
        {variation.name}
      </div>
      {isActive && (
        <div className={`absolute bottom-0 left-0 right-0 h-0.5 bg-${color}-500`} />
      )}
    </button>
  );
};

// Widget Card Component
const WidgetCard = ({ id, title, icon: Icon, children, className = '', expandable = false, loading = false }) => {
  const [isExpanded, setIsExpanded] = useState(true);
  
  return (
    <div 
      id={id ? `widget-${id}` : undefined}
      className={`bg-white rounded-lg border border-gray-200 shadow-md hover:shadow-lg transition-all duration-300 ${className}`}
    >
      {(Icon || title || loading || expandable) && (
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-3">
            {Icon && (
              <div className="p-2 bg-white rounded-lg border border-gray-200">
                <Icon className="w-4 h-4 text-gray-700" />
              </div>
            )}
            {title && <h3 className="font-semibold text-gray-900">{title}</h3>}
          </div>
          <div className="flex items-center gap-2">
            {loading && <Loader2 className="w-4 h-4 text-gray-400 animate-spin" />}
            {expandable && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
              >
                {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
              </button>
            )}
          </div>
        </div>
      )}
      {(!expandable || isExpanded) && (
        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 text-gray-400 animate-spin" />
            </div>
          ) : (
            children
          )}
        </div>
      )}
    </div>
  );
};

// Quality Score Meter Component
const QualityMeter = ({ label, value, color }) => {
  return (
    <div className="flex flex-col gap-2">
      <div className="flex justify-between items-center">
        <span className="text-sm text-gray-600">{label}</span>
        <span className="text-sm font-semibold">{value}%</span>
      </div>
      <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden border border-gray-300">
        <div 
          className={`h-full bg-${color}-500 transition-all duration-500`}
          style={{ width: `${value}%` }}
        />
      </div>
    </div>
  );
};

// Chat Message Component
const ChatMessage = ({ message, isUser }) => {
  return (
    <div className={`flex gap-3 ${isUser ? 'justify-end' : 'justify-start'}`}>
      {!isUser && (
        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center flex-shrink-0 shadow-md">
          <Sparkles className="w-4 h-4 text-white" />
        </div>
      )}
      <div className={`max-w-[80%] ${isUser ? 'order-first' : ''}`}>
        <div className={`rounded-xl px-4 py-3 ${
          isUser 
            ? 'bg-gray-900 text-white border border-gray-300 shadow-md' 
            : 'bg-white text-gray-900 border border-gray-200 shadow-md'
        }`}>
          <p className="text-sm leading-relaxed">{message.text}</p>
        </div>
        <p className="text-xs text-gray-400 mt-1 px-2">
          {new Date(message.timestamp).toLocaleTimeString()}
        </p>
      </div>
    </div>
  );
};

// Project Status Badge component
const ProjectStatusBadge = ({ status }) => {
  const config = getStatusConfig(status);
  
  // Map status to appropriate icon
  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed': return CheckCircle;
      case 'processing':
      case 'in_progress': return Clock;
      case 'failed': return AlertTriangle;
      case 'pending':
      case 'queued': return Timer;
      case 'draft': return Clock;
      case 'archived': return Package;
      default: return Clock;
    }
  };
  
  const IconComponent = getStatusIcon(status);
  
  return (
    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${config.color}`}>
      <IconComponent className="w-4 h-4 mr-1" />
      {config.text}
    </div>
  );
};



const ProductPlayground = ({ projectId: routeProjectId }) => {
  const { projectId: routeParamId } = useParams();
  const { formData, updateFormData } = useApp();
  const { user, apiRequest } = useAuth();
  const navigate = useNavigate();
  
  // State management
  const [selectedVariation, setSelectedVariation] = useState(0);
  const [chatMessages, setChatMessages] = useState([]);
  const [chatInput, setChatInput] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [formulationData, setFormulationData] = useState(null);
  const [showGenOverlay, setShowGenOverlay] = useState(false);
  const [projectId, setProjectId] = useState(routeProjectId || routeParamId || formData?.projectIdToLoad || null);
  const [projectInfo, setProjectInfo] = useState(null);
  const chatEndRef = useRef(null);
  
  // Context control states for toolbar
  const [variantScope, setVariantScope] = useState('current');
  const [componentScope, setComponentScope] = useState('all');
  
  // Load formulation data
  useEffect(() => {
    const id = routeProjectId || routeParamId || formData?.projectIdToLoad;
    if (id) {
      setProjectId(id);
      setShowGenOverlay(true);
      loadFormulationData(id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [routeProjectId, routeParamId]);

  // If a forceOverlay flag is set from navigation, respect it
  useEffect(() => {
    if (formData?.forceOverlay) {
      setShowGenOverlay(true);
    }
  }, [formData?.forceOverlay]);
  
  // Auto-scroll chat
  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages]);
  
  const loadFormulationData = async (projectId) => {
    try {
      setIsGenerating(true);
      
      // Load project info for breadcrumbs
      console.log('🔍 PlaygroundRedesigned: Loading project info for projectId:', projectId);
      try {
        const projectResponse = await apiRequest(`/projects/${projectId}`);
        console.log('🔍 PlaygroundRedesigned: Project response:', projectResponse);
        if (projectResponse.success && projectResponse.data) {
          setProjectInfo(projectResponse.data);
          console.log('🔍 PlaygroundRedesigned: Set projectInfo:', projectResponse.data);
        }
      } catch (projectError) {
        console.error('🔍 PlaygroundRedesigned: Error loading project info:', projectError);
      }
      
      const response = await apiRequest(`/playground/load/${projectId}`);
      if (response.success) {
        setFormulationData(response.data.components);
        // Hide overlay when data is successfully loaded
        if (response.data.components && Object.keys(response.data.components).length > 0) {
          updateFormData('forceOverlay', false);
          setShowGenOverlay(false);
        }
        // Load chat history if available
        if (response.data.conversationHistory && response.data.conversationHistory.length > 0) {
          // Transform backend format to frontend format
          const transformedMessages = response.data.conversationHistory.map(msg => ({
            text: msg.message || msg.text || '',
            isUser: msg.role === 'user',
            timestamp: msg.timestamp || new Date().toISOString()
          }));
          setChatMessages(transformedMessages);
        }
      }
    } catch (error) {
      console.error('Error loading formulation:', error);
    } finally {
      setIsGenerating(false);
    }
  };
  
  const handleSendMessage = async () => {
    if (!chatInput.trim() || isGenerating) return;
    
    const userMessage = {
      text: chatInput,
      isUser: true,
      timestamp: new Date().toISOString()
    };
    
    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsGenerating(true);
    
    try {
      // Send message to API for formulation modification
      // Transform chat messages to backend format
      const backendConversationHistory = chatMessages.map(msg => ({
        role: msg.isUser ? 'user' : 'assistant',
        message: msg.text,
        timestamp: msg.timestamp
      }));
      
      const response = await apiRequest(`/playground/chat`, {
        method: 'POST',
        body: JSON.stringify({
          message: userMessage.text,
          projectId: routeProjectId,
          conversationHistory: backendConversationHistory, // Send full conversation history in backend format
          action: 'modify', // Can be 'modify', 'fix', 'optimize', etc.
          contextControl: {
            variantScope: variantScope,
            componentScope: componentScope,
            // SIMPLIFIED: Direct index mapping
            // Frontend selectedVariation 0 = backend recipes[0]
            selectedVariantIndex: selectedVariation
          }
        })
      });
      
      if (response.success) {
        const { message: aiResponse, changes, upgradedFormulation, version } = response;
        
        // Add AI response to chat
        const aiMessage = {
          text: aiResponse,
          isUser: false,
          timestamp: new Date().toISOString()
        };
        setChatMessages(prev => [...prev, aiMessage]);
        
        // If formulation was updated, reload it but preserve chat
        if (changes && changes.componentsModified && changes.componentsModified.length > 0) {
          // Reload formulation data
          setIsGenerating(true);
          try {
            const reloadResponse = await apiRequest(`/playground/load/${routeProjectId}`);
            if (reloadResponse.success) {
              setFormulationData(reloadResponse.data.components);
              // Load the updated conversation history from backend
              if (reloadResponse.data.conversationHistory && reloadResponse.data.conversationHistory.length > 0) {
                const transformedMessages = reloadResponse.data.conversationHistory.map(msg => ({
                  text: msg.message || msg.text || '',
                  isUser: msg.role === 'user',
                  timestamp: msg.timestamp || new Date().toISOString()
                }));
                setChatMessages(transformedMessages);
              }
            }
          } catch (error) {
            console.error('Error reloading formulation:', error);
          } finally {
            setIsGenerating(false);
          }
          
          // Highlight updated components briefly
          changes.componentsModified.forEach(component => {
            const element = document.getElementById(`widget-${component}`);
            if (element) {
              element.classList.add('ring-2', 'ring-emerald-500', 'ring-offset-2');
              setTimeout(() => {
                element.classList.remove('ring-2', 'ring-emerald-500', 'ring-offset-2');
              }, 3000);
            }
          });
        }
      } else {
        // Show error message
        const errorMessage = {
          text: "I'm having trouble processing your request. Please try again.",
          isUser: false,
          timestamp: new Date().toISOString()
        };
        setChatMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage = {
        text: "There was an error communicating with the server. Please try again.",
        isUser: false,
        timestamp: new Date().toISOString()
      };
      setChatMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Failed project action handlers
  const handleDeleteProject = async () => {
    if (window.confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
      try {
        const response = await apiRequest(`/projects/${projectId}`, {
          method: 'DELETE'
        });
        if (response.success) {
          navigate('/dashboard');
        }
      } catch (error) {
        console.error('Error deleting project:', error);
      }
    }
  };
  
  const handleArchiveProject = async () => {
    try {
      const response = await apiRequest(`/projects/${projectId}`, {
        method: 'PATCH',
        body: JSON.stringify({ status: 'archived' })
      });
      if (response.success) {
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('Error archiving project:', error);
    }
  };
  
  const handleCopyProject = async () => {
    try {
      // Navigate to dashboard with project copy data
      navigate('/dashboard', {
        state: {
          copyProject: {
            industry: projectInfo?.industry,
            category: projectInfo?.category,
            subcategory: projectInfo?.subcategory,
            openWizard: true,
            goToGoals: true
          }
        }
      });
    } catch (error) {
      console.error('Error copying project:', error);
    }
  };
  
  // Get current recipe data
  const currentRecipe = formulationData?.recipes?.[selectedVariation] || formulationData?.mainRecipe || {};
  const ingredients = currentRecipe.ingredients || [];
  const scores = currentRecipe.scores || {};
  const costAnalysis = currentRecipe.cost_analysis || {};
  const nutritionalProfile = currentRecipe.nutritional_profile || {};
  const productionSpecs = currentRecipe.production_specs || {};
  
  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-gray-100 to-blue-100">
      {projectId && (showGenOverlay || (!formulationData || Object.keys(formulationData||{}).length === 0) || ['failed', 'generating', 'processing', 'queued', 'pending', 'in_progress'].includes(projectInfo?.status)) && (
        <GenerationOverlay projectId={projectId} onClose={() => setShowGenOverlay(false)} />
      )}
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate('/dashboard')}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div>
              <div className="flex items-center gap-3">
                <h1 className="text-2xl font-semibold text-gray-900">Product Playground</h1>
                {projectInfo?.status && (
                  <ProjectStatusBadge status={projectInfo.status} />
                )}
              </div>
              <Breadcrumbs 
                items={[
                  { label: 'Herbal' },
                  { label: 'Cosmetics' },
                  { label: 'Hair-Serum' }
                ]}
                className="mt-1"
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <Download className="w-5 h-5 text-gray-600" />
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <Printer className="w-5 h-5 text-gray-600" />
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <Share2 className="w-5 h-5 text-gray-600" />
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <MoreVertical className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>
      </header>
      
      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Chat (30%) */}
        <div className="w-[30%] bg-gradient-to-b from-emerald-50 to-teal-50 border-r-2 border-emerald-200 flex flex-col shadow-xl">
          {/* Chat Header */}
          <div className="px-6 py-2 pt-3 border-b-2 border-emerald-200 bg-gradient-to-r from-emerald-100 to-teal-100">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center shadow-lg">
                <Sparkles className="w-5 h-5 text-white animate-pulse" />
              </div>
              <div>
                <h2 className="font-semibold text-gray-900">AURA</h2>
                <p className="text-xs text-gray-600">Formulation Expert</p>
              </div>
            </div>
          </div>
          
          {/* Chat Messages */}
          <div className="flex-1 overflow-y-auto px-6 py-4 space-y-4">
            {chatMessages.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-emerald-200 to-teal-200 flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <Sparkles className="w-8 h-8 text-emerald-600" />
                </div>
                <h3 className="text-gray-900 font-medium mb-2">Start a conversation</h3>
                <p className="text-sm text-gray-600 max-w-sm mx-auto">
                  Ask me anything about your formulation. I can help optimize ingredients, adjust costs, or improve compliance.
                </p>
              </div>
            ) : (
              <>
                {chatMessages.map((message, index) => (
                  <ChatMessage key={index} message={message} isUser={message.isUser} />
                ))}
                {isGenerating && (
                  <div className="flex gap-3">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center shadow-md">
                      <Sparkles className="w-4 h-4 text-white animate-spin" />
                    </div>
                    <div className="bg-gray-50 rounded-2xl px-4 py-3 border border-gray-200">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                      </div>
                    </div>
                  </div>
                )}
                <div ref={chatEndRef} />
              </>
            )}
          </div>
          
          {/* Context Toolbar - Above Chat Input */}
          {projectInfo?.status !== 'failed' && (
            <div className="border-t border-emerald-200 bg-gradient-to-r from-emerald-50 to-teal-50 shadow-md">
              <ContextToolbar
                variantScope={variantScope}
                setVariantScope={setVariantScope}
                componentScope={componentScope}
                setComponentScope={setComponentScope}
                currentVariantName={
                  formulationData && formulationData.recipes && formulationData.recipes[selectedVariation] 
                    ? formulationData.recipes[selectedVariation].name || 'Main Recipe'
                    : 'Main Recipe'
                }
              />
            </div>
          )}

          {/* Chat Input */}
          {projectInfo?.status !== 'failed' && (
            <div className="px-6 py-4 border-t-2 border-emerald-200 bg-gradient-to-r from-emerald-100 to-teal-100">
              <div className="flex gap-2">
                <textarea
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
                  placeholder="Ask about ingredients, costs, compliance..."
                  className="flex-1 px-4 py-3 bg-white rounded-lg border-2 border-emerald-300 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm shadow-md placeholder-emerald-400 resize-none"
                  disabled={isGenerating}
                  rows="2"
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!chatInput.trim() || isGenerating}
                  className="px-4 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-xl hover:from-emerald-700 hover:to-teal-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg self-end"
                >
                  <Send className="w-5 h-5" />
                </button>
              </div>
            </div>
          )}
        </div>
        
        {/* Right Panel - Product Details (70%) */}
        <div className="flex-1 bg-white flex flex-col overflow-hidden border-l border-gray-200">
          {/* Variation Tabs */}
          <div className="bg-gradient-to-r from-white to-gray-50 border-b-2 border-gray-300 shadow-lg">
            <div className="flex items-center gap-1 px-6 py-2 overflow-x-auto">
              {formulationData?.recipes?.map((recipe, index) => (
                <VariationTab
                  key={index}
                  variation={recipe}
                  isActive={selectedVariation === index}
                  onClick={() => setSelectedVariation(index)}
                  index={index}
                />
              ))}
            </div>
          </div>
          
          {/* Widget Grid */}
          <div className="flex-1 overflow-y-auto py-6 px-8 bg-slate-50">
            <div className="grid grid-cols-12 gap-6">
              {/* Goal Achievement - Full Width */}
              <div className="col-span-12">
                <WidgetCard id="goal-achievement" loading={isGenerating}>
                  <div className="grid grid-cols-5 gap-4">
                    <QualityMeter label="Nutrition" value={scores.nutrition || 0} color="emerald" />
                    <QualityMeter label="Sustainability" value={scores.sustainability || 0} color="green" />
                    <QualityMeter label="Cost Efficiency" value={scores.cost_efficiency || 0} color="blue" />
                    <QualityMeter label="Compliance" value={scores.compliance || 0} color="purple" />
                    <QualityMeter label="Market Appeal" value={scores.market_appeal || 0} color="amber" />
                  </div>
                </WidgetCard>
              </div>
              
              {/* Formulation Recipe - Professional Lab Style */}
              <div className="col-span-7">
                <WidgetCard id="formulation-recipe" title="Formulation Recipe" icon={FlaskConical} expandable loading={false}>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm text-left text-slate-500">
                      <thead className="text-xs text-slate-700 uppercase bg-slate-100">
                        <tr>
                          <th scope="col" className="px-6 py-4 border-l border-dashed border-slate-200 first:border-l-0">Sr. No</th>
                          <th scope="col" className="px-6 py-4 border-l border-dashed border-slate-200">Ingredient Name</th>
                          <th scope="col" className="px-6 py-4 border-l border-dashed border-slate-200">Function</th>
                          <th scope="col" className="px-6 py-4 border-l border-dashed border-slate-200 text-center">%w/w</th>
                          <th scope="col" className="px-6 py-4 border-l border-dashed border-slate-200 text-center">Qty (kg)</th>
                          <th scope="col" className="px-6 py-4 border-l border-dashed border-slate-200 text-right">Rate (₹/kg)</th>
                          <th scope="col" className="px-6 py-4 border-l border-dashed border-slate-200 text-right">Amount (₹)</th>
                        </tr>
                      </thead>
                      <tbody>
                        {ingredients.map((ingredient, index) => (
                          <tr key={index} className="bg-white border-b hover:bg-slate-50 transition-colors">
                            <td className="px-6 py-4 border-l border-dashed border-slate-200 first:border-l-0 font-medium text-slate-900">{index + 1}</td>
                            <td className="px-6 py-4 border-l border-dashed border-slate-200 font-semibold text-slate-900 whitespace-nowrap">{ingredient.name}</td>
                            <td className="px-6 py-4 border-l border-dashed border-slate-200 text-slate-600">{ingredient.function}</td>
                            <td className="px-6 py-4 border-l border-dashed border-slate-200 text-center">
                              <span className="bg-emerald-100 text-emerald-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                {ingredient.percentage}%
                              </span>
                            </td>
                            <td className="px-6 py-4 border-l border-dashed border-slate-200 text-center font-medium text-slate-700">{(ingredient.percentage * 10 / 100).toFixed(2)}</td>
                            <td className="px-6 py-4 border-l border-dashed border-slate-200 text-right font-semibold text-slate-900">₹{ingredient.cost_per_kg}</td>
                            <td className="px-6 py-4 border-l border-dashed border-slate-200 text-right font-semibold text-slate-900">₹{Math.round(ingredient.cost_per_kg * ingredient.percentage * 10 / 100)}</td>
                          </tr>
                        ))}
                        <tr className="bg-slate-100 border-t-2 border-slate-300">
                          <td className="px-6 py-4 border-l border-dashed border-slate-200 first:border-l-0 font-bold text-slate-900 uppercase" colSpan={4}>TOTAL</td>
                          <td className="px-6 py-4 border-l border-dashed border-slate-200 text-center font-bold text-slate-900">10.00 kg</td>
                          <td className="px-6 py-4 border-l border-dashed border-slate-200 text-right font-bold text-slate-900">-</td>
                          <td className="px-6 py-4 border-l border-dashed border-slate-200 text-right font-bold text-slate-900">
                            ₹{ingredients.reduce((sum, ing) => sum + Math.round(ing.cost_per_kg * ing.percentage * 10 / 100), 0)}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </WidgetCard>
              </div>
              
              <div className="col-span-5">
                <WidgetCard id="cost-analysis" title="Cost Analysis & Pricing" icon={DollarSign}>
                  <div className="space-y-5">
                    {/* Manufacturing Costs with Data Visualization */}
                    <div className="bg-slate-50 p-5 rounded-lg border border-slate-200">
                      <h4 className="text-base font-semibold text-slate-900 mb-4">Manufacturing Cost Breakdown</h4>
                      <div className="space-y-4">
                        {/* Raw Materials */}
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium text-slate-700">Raw Materials</span>
                            <span className="text-sm font-semibold text-slate-900">₹{costAnalysis.raw_material_cost || 0}</span>
                          </div>
                          <div className="w-full h-2 bg-slate-200 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-emerald-500 transition-all duration-500 ease-out" 
                              style={{ width: `${((costAnalysis.raw_material_cost || 0) / (costAnalysis.total_cogs || 1)) * 100}%` }}
                            />
                          </div>
                          <div className="text-xs text-slate-600">
                            {Math.round(((costAnalysis.raw_material_cost || 0) / (costAnalysis.total_cogs || 1)) * 100)}% of total cost
                          </div>
                        </div>
                        
                        {/* Processing & Labor */}
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium text-slate-700">Processing & Labor</span>
                            <span className="text-sm font-semibold text-slate-900">₹{costAnalysis.processing_cost || 0}</span>
                          </div>
                          <div className="w-full h-2 bg-slate-200 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-blue-500 transition-all duration-500 ease-out" 
                              style={{ width: `${((costAnalysis.processing_cost || 0) / (costAnalysis.total_cogs || 1)) * 100}%` }}
                            />
                          </div>
                          <div className="text-xs text-slate-600">
                            {Math.round(((costAnalysis.processing_cost || 0) / (costAnalysis.total_cogs || 1)) * 100)}% of total cost
                          </div>
                        </div>
                        
                        {/* Packaging Material */}
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium text-slate-700">Packaging Material</span>
                            <span className="text-sm font-semibold text-slate-900">₹{costAnalysis.packaging_cost || 0}</span>
                          </div>
                          <div className="w-full h-2 bg-slate-200 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-amber-500 transition-all duration-500 ease-out" 
                              style={{ width: `${((costAnalysis.packaging_cost || 0) / (costAnalysis.total_cogs || 1)) * 100}%` }}
                            />
                          </div>
                          <div className="text-xs text-slate-600">
                            {Math.round(((costAnalysis.packaging_cost || 0) / (costAnalysis.total_cogs || 1)) * 100)}% of total cost
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Pricing Summary */}
                    <div className="bg-emerald-50 p-5 rounded-lg border border-emerald-200">
                      <h4 className="text-base font-semibold text-slate-900 mb-4">Pricing Summary</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center px-4 py-3 bg-white rounded-lg border border-slate-200 shadow-sm">
                          <span className="text-sm font-medium text-slate-700">Total COGS</span>
                          <span className="text-base font-semibold text-slate-900">₹{costAnalysis.total_cogs || 0}</span>
                        </div>
                        <div className="flex justify-between items-center px-4 py-3 bg-white rounded-lg border border-emerald-300 shadow-sm">
                          <span className="text-sm font-medium text-slate-700">Suggested MRP</span>
                          <span className="text-base font-semibold text-emerald-600">₹{costAnalysis.suggested_retail || 0}</span>
                        </div>
                        <div className="flex justify-between items-center px-4 py-3 bg-emerald-100 rounded-lg border border-emerald-300">
                          <span className="text-sm font-medium text-slate-700">Gross Margin</span>
                          <span className="text-lg font-bold text-emerald-700">{costAnalysis.margin_percentage || 0}%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </WidgetCard>
              </div>
              
              {/* Nutritional Profile */}
              <div className="col-span-6">
                <WidgetCard title="Nutritional Profile" icon={Package} expandable>
                  <div className="grid grid-cols-2 gap-5">
                    <div>
                      <h4 className="text-base font-semibold text-slate-900 mb-4">Macronutrients</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center py-2 border-b border-slate-200 last:border-b-0">
                          <span className="text-sm font-medium text-slate-700">Protein</span>
                          <span className="text-sm font-semibold text-slate-900">{nutritionalProfile.macronutrients?.protein_g || 0}g</span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-slate-200 last:border-b-0">
                          <span className="text-sm font-medium text-slate-700">Carbohydrates</span>
                          <span className="text-sm font-semibold text-slate-900">{nutritionalProfile.macronutrients?.carbohydrates_g || 0}g</span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-slate-200 last:border-b-0">
                          <span className="text-sm font-medium text-slate-700">Fat</span>
                          <span className="text-sm font-semibold text-slate-900">{nutritionalProfile.macronutrients?.fat_g || 0}g</span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-slate-200 last:border-b-0">
                          <span className="text-sm font-medium text-slate-700">Fiber</span>
                          <span className="text-sm font-semibold text-slate-900">{nutritionalProfile.macronutrients?.fiber_g || 0}g</span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h4 className="text-base font-semibold text-slate-900 mb-4">Per Serving</h4>
                      <div className="text-center p-5 bg-emerald-50 rounded-lg border border-emerald-200">
                        <p className="text-4xl font-bold text-emerald-700">
                          {nutritionalProfile.macronutrients?.calories_per_serving || 0}
                        </p>
                        <p className="text-sm font-medium text-slate-600 mt-2">Calories</p>
                      </div>
                    </div>
                  </div>
                </WidgetCard>
              </div>
              
              {/* Production Specifications */}
              <div className="col-span-6">
                <WidgetCard title="Production Specifications" icon={Package} expandable>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center px-4 py-3 bg-slate-50 rounded-lg border border-slate-200">
                      <span className="text-sm font-medium text-slate-700">Batch Size</span>
                      <span className="text-sm font-semibold text-slate-900">{productionSpecs.batch_size || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between items-center px-4 py-3 bg-slate-50 rounded-lg border border-slate-200">
                      <span className="text-sm font-medium text-slate-700">Shelf Life</span>
                      <span className="text-sm font-semibold text-slate-900">{productionSpecs.shelf_life || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between items-center px-4 py-3 bg-slate-50 rounded-lg border border-slate-200">
                      <span className="text-sm font-medium text-slate-700">Storage Conditions</span>
                      <span className="text-sm font-semibold text-slate-900">{productionSpecs.storage_conditions || 'N/A'}</span>
                    </div>
                  </div>
                </WidgetCard>
              </div>
              
              {/* Manufacturing Process */}
              <div className="col-span-12">
                <WidgetCard title="Manufacturing Process" icon={Package} expandable>
                  <div className="prose prose-sm max-w-none text-gray-600">
                    <p>{productionSpecs.manufacturing_process || 'Manufacturing process details will be displayed here based on the formulation requirements.'}</p>
                  </div>
                </WidgetCard>
              </div>
              
              {/* Compliance Status */}
              <div className="col-span-6">
                <WidgetCard title="Compliance & Regulatory" icon={Shield} expandable>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 px-3 py-2 bg-emerald-50 rounded-lg border border-emerald-200">
                      <Check className="w-5 h-5 text-emerald-600 flex-shrink-0" />
                      <span className="text-sm font-medium text-slate-700">FSSAI Compliant</span>
                    </div>
                    <div className="flex items-center gap-3 px-3 py-2 bg-amber-50 rounded-lg border border-amber-200">
                      <AlertCircle className="w-5 h-5 text-amber-600 flex-shrink-0" />
                      <span className="text-sm font-medium text-slate-700">FDA Review Pending</span>
                    </div>
                    <div className="flex items-center gap-3 px-3 py-2 bg-emerald-50 rounded-lg border border-emerald-200">
                      <Check className="w-5 h-5 text-emerald-600 flex-shrink-0" />
                      <span className="text-sm font-medium text-slate-700">EU Standards Met</span>
                    </div>
                  </div>
                </WidgetCard>
              </div>
              
              {/* Sustainability Metrics */}
              <div className="col-span-6">
                <WidgetCard title="Sustainability Metrics" icon={Leaf} expandable>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-slate-700">Carbon Footprint</span>
                        <span className="text-sm font-semibold text-emerald-700">Low</span>
                      </div>
                      <div className="w-full h-3 bg-slate-200 rounded-full overflow-hidden">
                        <div className="h-full bg-emerald-500 w-1/3 transition-all duration-500 ease-out" />
                      </div>
                      <div className="text-xs text-slate-600 mt-1">33% of industry average</div>
                    </div>
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-slate-700">Packaging Recyclability</span>
                        <span className="text-sm font-semibold text-emerald-700">85%</span>
                      </div>
                      <div className="w-full h-3 bg-slate-200 rounded-full overflow-hidden">
                        <div className="h-full bg-emerald-500 w-5/6 transition-all duration-500 ease-out" />
                      </div>
                      <div className="text-xs text-slate-600 mt-1">Above industry standard</div>
                    </div>
                  </div>
                </WidgetCard>
              </div>
              
              {/* Market Analysis */}
              <div className="col-span-6">
                <WidgetCard title="Market Analysis" icon={TrendingUp} expandable>
                  <div className="space-y-4">
                    <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
                      <h4 className="font-semibold text-gray-900 text-sm mb-2">Target Market</h4>
                      <div className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Health Conscious Adults</span>
                          <span className="font-medium text-purple-700">65%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Fitness Enthusiasts</span>
                          <span className="font-medium text-purple-700">25%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Premium Consumers</span>
                          <span className="font-medium text-purple-700">10%</span>
                        </div>
                      </div>
                    </div>
                    <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
                      <h4 className="font-semibold text-gray-900 text-sm mb-2">Market Opportunity</h4>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-emerald-600">₹2,450 Cr</p>
                        <p className="text-xs text-gray-600">Addressable Market Size</p>
                      </div>
                    </div>
                  </div>
                </WidgetCard>
              </div>
              
              {/* Quality Testing */}
              <div className="col-span-6">
                <WidgetCard title="Quality Testing Protocol" icon={FlaskConical} expandable>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-2 bg-green-50 rounded border border-green-200">
                      <div className="flex items-center gap-2">
                        <Check className="w-4 h-4 text-emerald-600" />
                        <span className="text-sm text-gray-700">Microbiological Testing</span>
                      </div>
                      <span className="text-xs text-green-600 font-medium">Passed</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-yellow-50 rounded border border-yellow-200">
                      <div className="flex items-center gap-2">
                        <RefreshCw className="w-4 h-4 text-gray-600" />
                        <span className="text-sm text-gray-700">Heavy Metal Analysis</span>
                      </div>
                      <span className="text-xs text-gray-700 font-semibold">In Progress</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-green-50 rounded border border-green-200">
                      <div className="flex items-center gap-2">
                        <Check className="w-4 h-4 text-emerald-600" />
                        <span className="text-sm text-gray-700">Nutritional Validation</span>
                      </div>
                      <span className="text-xs text-green-600 font-medium">Passed</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-blue-50 rounded border border-blue-200">
                      <div className="flex items-center gap-2">
                        <Info className="w-4 h-4 text-gray-600" />
                        <span className="text-sm text-gray-700">Stability Testing</span>
                      </div>
                      <span className="text-xs text-gray-700 font-semibold">Scheduled</span>
                    </div>
                  </div>
                </WidgetCard>
              </div>
              
              
              {/* Ingredients Database with Supplier Information */}
              <div className="col-span-12">
                <WidgetCard title="Ingredients Database & Suppliers" icon={Package} expandable>
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-4">Available ingredients from certified suppliers - the building blocks for your formulations</p>
                    <div className="flex flex-wrap gap-2 mb-4">
                      <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">Certified Organic</span>
                      <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">Natural Extract</span>
                      <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">Active Compound</span>
                      <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">Premium Grade</span>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-6">
                    <div className="p-5 bg-green-50 rounded-xl border-2 border-green-300 shadow-md hover:shadow-lg transition-shadow">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-gray-900">Whey Protein Isolate</h4>
                        <span className="px-2 py-1 bg-emerald-50 text-emerald-700 text-xs font-medium rounded-full">Certified</span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">High bioavailability protein source</p>
                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div className="space-y-1">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Purity:</span>
                            <span className="font-medium">90-95%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Cost:</span>
                            <span className="font-semibold text-emerald-600">₹850/kg</span>
                          </div>
                        </div>
                        <div className="bg-white p-3 rounded-lg border-2 border-green-200 shadow-sm">
                          <div className="flex items-center gap-1 mb-1">
                            <Users className="w-3 h-3 text-gray-600" />
                            <span className="font-medium text-gray-900 text-xs">Nutraceutical Corp</span>
                          </div>
                          <div className="text-xs text-gray-600">
                            <div>Lead Time: 7-10 days</div>
                            <div>Quality: A+ Verified</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-5 bg-blue-50 rounded-xl border-2 border-blue-300 shadow-md hover:shadow-lg transition-shadow">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-gray-900">Ashwagandha Extract</h4>
                        <span className="px-2 py-1 bg-emerald-50 text-emerald-700 text-xs font-medium rounded-full">Premium</span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">Standardized adaptogenic herb</p>
                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div className="space-y-1">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Withanolides:</span>
                            <span className="font-medium">5-10%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Cost:</span>
                            <span className="font-semibold text-emerald-600">₹1,200/kg</span>
                          </div>
                        </div>
                        <div className="bg-white p-3 rounded-lg border-2 border-blue-200 shadow-sm">
                          <div className="flex items-center gap-1 mb-1">
                            <Users className="w-3 h-3 text-gray-600" />
                            <span className="font-medium text-gray-900 text-xs">BioActive Solutions</span>
                          </div>
                          <div className="text-xs text-gray-600">
                            <div>Lead Time: 5-7 days</div>
                            <div>Quality: A Preferred</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-5 bg-purple-50 rounded-xl border-2 border-purple-300 shadow-md hover:shadow-lg transition-shadow">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-gray-900">Curcumin Complex</h4>
                        <span className="px-2 py-1 bg-emerald-50 text-emerald-700 text-xs font-medium rounded-full">Active</span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">Bioavailable turmeric compound</p>
                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div className="space-y-1">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Curcumin:</span>
                            <span className="font-medium">95%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Cost:</span>
                            <span className="font-semibold text-emerald-600">₹2,400/kg</span>
                          </div>
                        </div>
                        <div className="bg-white p-3 rounded-lg border-2 border-purple-200 shadow-sm">
                          <div className="flex items-center gap-1 mb-1">
                            <Users className="w-3 h-3 text-gray-600" />
                            <span className="font-medium text-gray-900 text-xs">Green Valley Extracts</span>
                          </div>
                          <div className="text-xs text-gray-600">
                            <div>Lead Time: 10-14 days</div>
                            <div>Quality: A+ Organic</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-5 bg-amber-50 rounded-xl border-2 border-amber-300 shadow-md hover:shadow-lg transition-shadow">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-gray-900">Vitamin D3</h4>
                        <span className="px-2 py-1 bg-emerald-50 text-emerald-700 text-xs font-medium rounded-full">Essential</span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">Cholecalciferol supplement</p>
                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div className="space-y-1">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Potency:</span>
                            <span className="font-medium">40,000 IU/g</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Cost:</span>
                            <span className="font-semibold text-emerald-600">₹3,500/kg</span>
                          </div>
                        </div>
                        <div className="bg-white p-3 rounded-lg border-2 border-amber-200 shadow-sm">
                          <div className="flex items-center gap-1 mb-1">
                            <Users className="w-3 h-3 text-gray-600" />
                            <span className="font-medium text-gray-900 text-xs">Pharma Ingredients Ltd</span>
                          </div>
                          <div className="text-xs text-gray-600">
                            <div>Lead Time: 3-5 days</div>
                            <div>Quality: A+ Pharma Grade</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="mt-6 flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      Showing 4 of 2,847 available ingredients with verified suppliers
                    </div>
                    <button className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors">
                      Browse Full Database
                    </button>
                  </div>
                </WidgetCard>
              
              {/* Regulatory Pathway */}
              <div className="col-span-6">
                <WidgetCard title="Regulatory Pathway" icon={Shield} expandable>
                  <div className="space-y-4">
                    <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <h4 className="font-semibold text-gray-900 mb-2">Approval Timeline</h4>
                      <p className="text-sm text-gray-600">6-9 months for FSSAI approval, 3-4 months for organic certification</p>
                    </div>
                    <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
                      <h4 className="font-semibold text-gray-900 mb-2">Required Studies</h4>
                      <div className="space-y-1 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-purple-600 rounded-full"></div>
                          <span>Nutritional analysis and labeling verification</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-purple-600 rounded-full"></div>
                          <span>Microbiological testing for shelf stability</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-purple-600 rounded-full"></div>
                          <span>Heavy metals and contaminants testing</span>
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                        <h4 className="font-semibold text-gray-900 text-sm mb-1">Regulatory Costs</h4>
                        <p className="text-sm text-emerald-600 font-semibold">₹2.5-4.2 Lakhs</p>
                      </div>
                      <div className="p-3 bg-amber-50 rounded-lg border border-amber-200">
                        <h4 className="font-semibold text-gray-900 text-sm mb-1">Key Milestones</h4>
                        <p className="text-sm text-gray-600">4 major checkpoints</p>
                      </div>
                    </div>
                  </div>
                </WidgetCard>
              </div>
              
              {/* Manufacturing Recommendations */}
              <div className="col-span-6">
                <WidgetCard title="Manufacturing Recommendations" icon={Factory} expandable>
                  <div className="space-y-4">
                    <div className="p-3 bg-indigo-50 rounded-lg border border-indigo-200">
                      <h4 className="font-semibold text-gray-900 mb-2">Preferred Locations</h4>
                      <div className="space-y-1 text-sm text-gray-600">
                        <div className="flex justify-between">
                          <span>Primary:</span>
                          <span className="font-medium">Gujarat, Himachal Pradesh</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Secondary:</span>
                          <span className="font-medium">Karnataka, Tamil Nadu</span>
                        </div>
                      </div>
                    </div>
                    <div className="p-3 bg-emerald-50 rounded-lg border border-emerald-200">
                      <h4 className="font-semibold text-gray-900 mb-2">Equipment Requirements</h4>
                      <p className="text-sm text-gray-600">High-shear mixers, encapsulation line, quality control lab setup</p>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="p-3 bg-rose-50 rounded-lg border border-rose-200">
                        <h4 className="font-semibold text-rose-900 text-sm mb-1">Quality Standards</h4>
                        <p className="text-sm text-rose-700">ISO 22000, HACCP</p>
                      </div>
                      <div className="p-3 bg-cyan-50 rounded-lg border border-cyan-200">
                        <h4 className="font-semibold text-gray-900 text-sm mb-1">Capacity Planning</h4>
                        <p className="text-sm text-gray-600">500kg/batch optimal</p>
                      </div>
                    </div>
                  </div>
                </WidgetCard>
              </div>
              
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Processing Modal */}
      <ProcessingModal 
        isVisible={isGenerating} 
        message="Optimizing your formulation with AI assistance..."
      />
    </div>
  );
};

export default ProductPlayground;
