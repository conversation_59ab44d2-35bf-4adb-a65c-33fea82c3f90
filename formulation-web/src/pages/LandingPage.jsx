import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronRight, Beaker, Target, Truck, Award, TrendingUp, Leaf, Droplets, Users, Globe, Settings, Brain, Shield, BarChart3, MessageSquare, Pill, LogOut } from 'lucide-react';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import Logo from '../components/Logo';

const LandingPage = () => {
  const { setCurrentStep } = useApp();
  const { isAuthenticated, user, logout } = useAuth();
  const navigate = useNavigate();

  const handleStartBuilding = () => {
    if (isAuthenticated) {
      navigate('/dashboard');
    } else {
      // Navigate to login page
      navigate('/login');
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-white">
      {/* Header */}
      <header className="px-6 py-4 bg-white shadow-sm">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <Logo />
          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <>
                <div className="text-sm text-gray-600">
                  Welcome, <span className="font-medium">{user?.first_name || user?.email?.split('@')[0]}</span>
                </div>
                <button 
                  onClick={handleLogout}
                  className="flex items-center text-gray-600 hover:text-gray-800 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </button>
              </>
            ) : (
              <>
                <button 
                  onClick={() => navigate('/login')}
                  className="text-gray-600 hover:text-gray-800"
                >
                  Sign In
                </button>
                <button 
                  onClick={() => navigate('/login')}
                  className="bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800"
                >
                  Get Started
                </button>
              </>
            )}
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-6 py-20">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Hero image - 50% */}
          <div className="flex items-center justify-center">
            <img 
              src="/assets/hero-01.jpg" 
              alt="Wellness Formulation Laboratory" 
              className="w-full h-auto rounded-2xl shadow-xl object-cover max-h-96"
            />
          </div>
          
          {/* Hero content moved to right - 50% width */}
          <div className="space-y-8">
            <div>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4 leading-tight">
                Professional <span className="text-green-600">Formulation</span> Platform
              </h1>
              <p className="text-lg text-gray-600 leading-relaxed">
                Enterprise-grade formulation design and optimization for nutraceuticals, functional beverages, and herbal cosmetics.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="border-2 border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium hover:border-gray-400 hover:bg-gray-50 transition-colors">
                View Demo
              </button>
              <button 
                onClick={handleStartBuilding}
                className="bg-gray-900 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-800 flex items-center justify-center transition-colors"
              >
                {isAuthenticated ? 'Start Building' : 'Sign In to Start'} <ChevronRight className="ml-2 w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Feature Cards Section - Moved below hero and spread across page */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
              <Beaker className="w-8 h-8 text-green-500 mb-4" />
              <h3 className="font-semibold mb-2">Smart Formulation</h3>
              <p className="text-sm text-gray-600">AI-powered ingredient selection and ratio optimization</p>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
              <Target className="w-8 h-8 text-blue-500 mb-4" />
              <h3 className="font-semibold mb-2">Goal-Driven Design</h3>
              <p className="text-sm text-gray-600">Target specific nutrition and performance metrics</p>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
              <Truck className="w-8 h-8 text-purple-500 mb-4" />
              <h3 className="font-semibold mb-2">Manufacturing Ready</h3>
              <p className="text-sm text-gray-600">Complete process docs and supplier recommendations</p>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
              <Award className="w-8 h-8 text-orange-500 mb-4" />
              <h3 className="font-semibold mb-2">Compliance Built-in</h3>
              <p className="text-sm text-gray-600">FDA, FSSAI, and organic certification support</p>
            </div>
          </div>
        </div>
      </div>

      {/* Platform Features Section */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Enterprise Platform Features</h2>
            <p className="text-xl text-gray-600">Comprehensive tools for professional formulation development</p>
          </div>

          <div className="grid grid-cols-3 gap-8 mb-12">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Brain className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold mb-3">AI-Powered Analysis</h3>
              <p className="text-gray-600">Advanced machine learning algorithms optimize ingredient combinations for maximum efficacy and cost efficiency.</p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Regulatory Compliance</h3>
              <p className="text-gray-600">Built-in compliance checking for FDA, FSSAI, EU regulations, and organic certification standards.</p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Real-time Analytics</h3>
              <p className="text-gray-600">Track formulation performance, cost metrics, and sustainability scores with comprehensive dashboards.</p>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Team Collaboration</h3>
              <p className="text-gray-600">Enable cross-functional teams to collaborate seamlessly with role-based access and approval workflows.</p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Globe className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Global Supply Chain</h3>
              <p className="text-gray-600">Access verified suppliers worldwide with quality certifications and competitive pricing data.</p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Settings className="w-8 h-8 text-gray-600" />
              </div>
              <h3 className="text-lg font-semibold mb-3">API Integration</h3>
              <p className="text-gray-600">Seamlessly integrate with existing ERP, PLM, and manufacturing systems through robust APIs.</p>
            </div>
          </div>
        </div>
      </div>

      {/* Testimonials Section - Moved lower */}
      <div className="bg-gray-50 py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Trusted by Industry Leaders</h2>
            <p className="text-xl text-gray-600">See what our customers say about transforming their formulation process</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-xl shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full mr-4 bg-green-500 flex items-center justify-center text-white font-bold">SC</div>
                <div>
                  <h4 className="font-semibold text-gray-900">Dr. Sarah Chen</h4>
                  <p className="text-sm text-gray-600">R&D Director, NutriTech Solutions</p>
                </div>
              </div>
              <p className="text-gray-700 mb-4">"The AI-powered formulation suggestions cut our development time by 60%. We can now bring products to market in weeks instead of months."</p>
              <div className="flex text-yellow-400">
                <span>★★★★★</span>
              </div>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full mr-4 bg-blue-500 flex items-center justify-center text-white font-bold">MR</div>
                <div>
                  <h4 className="font-semibold text-gray-900">Michael Rodriguez</h4>
                  <p className="text-sm text-gray-600">CEO, Pure Wellness Co.</p>
                </div>
              </div>
              <p className="text-gray-700 mb-4">"The compliance features saved us countless hours of regulatory research. Everything is automatically checked against FDA and organic standards."</p>
              <div className="flex text-yellow-400">
                <span>★★★★★</span>
              </div>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full mr-4 bg-purple-500 flex items-center justify-center text-white font-bold">PS</div>
                <div>
                  <h4 className="font-semibold text-gray-900">Dr. Priya Sharma</h4>
                  <p className="text-sm text-gray-600">Formulation Scientist, Ayurvedic Naturals</p>
                </div>
              </div>
              <p className="text-gray-700 mb-4">"The ingredient database is incredibly comprehensive, especially for herbal formulations. The cost optimization features help maintain profitability."</p>
              <div className="flex text-yellow-400">
                <span>★★★★★</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action Section */}
      <div className="bg-gray-900 text-white py-16">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-4xl font-bold mb-6">Ready to Transform Your Formulation Process?</h2>
          <p className="text-xl text-gray-300 mb-8">
            Join leading companies using our platform to accelerate product development and reduce time-to-market.
          </p>
          <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-8">
            <button className="bg-white text-gray-900 px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-100 transition-colors">
              Start Free Trial
            </button>
            <button className="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-medium hover:bg-white hover:text-gray-900 transition-colors">
              Schedule Demo
            </button>
          </div>
          <p className="text-sm text-gray-400 mt-6">No credit card required • 30-day free trial • Setup in minutes</p>
        </div>
      </div>

      {/* Industry Focus */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-6">
          <h2 className="text-3xl font-bold text-center mb-12">Specialized for Key Industries</h2>
          <div className="grid grid-cols-3 gap-8">
            <div className="text-center p-6 rounded-xl border-2 border-gray-100 hover:border-green-200 transition-colors">
              <Droplets className="w-12 h-12 text-blue-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Functional Beverages</h3>
              <p className="text-gray-600">Antioxidant juices, energy drinks, and wellness beverages</p>
            </div>
            <div className="text-center p-6 rounded-xl border-2 border-gray-100 hover:border-green-200 transition-colors">
              <Pill className="w-12 h-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Nutraceuticals</h3>
              <p className="text-gray-600">Supplements, capsules, powders, and liquid formulations</p>
            </div>
            <div className="text-center p-6 rounded-xl border-2 border-gray-100 hover:border-green-200 transition-colors">
              <Leaf className="w-12 h-12 text-purple-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Herbal Cosmetics</h3>
              <p className="text-gray-600">Natural skincare, Ayurvedic formulations, and organic beauty</p>
            </div>
          </div>
        </div>
      </div>

      {/* Comprehensive Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-5 gap-8 mb-12">
            {/* Company Info */}
            <div className="col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex space-x-1">
                  <div className="w-4 h-4 bg-green-400 rounded-full"></div>
                  <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                  <div className="w-0 h-0 border-l-[8px] border-l-green-400 border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent"></div>
                  <div className="w-0 h-0 border-l-[8px] border-l-green-500 border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent"></div>
                </div>
                <span className="text-xl font-bold text-white">agrizy wellness</span>
              </div>
              <p className="text-gray-400 mb-6 max-w-md">
                Enterprise-grade formulation platform for nutraceuticals, functional beverages, and herbal cosmetics. Trusted by leading manufacturers worldwide.
              </p>
              <div className="flex space-x-4">
                <button className="bg-gray-800 hover:bg-gray-700 p-2 rounded-lg">
                  <Globe className="w-5 h-5" />
                </button>
                <button className="bg-gray-800 hover:bg-gray-700 p-2 rounded-lg">
                  <MessageSquare className="w-5 h-5" />
                </button>
                <button className="bg-gray-800 hover:bg-gray-700 p-2 rounded-lg">
                  <Users className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Products */}
            <div>
              <h4 className="text-lg font-semibold mb-4">Products</h4>
              <ul className="space-y-3 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Formulation Engine</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Ingredient Database</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Compliance Suite</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Manufacturing Tools</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Analytics Dashboard</a></li>
              </ul>
            </div>

            {/* Resources */}
            <div>
              <h4 className="text-lg font-semibold mb-4">Resources</h4>
              <ul className="space-y-3 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-white transition-colors">API Reference</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Case Studies</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Best Practices</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Webinars</a></li>
              </ul>
            </div>

            {/* Company */}
            <div>
              <h4 className="text-lg font-semibold mb-4">Company</h4>
              <ul className="space-y-3 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">About Us</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Partners</a></li>
                <li><a href="#" className="hover:text-white transition-colors">News</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-gray-800 pt-8">
            <div className="flex justify-between items-center">
              <div className="text-gray-400 text-sm">
                © 2025 Agrizy Wellness. All rights reserved.
              </div>
              <div className="flex space-x-6 text-sm text-gray-400">
                <a href="#" className="hover:text-white transition-colors">Privacy Policy</a>
                <a href="#" className="hover:text-white transition-colors">Terms of Service</a>
                <a href="#" className="hover:text-white transition-colors">Cookie Policy</a>
                <a href="#" className="hover:text-white transition-colors">Security</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;