import React, { useEffect, useMemo, useState } from 'react';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import Breadcrumbs from '../components/Breadcrumbs';
import AuraOverlay from '../components/AuraOverlay';

const Row = ({ label, value }) => (
  <div className="flex justify-between py-2 border-b border-slate-100 last:border-0">
    <span className="text-slate-600">{label}</span>
    <span className="font-medium text-slate-900 capitalize">{value || '—'}</span>
  </div>
);

const SummaryEmbedded = () => {
  const { formData, updateFormData } = useApp();
  const { apiRequest } = useAuth();
  const { industry, productCategory, productSubCategory, goals } = formData;
  const [notes, setNotes] = useState(formData.notes || '');
  const [loading, setLoading] = useState(false);
  const [auraOpen, setAuraOpen] = useState(false);
  const [auraLoading, setAuraLoading] = useState(false);
  const [auraText, setAuraText] = useState('');
  const [bindings, setBindings] = useState([]);
  const [answersMap, setAnswersMap] = useState({});

  const inspire = async () => {
    try {
      setAuraOpen(true);
      setAuraLoading(true);
      const payload = {
        industry,
        category: productCategory,
        sub_category: productSubCategory,
        goals: goals || {}
      };
      const resp = await apiRequest('/playground/suggest-notes', { method: 'POST', body: JSON.stringify(payload) });
      const text = resp?.data?.notes || '';
      setAuraText(text);
    } catch (e) {
      console.error('Inspire me failed:', e);
      setAuraText('Sorry — I could not fetch a note right now. Please try again.');
    } finally {
      setAuraLoading(false);
    }
  };

  const toTitleCase = (s) => (s || '—')
    .toString()
    .replace(/-/g, ' ')
    .replace(/\b\w/g, (c) => c.toUpperCase());

  const crumbItems = useMemo(() => (
    [
      { label: toTitleCase(industry) },
      { label: toTitleCase(productCategory) },
      { label: toTitleCase(productSubCategory) },
    ].filter(Boolean)
  ), [industry, productCategory, productSubCategory]);

  // Load parameter bindings and answer label maps so we can show names and labels instead of IDs
  useEffect(() => {
    let cancelled = false;
    const load = async () => {
      try {
        if (!productSubCategory) return;
        const resp = await apiRequest(`/param-bindings?level=sub_category&slug=${encodeURIComponent(productSubCategory)}`);
        const data = resp?.data || [];
        if (cancelled) return;
        setBindings(data);

        // fetch answer sets for enum/selects
        const keys = Array.from(new Set((data || []).map(b => b.answers_key).filter(Boolean)));
        if (keys.length) {
          const answersResp = await apiRequest(`/param-answers?keys=${encodeURIComponent(keys.join(','))}`);
          if (cancelled) return;
          setAnswersMap(answersResp?.data || {});
        } else {
          setAnswersMap({});
        }
      } catch (e) {
        console.error('Failed to load bindings/answers for summary:', e);
      }
    };
    load();
    return () => { cancelled = true; };
  }, [apiRequest, productSubCategory]);

  const byKey = useMemo(() => {
    const map = new Map();
    bindings.forEach(b => { if (!map.has(b.parameter_key)) map.set(b.parameter_key, b); });
    return map;
  }, [bindings]);

  const renderValue = (k, v) => {
    const b = byKey.get(k);
    if (!b) return String(v);
    // enum/select → map to label when possible
    if ((b.type === 'enum' || b?.widget?.type === 'select') && b.answers_key && answersMap[b.answers_key]) {
      const opts = answersMap[b.answers_key]?.options || [];
      const hit = opts.find(o => String(o.value) === String(v));
      return hit ? hit.label : String(v);
    }
    // boolean → Yes/No
    if (b.type === 'boolean') return v ? 'Yes' : 'No';
    // number → append unit when available
    if (b.type === 'number' && b?.guidance?.unit) {
      return `${String(v)} ${b.guidance.unit}`;
    }
    return String(v);
  };

  return (
    <>
    <div className="space-y-4">
      <div className="bg-white rounded-xl border border-slate-200 p-4">
        <div className="flex items-center justify-between mb-3">
          <Breadcrumbs items={crumbItems} className="mb-0" size="base" />
          <button onClick={inspire} className="text-emerald-700 text-sm underline hover:text-emerald-800" disabled={auraLoading}>
            {auraLoading ? 'Invoking Aura…' : 'Get Inspired by Aura ✨'}
          </button>
        </div>
        <textarea
          value={notes}
          onChange={(e)=>{ setNotes(e.target.value); updateFormData('notes', e.target.value); }}
          placeholder="Add any specific guidance, considerations, or hypotheses for the AI to keep in mind."
          rows={5}
          className="w-full p-3 border border-slate-300 rounded-md focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 text-sm mb-4"
        />
        {goals && Object.keys(goals).length > 0 ? (
          <div>
            <h4 className="text-sm font-semibold text-slate-900 mb-2">Specific Goals</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {Object.entries(goals).slice(0, 8).map(([k, v]) => {
                const binding = byKey.get(k);
                const label = binding?.name || k;
                const valueDisp = renderValue(k, v);
                return (
                  <div key={k} className="text-sm flex items-start justify-between gap-4">
                    <span className="text-slate-600 break-all">{label}</span>
                    <span className="text-slate-900 font-medium break-all">{valueDisp}</span>
                  </div>
                );
              })}
            </div>
          </div>
        ) : (
          <div className="text-sm text-slate-600">No goals specified.</div>
        )}
      </div>
    </div>
    <AuraOverlay
      open={auraOpen}
      loading={auraLoading}
      text={auraText}
      onClose={() => setAuraOpen(false)}
      onUse={() => { setNotes(auraText); updateFormData('notes', auraText); setAuraOpen(false); }}
    />
    </>
  );
};

export default SummaryEmbedded;
