@tailwind base;
@tailwind components;
@tailwind utilities;

/* Simple slide animations for wizard modal */
@keyframes slideInRight { from { transform: translateX(100%); opacity: 0.6; } to { transform: translateX(0); opacity: 1; } }
@keyframes slideOutLeft { from { transform: translateX(0); opacity: 1; } to { transform: translateX(-20%); opacity: 0; } }
@keyframes slideInLeft { from { transform: translateX(-100%); opacity: 0.6; } to { transform: translateX(0); opacity: 1; } }
@keyframes slideOutRight { from { transform: translateX(0); opacity: 1; } to { transform: translateX(20%); opacity: 0; } }

.slide-in-right { animation: slideInRight 260ms ease forwards; }
.slide-out-left { animation: slideOutLeft 260ms ease forwards; }
.slide-in-left { animation: slideInLeft 260ms ease forwards; }
.slide-out-right { animation: slideOutRight 260ms ease forwards; }

/* Compact, themed sliders */
input[type="range"] {
  height: 18px;
}
input[type="range"]::-webkit-slider-runnable-track {
  height: 4px;
  background: rgb(203 213 225); /* slate-300 */
  border-radius: 9999px;
}
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 14px;
  height: 14px;
  margin-top: -5px; /* center thumb on 4px track */
  background: rgb(5 150 105); /* emerald-600 */
  border-radius: 9999px;
  box-shadow: 0 0 0 2px white;
}
input[type="range"]::-moz-range-track {
  height: 4px;
  background: rgb(203 213 225);
  border-radius: 9999px;
}
input[type="range"]::-moz-range-thumb {
  width: 14px;
  height: 14px;
  background: rgb(5 150 105);
  border: none;
  border-radius: 9999px;
}

/* Aura overlay micro-animations */
@keyframes auraFloat { 0% { transform: translateY(0) } 50% { transform: translateY(-6px) } 100% { transform: translateY(0) } }
@keyframes spinSlow { from { transform: rotate(0deg) } to { transform: rotate(360deg) } }
@keyframes glowPulse { 0% { box-shadow: 0 0 0 0 rgba(16,185,129,.35) } 70% { box-shadow: 0 0 0 12px rgba(16,185,129,0) } 100% { box-shadow: 0 0 0 0 rgba(16,185,129,0) } }
@keyframes twinkle { 0%,100% { opacity: .25; transform: scale(.9) } 50% { opacity: 1; transform: scale(1.05) } }
@keyframes shimmer { 0% { background-position: -200% 0 } 100% { background-position: 200% 0 } }

.aura-float { animation: auraFloat 3.2s ease-in-out infinite; }
.spin-slow { animation: spinSlow 12s linear infinite; }
.glow-pulse { animation: glowPulse 2.2s ease-out infinite; }
.twinkle { animation: twinkle 2.4s ease-in-out infinite; }
.shimmer { background-image: linear-gradient(90deg, rgba(16,185,129,0.0) 0%, rgba(16,185,129,0.25) 50%, rgba(16,185,129,0.0) 100%); background-size: 200% 100%; animation: shimmer 2.2s linear infinite; }

/* Chemistry (beaker + bubbles + orbit) */
@keyframes bubbleUp { 0% { transform: translateY(8px) scale(.9); opacity: .0 } 40% { opacity: .9 } 100% { transform: translateY(-26px) scale(1); opacity: 0 } }
@keyframes orbit { from { transform: rotate(0deg) } to { transform: rotate(360deg) } }
@keyframes flaskGlow { 0%,100% { box-shadow: 0 0 0 0 rgba(16,185,129,.25) } 50% { box-shadow: 0 0 24px 4px rgba(16,185,129,.28) } }
.chem-orbit { animation: orbit 9s linear infinite; }
.chem-bubble { animation: bubbleUp 2.2s ease-in infinite; }
.chem-glow { animation: flaskGlow 2.6s ease-in-out infinite; }
