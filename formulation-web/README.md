# AgriZy Formulation Platform - Frontend

This is the frontend React application for the AgriZy Formulation Platform, an enterprise-grade web application for AI-powered formulation development in the health and wellness industry.

## Technology Stack

- **Framework**: React 19.1.0
- **Build Tool**: Vite 7.0.4
- **Styling**: Tailwind CSS 3.4.17
- **Icons**: Lucide React 0.525.0
- **Routing**: React Router DOM 7.7.0
- **Package Manager**: pnpm

## Features

- Multi-step formulation wizard
- AI-powered formulation generation
- Interactive product playground
- Real-time formulation optimization
- Responsive design with Tailwind CSS
- JWT-based authentication
- Protected routes

## Getting Started

### Prerequisites

- Node.js 18+ 
- pnpm
- Backend API running (formulation-api)

### Installation

1. Install dependencies:
```bash
pnpm install
```

2. Start development server:
```bash
pnpm run dev
```

3. Build for production:
```bash
pnpm run build
```

4. Preview production build:
```bash
pnpm run preview
```

## Development

### Available Scripts

- `pnpm run dev` - Start development server with HMR
- `pnpm run build` - Build for production
- `pnpm run preview` - Preview production build locally
- `pnpm run lint` - Run ESLint

### Environment Variables

Create a `.env.local` file in the root directory:

```env
VITE_API_BASE_URL=http://localhost:3001/api
```

### Project Structure

```
src/
├── components/          # Reusable UI components
├── pages/              # Page components
├── context/            # React Context providers
├── App.jsx             # Main application component
└── main.jsx            # Entry point
```

## Backend Integration

This frontend connects to the AgriZy Formulation API. Make sure the backend is running on `http://localhost:3001` or update the `VITE_API_BASE_URL` environment variable.

## Deployment

The application can be deployed as static files to any web server or CDN:

1. Build the application: `pnpm run build`
2. Deploy the `dist/` directory to your hosting platform

Popular deployment options:
- Vercel
- Netlify
- AWS S3 + CloudFront
- Nginx/Apache

## Contributing

1. Create a feature branch
2. Make your changes
3. Test thoroughly
4. Submit a pull request

## License

Proprietary - AgriZy Wellness Platform