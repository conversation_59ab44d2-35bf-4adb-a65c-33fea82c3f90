# Agrizy Formulation Platform - Project Rules

## Overview
This is a full-stack formulation platform for cosmetic and personal care product development, consisting of a React frontend and Node.js/Express backend with MongoDB database.

## Architecture

### Frontend (formulation-web)
- **Framework**: React 18 with Vite
- **Styling**: Tailwind CSS
- **State Management**: React Context API
- **Port**: 7700 (development)
- **Main Components**:
  - ProductPlayground: Main formulation interface (redesigned, modern implementation)
  - ProductPlaygroundOld: Legacy formulation interface (deprecated)
  - Breadcrumb: Navigation component showing Industry → Category → Subcategory
  - Chat interface for AI-powered formulation assistance

### Backend (formulation-api)
- **Framework**: Node.js with Express
- **Database**: MongoDB with Mongoose ODM
- **Port**: 7070 (development)
- **Authentication**: JWT-based
- **Key Services**:
  - Claude AI integration for formulation generation
  - Job queue system for async processing
  - Project management and data persistence

### Database Schema
- **Projects**: Contains industry, product_type, status, components, etc.
- **Jobs**: Queue system for formulation processing
- **Users**: Authentication and user management
- **Components**: Formulation ingredients and specifications

## Development Ports
- **Frontend**: http://localhost:7700
- **Backend API**: http://localhost:7070
- **MongoDB**: Default MongoDB port (27017)

## Key Development Patterns

### Data Flow
1. Frontend makes API calls to backend endpoints
2. Backend processes requests, often queuing jobs for AI processing
3. Results are stored in MongoDB and returned to frontend
4. Real-time updates via polling or WebSocket connections

### API Endpoints Structure
- `/api/auth/*` - Authentication endpoints
- `/api/projects/*` - Project management
- `/api/playground/*` - Formulation generation and loading
- `/api/jobs/*` - Job queue management

### Frontend Routing
- `/playground/:projectId` - Main playground interface (ProductPlayground component)
- Legacy routes removed: `/playground-re/:projectId`, `/playground-old/:projectId`
- Single route architecture for simplified navigation

### Component Architecture
- **ProductPlayground**: Main container component (redesigned version with modern UI/UX)
  - Features: Enhanced ingredient database, regulatory guidance, quality testing status
  - Architecture: Modular design with reusable components and improved state management
- **ProductPlaygroundOld**: Legacy container component (deprecated)
  - Status: Preserved for reference but not actively used
  - Migration: Renamed from original ProductPlayground component
- **Breadcrumb**: Shows navigation path (Industry → Category → Subcategory)
- **Chat Interface**: AI interaction for formulation requests
- **Results Display**: Shows generated formulations and cost analysis

## Critical Implementation Notes

### Breadcrumb Component
- Only displays when `projectInfo` contains `industry` and `product_type`
- Does NOT use `category` field (not present in Project model)
- Text formatting: capitalizes and replaces underscores with spaces
- Located below "Product Playground" title

### Data Validation
- Backend uses JSON schemas for request validation
- Frontend validates user inputs before API calls
- Error handling includes user-friendly messages

### Job Processing
- Async formulation generation using job queue
- Status tracking: queued → processing → completed/failed
- Worker processes handle Claude AI integration

## Development Workflow

### Starting Development Servers
```bash
# Backend (from formulation-api/)
npm run dev  # Starts on port 7070

# Frontend (from formulation-web/)
npm run dev  # Starts on port 7700

# Job Worker (from formulation-api/)
node scripts/job-worker.js
```

### Environment Setup
- Copy `.env.example` to `.env` in both directories
- Configure MongoDB connection string
- Set up Claude AI API credentials
- Configure JWT secrets

## Code Quality Standards

### Frontend
- Use functional components with hooks
- Implement proper error boundaries
- Follow React best practices for state management
- Use Tailwind for consistent styling

### Backend
- Use async/await for database operations
- Implement proper error handling middleware
- Follow RESTful API conventions
- Use Mongoose schemas for data validation

### Database
- Use proper indexing for performance
- Implement data validation at schema level
- Follow MongoDB naming conventions
- Regular backup procedures

## Security Considerations
- JWT token validation on protected routes
- Input sanitization and validation
- CORS configuration for cross-origin requests
- Environment variables for sensitive data
- Rate limiting on API endpoints

## Testing Strategy
- Unit tests for utility functions
- Integration tests for API endpoints
- Frontend component testing
- End-to-end testing for critical user flows

## Deployment Notes
- Use PM2 for process management in production
- Configure reverse proxy (nginx) for frontend
- Set up MongoDB replica sets for production
- Implement proper logging and monitoring
- Use environment-specific configurations

## Common Issues and Solutions

### Breadcrumb Not Showing
- Ensure `projectInfo` contains `industry` and `category` and 'subcategory'
- Check that project data is loaded from API
- Verify component conditional rendering logic

### Job Processing Failures
- Check Claude AI API credentials and rate limits
- Verify job worker is running
- Monitor job status in database

### CORS Issues
- Configure proper CORS settings in backend
- Ensure frontend and backend ports match configuration
- Check environment-specific CORS origins

## File Structure Conventions
- Components in `/src/components/`
- Pages in `/src/pages/`
  - `ProductPlayground.jsx` - Main playground component (redesigned)
  - `ProductPlaygroundOld.jsx` - Legacy playground component (deprecated)
- Services in `/src/services/`
- API routes in `/src/routes/`
- Database models in `/src/models/`
- Utilities in `/src/utils/`

## Component Migration History

### December 2024 - Playground Component Consolidation
- **Migration Completed**: Consolidated dual playground components into single interface
- **Changes Made**:
  - Renamed `ProductPlayground.jsx` → `ProductPlaygroundOld.jsx` (legacy)
  - Renamed `PlaygroundRedesigned.jsx` → `ProductPlayground.jsx` (primary)
  - Updated component function names and exports to match new file names
  - Simplified routing from 3 routes to 1: `/playground/:projectId`
  - Removed legacy fallback routes for cleaner architecture
- **Rationale**: PlaygroundRedesigned had superior UX, modern architecture, and enhanced features
- **Status**: Migration complete, legacy component preserved for reference

## Performance Optimization
- Implement lazy loading for large components
- Use React.memo for expensive renders
- Optimize database queries with proper indexing
- Implement caching strategies for frequently accessed data
- Monitor and optimize bundle sizes

This document should be updated as the project evolves and new patterns emerge.